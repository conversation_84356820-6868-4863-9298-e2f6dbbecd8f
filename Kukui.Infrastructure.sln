Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{EDA9874D-AD05-46CE-AD31-63198C438901}"
	ProjectSection(SolutionItems) = preProject
		src\Directory.Build.props = src\Directory.Build.props
		src\App.Build.props = src\App.Build.props
		src\AppLibrary.Build.props = src\AppLibrary.Build.props
		src\DisableAnalysisAndPack.Build.props = src\DisableAnalysisAndPack.Build.props
		src\Samples.Build.Props = src\Samples.Build.Props
		src\Web.Build.props = src\Web.Build.props
		src\WebApp.Build.props = src\WebApp.Build.props
		src\Samples.Packages.props = src\Samples.Packages.props
		src\Directory.Packages.Tests.props = src\Directory.Packages.Tests.props
		src\Directory.Packages.props = src\Directory.Packages.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "hosting", "hosting", "{DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}"
	ProjectSection(SolutionItems) = preProject
		src\hosting\README.md = src\hosting\README.md
		src\hosting\CHANGELOG.md = src\hosting\CHANGELOG.md
		src\hosting\src\Hosting.Build.props = src\hosting\src\Hosting.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kukui.Hosting.Common", "src\hosting\src\Kukui.Hosting.Common\Kukui.Hosting.Common.csproj", "{5CA73FDF-3815-42BF-90DA-32EBE24B00B8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kukui.Hosting.Common.Vault", "src\hosting\src\Kukui.Hosting.Common.Vault\Kukui.Hosting.Common.Vault.csproj", "{C4E0EBEB-ECFE-4281-B71D-7248EE0BCBC1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kukui.Hosting.Server", "src\hosting\src\Kukui.Hosting.Server\Kukui.Hosting.Server.csproj", "{0F67852C-BDBA-4A65-B791-3C8844320034}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kukui.Hosting.Web", "src\hosting\src\Kukui.Hosting.Web\Kukui.Hosting.Web.csproj", "{888B33D1-677A-457D-A6F5-0A7769F5D654}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kukui.Hosting.Web.Common", "src\hosting\src\Kukui.Hosting.Web.Common\Kukui.Hosting.Web.Common.csproj", "{056308D9-ABC7-4067-84F0-B2FC8CB962A4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "message-bus", "message-bus", "{EB63F568-CF91-4674-A8FB-0688DCB10450}"
	ProjectSection(SolutionItems) = preProject
		src\message-bus\README.md = src\message-bus\README.md
		src\message-bus\CHANGELOG.md = src\message-bus\CHANGELOG.md
		src\message-bus\src\Directory.Build.props = src\message-bus\src\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MessageBus.Abstractions", "src\message-bus\src\MessageBus.Abstractions\MessageBus.Abstractions.csproj", "{8AB9E215-DBCE-456A-9AF5-D421233A10C7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "database", "database", "{4812C253-1C93-4AFA-85D0-1EADD9776383}"
	ProjectSection(SolutionItems) = preProject
		src\database\README.md = src\database\README.md
		src\database\CHANGELOG.md = src\database\CHANGELOG.md
		src\database\src\Directory.Build.props = src\database\src\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScyllaDb.Hosting.Extensions", "src\database\src\ScyllaDb.Hosting.Extensions\ScyllaDb.Hosting.Extensions.csproj", "{93E43857-3B84-494E-A95C-627BB256A493}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{78073787-31A1-4A50-889A-3E38E24900C1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".samples", ".samples", "{1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".docs", ".docs", "{BB8DC88F-6530-4B81-B7D0-2E91656516E3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "hosting", "hosting", "{DB2F8E6D-FDBA-4E59-9D73-3762D5B29197}"
	ProjectSection(SolutionItems) = preProject
		src\hosting\.samples\Directory.Build.props = src\hosting\.samples\Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "message-bus", "message-bus", "{89F6F52B-E151-4951-A35D-273DE405F102}"
	ProjectSection(SolutionItems) = preProject
		src\message-bus\.samples\Directory.Build.props = src\message-bus\.samples\Directory.Build.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "database", "database", "{66C4378F-B232-46CF-ACEE-FA37F66CC97C}"
	ProjectSection(SolutionItems) = preProject
		src\database\.samples\Directory.Build.props = src\database\.samples\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.Console.ScyllaDb", "src\database\.samples\Sample.Console.ScyllaDb\Sample.Console.ScyllaDb.csproj", "{95F8687D-51A1-435B-A3CC-3448F2813E85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.Console.Nats.JetStream.Consumer", "src\message-bus\.samples\Sample.Console.Nats.JetStream.Consumer\Sample.Console.Nats.JetStream.Consumer.csproj", "{9902681B-2666-4680-AB46-710C0ACE5E06}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.WebApi.Nats.JetStream.Producer", "src\message-bus\.samples\Sample.WebApi.Nats.JetStream.Producer\Sample.WebApi.Nats.JetStream.Producer.csproj", "{C62CAF73-87DF-4379-8545-A812805EA7AD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.Console.Server", "src\hosting\.samples\Sample.Console.Server\Sample.Console.Server.csproj", "{5783F226-2818-45CF-847F-81B33931DACF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.WebApi.Server", "src\hosting\.samples\Sample.WebApi.Server\Sample.WebApi.Server.csproj", "{63BCAC23-D8BF-433D-BA82-73350BBA8BED}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MessageBus.Nats.Hosting.Extensions", "src\message-bus\src\MessageBus.Nats.Hosting.Extensions\MessageBus.Nats.Hosting.Extensions.csproj", "{6E9F4BEB-114F-408B-BBDE-835A166924F3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MessageBus.Nats.JetStream", "src\message-bus\src\MessageBus.Nats.JetStream\MessageBus.Nats.JetStream.csproj", "{EA353A11-7AC8-49F2-9B49-5F20FC920AA4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MessageBus.Nats.RqRs", "src\message-bus\src\MessageBus.Nats.RqRs\MessageBus.Nats.RqRs.csproj", "{B4B0A1E2-BF3B-435B-AE83-E49D35314070}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Nats.Events.Shared", "src\message-bus\.samples\Nats.Events.Shared\Nats.Events.Shared.csproj", "{5A836D87-586C-483B-8FF7-D4CDA2784893}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.WebApi.Nats.RqRs", "src\message-bus\.samples\Sample.WebApi.Nats.RqRs\Sample.WebApi.Nats.RqRs.csproj", "{8917029C-D68F-4C9A-9313-2210CB29AAA4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".docker", ".docker", "{CCC41DE2-6DDF-43F2-892E-88B3F482DDBB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "database", "database", "{734A6B28-C410-4111-98A4-8D711D5EE8D7}"
	ProjectSection(SolutionItems) = preProject
		src\database\.samples\docker-compose.yml = src\database\.samples\docker-compose.yml
		src\database\.samples\docker-compose.cluster.local.yml = src\database\.samples\docker-compose.cluster.local.yml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "message-bus", "message-bus", "{8E3F9B02-445C-41F1-8232-581CD3D239B2}"
	ProjectSection(SolutionItems) = preProject
		src\message-bus\.samples\docker-compose.yml = src\message-bus\.samples\docker-compose.yml
		src\message-bus\.samples\server.conf = src\message-bus\.samples\server.conf
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".templates", ".templates", "{94571347-3B1F-425E-A529-B0780D5C0332}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Template.ServerApp", "src\hosting\.templates\Template.ServerApp\Template.ServerApp.csproj", "{B8F61E98-90AD-46DD-8764-35A813016064}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Template.WebServerApp", "src\hosting\.templates\Template.WebServerApp\Template.WebServerApp.csproj", "{9211DCEA-2783-41C3-8ED5-6E6F42B3B313}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{63F21D58-5B6B-4743-A7AA-647E144A0261}"
	ProjectSection(SolutionItems) = preProject
		src\tests\CHANGELOG.md = src\tests\CHANGELOG.md
		src\tests\README.md = src\tests\README.md
		src\tests\src\Containers.Build.props = src\tests\src\Containers.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.UnitTests", "src\tests\src\Testing.UnitTests\Testing.UnitTests.csproj", "{25E62BCE-99EF-432B-93C5-ACCC63441D17}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScyllaDb.Linq.Extensions", "src\database\src\ScyllaDb.Linq.Extensions\ScyllaDb.Linq.Extensions.csproj", "{5B64641E-C45F-401B-B296-E45964B58D63}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScyllaDb.Mapping.Extensions", "src\database\src\ScyllaDb.Mapping.Extensions\ScyllaDb.Mapping.Extensions.csproj", "{F7E88FF1-86E0-4D99-A8C0-35CA5E08FF6D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScyllaDb.Abstractions", "src\database\src\ScyllaDb.Abstractions\ScyllaDb.Abstractions.csproj", "{DB84E099-2B15-434E-BD85-B3FC8280224F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MessageBus.Nats.KeyValue", "src\message-bus\src\MessageBus.Nats.KeyValue\MessageBus.Nats.KeyValue.csproj", "{63ABA175-988A-4B77-AA96-55C6614C9E2A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "outbox", "outbox", "{670883C4-C5DB-44E4-979F-FF373A470D4E}"
	ProjectSection(SolutionItems) = preProject
		src\outbox\src\Directory.Build.props = src\outbox\src\Directory.Build.props
		src\outbox\README.md = src\outbox\README.md
		src\outbox\CHANGELOG.md = src\outbox\CHANGELOG.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "outbox", "outbox", "{2D5BF9DC-422C-4CB7-8E6D-E2020E5824DE}"
	ProjectSection(SolutionItems) = preProject
		src\outbox\.samples\Directory.Build.props = src\outbox\.samples\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.Outbox.Server", "src\outbox\.samples\Sample.Outbox.Server\Sample.Outbox.Server.csproj", "{3B74BE7A-0458-4539-9952-EB2CD91C61FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.Outbox.Events", "src\outbox\.samples\Sample.Outbox.Events\Sample.Outbox.Events.csproj", "{58260D08-7F47-4D0A-9A8D-E6DF1DE42615}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Outbox.Server.ScyllaDb", "src\outbox\src\Outbox.Server.ScyllaDb\Outbox.Server.ScyllaDb.csproj", "{F7F906A3-584C-4CF5-B220-CC71654263E6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.Containers.WireMock", "src\tests\src\Testing.Containers.WireMock\Testing.Containers.WireMock.csproj", "{2D32848B-F5C3-4FB2-B9AF-66CAD7308AAF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.Containers.Database", "src\tests\src\Testing.Containers.Database\Testing.Containers.Database.csproj", "{F9D71F2A-F7E9-4718-8137-B9AEEDF106B3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.Containers.Database.ScyllaDb", "src\tests\src\Testing.Containers.Database.ScyllaDb\Testing.Containers.Database.ScyllaDb.csproj", "{AF4C5C5D-6C8B-47A6-83B4-84F8A37C8D48}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.Containers.Redis", "src\tests\src\Testing.Containers.Redis\Testing.Containers.Redis.csproj", "{94C49B13-8B28-4E28-85DE-7C6BDB489AD9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.Containers.Keycloak", "src\tests\src\Testing.Containers.Keycloak\Testing.Containers.Keycloak.csproj", "{049C2476-F911-4DF5-B424-4E7E59AF0709}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.Containers.Abstractions", "src\tests\src\Testing.Containers.Abstractions\Testing.Containers.Abstractions.csproj", "{610169A8-CE07-45EC-B2BA-71262C77BC7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.Containers.MessageBus", "src\tests\src\Testing.Containers.MessageBus\Testing.Containers.MessageBus.csproj", "{1679D778-7E4A-4BF4-B0F8-D95559817222}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{42F48DC9-1CF9-4E31-84C2-ED58D69B1392}"
	ProjectSection(SolutionItems) = preProject
		src\tests\.samples\Directory.Build.props = src\tests\.samples\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebApi", "src\tests\.samples\WebApi\WebApi.csproj", "{A6374F90-9E8F-437D-9020-1DF6A75C0769}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "xUnit.AssemblyFixture", "src\tests\src\xUnit.AssemblyFixture\xUnit.AssemblyFixture.csproj", "{EA7DBA4C-4F95-4DDD-B98B-477B8973D13D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebApi.IntegrationTests", "src\tests\.samples\WebApi.IntegrationTests\WebApi.IntegrationTests.csproj", "{B0F798E1-FFEA-46AE-BD64-63325D62E7BF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Testing.IntegrationTests", "src\tests\src\Testing.IntegrationTests\Testing.IntegrationTests.csproj", "{1DCC37B6-6731-4076-A293-B2B14ABAA752}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebApi.ScyllaDb", "src\tests\.samples\WebApi.ScyllaDb\WebApi.ScyllaDb.csproj", "{B27D866A-13E7-4496-A378-D7BCAF44B935}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WebApi.IntegrationTests.ScyllaDb", "src\tests\.samples\WebApi.IntegrationTests.ScyllaDb\WebApi.IntegrationTests.ScyllaDb.csproj", "{A57A7DBC-AF81-449E-AAA2-C468B2913556}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Outbox.Server.ScyllaDb.Abstractions", "src\outbox\src\Outbox.Server.ScyllaDb.Abstractions\Outbox.Server.ScyllaDb.Abstractions.csproj", "{401C1260-1874-4F13-BAD1-2E429F34D9DF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Outbox.Server.Serialization", "src\outbox\src\Outbox.Server.Serialization\Outbox.Server.Serialization.csproj", "{C4C3D9C7-7C35-4CA4-87B7-439A2F5D2D58}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kukui.Hosting.Web.MinimalApi", "src\hosting\src\Kukui.Hosting.Web.MinimalApi\Kukui.Hosting.Web.MinimalApi.csproj", "{5AA58587-7B68-4AE6-9BF5-BF89771562BC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.MinimalApi", "src\hosting\.samples\Sample.MinimalApi\Sample.MinimalApi.csproj", "{0309CFFF-BA10-4BF2-893B-FEEBD0288943}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScyllaDb.Repository", "src\database\src\ScyllaDb.Repository\ScyllaDb.Repository.csproj", "{D48E22F2-F1B8-4255-8771-2352077B169D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ScyllaDb.Repository.IntegrationTests", "src\database\tests\ScyllaDb.Repository.IntegrationTests\ScyllaDb.Repository.IntegrationTests.csproj", "{8CC03043-4BAD-4AD5-BE16-462E4BB89B15}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Outbox.Server.PostgresSql", "src\outbox\src\Outbox.Server.PostgresSql\Outbox.Server.PostgresSql.csproj", "{B466D70A-8DE1-443F-AC12-D68C2B48D4C6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sample.Outbox.Server.Postgres.WebApi", "src\outbox\.samples\Sample.Outbox.Server.Postgres.WebApi\Sample.Outbox.Server.Postgres.WebApi.csproj", "{738EDA87-5642-49CA-98C5-916A4C950AE1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Outbox.Server.Relational", "src\outbox\src\Outbox.Server.Relational\Outbox.Server.Relational.csproj", "{9E104B11-20DF-46DA-89AB-259568E08552}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Outbox.Server.SqlServer", "src\outbox\src\Outbox.Server.SqlServer\Outbox.Server.SqlServer.csproj", "{99307D2B-27E6-4C32-82B5-08A86EE55EF1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Outbox.Server.Abstractions", "src\outbox\src\Outbox.Server.Abstractions\Outbox.Server.Abstractions.csproj", "{A7B4544D-BFD3-4CE3-9DD7-5D129413F5BB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Server.App.IntegrationTests", "src\tests\.samples\Server.App.IntegrationTests\Server.App.IntegrationTests.csproj", "{BDD4F6B9-83A5-4A8E-B41C-33421DD65992}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Server.Application", "src\tests\.samples\Server.Application\Server.Application.csproj", "{ADDB75B7-5BEA-4056-A831-A732295D6E54}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "http-clients", "http-clients", "{9D0B7F9C-ACFD-4C9C-A323-A119AD77BC61}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "http-clients", "http-clients", "{EBC08947-2D44-4345-AE14-537525E5FF1D}"
	ProjectSection(SolutionItems) = preProject
		src\authentication\.samples\Directory.Build.props = src\http-clients\.samples\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Authentication.Keycloak", "src\http-clients\src\Authentication.Keycloak\Authentication.Keycloak.csproj", "{A927D714-9E9B-4BD2-918C-0FDF26215A49}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Authentication.Keycloak.WebApi", "src\http-clients\.samples\Authentication.Keycloak.WebApi\Authentication.Keycloak.WebApi.csproj", "{D4331A2B-E26B-4788-AF1D-4B7B1BF851C8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "pipelines", "pipelines", "{3E01F9DA-7363-4152-B244-3EC8C24BF7B6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "pipelines", "pipelines", "{CDA315F6-7798-4483-99A2-08A77A1433E4}"
	ProjectSection(SolutionItems) = preProject
		src\pipelines\.samples\Directory.Build.props = src\pipelines\.samples\Directory.Build.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Console.Server.Pipelines", "src\pipelines\.samples\Console.Server.Pipelines\Console.Server.Pipelines.csproj", "{240E246C-EC99-4AF1-BF41-8065F4251A83}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Pipeline.Processing", "src\pipelines\src\Pipeline.Processing\Pipeline.Processing.csproj", "{85B06483-A4BE-4A56-A75A-9B826BB33EF7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Kukui.Hosting.Serilog", "src\hosting\src\Kukui.Hosting.Serilog\Kukui.Hosting.Serilog.csproj", "{4076EB7C-CFC0-4577-9C60-0C2B3D19F571}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "health-checks", "health-checks", "{F3DF3618-B80E-479A-B05D-C001BFFA1A2A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthChecks.Nats", "src\healthchecks\src\HealthChecks.Nats\HealthChecks.Nats.csproj", "{FB4804C0-7018-469A-9D66-9BDEE9943BF8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthChecks.ScyllaDb", "src\healthchecks\src\HealthChecks.ScyllaDb\HealthChecks.ScyllaDb.csproj", "{07235FE6-EE99-4CD2-9698-B61769C8DBC9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "HealthChecks.MassTransit.RabbitMq", "src\healthchecks\src\HealthChecks.MassTransit.RabbitMq\HealthChecks.MassTransit.RabbitMq.csproj", "{04C1FAEF-3E28-4939-A2F5-594361D0D5F7}"
EndProject

Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F20DFCE9-83E9-430A-A4CB-12BA5AF50D53}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F20DFCE9-83E9-430A-A4CB-12BA5AF50D53}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F20DFCE9-83E9-430A-A4CB-12BA5AF50D53}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F20DFCE9-83E9-430A-A4CB-12BA5AF50D53}.Release|Any CPU.Build.0 = Release|Any CPU
		{5CA73FDF-3815-42BF-90DA-32EBE24B00B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5CA73FDF-3815-42BF-90DA-32EBE24B00B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5CA73FDF-3815-42BF-90DA-32EBE24B00B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5CA73FDF-3815-42BF-90DA-32EBE24B00B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4E0EBEB-ECFE-4281-B71D-7248EE0BCBC1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4E0EBEB-ECFE-4281-B71D-7248EE0BCBC1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4E0EBEB-ECFE-4281-B71D-7248EE0BCBC1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4E0EBEB-ECFE-4281-B71D-7248EE0BCBC1}.Release|Any CPU.Build.0 = Release|Any CPU
		{0F67852C-BDBA-4A65-B791-3C8844320034}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F67852C-BDBA-4A65-B791-3C8844320034}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F67852C-BDBA-4A65-B791-3C8844320034}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F67852C-BDBA-4A65-B791-3C8844320034}.Release|Any CPU.Build.0 = Release|Any CPU
		{888B33D1-677A-457D-A6F5-0A7769F5D654}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{888B33D1-677A-457D-A6F5-0A7769F5D654}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{888B33D1-677A-457D-A6F5-0A7769F5D654}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{888B33D1-677A-457D-A6F5-0A7769F5D654}.Release|Any CPU.Build.0 = Release|Any CPU
		{056308D9-ABC7-4067-84F0-B2FC8CB962A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{056308D9-ABC7-4067-84F0-B2FC8CB962A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{056308D9-ABC7-4067-84F0-B2FC8CB962A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{056308D9-ABC7-4067-84F0-B2FC8CB962A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{8AB9E215-DBCE-456A-9AF5-D421233A10C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8AB9E215-DBCE-456A-9AF5-D421233A10C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8AB9E215-DBCE-456A-9AF5-D421233A10C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8AB9E215-DBCE-456A-9AF5-D421233A10C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{93E43857-3B84-494E-A95C-627BB256A493}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93E43857-3B84-494E-A95C-627BB256A493}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93E43857-3B84-494E-A95C-627BB256A493}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93E43857-3B84-494E-A95C-627BB256A493}.Release|Any CPU.Build.0 = Release|Any CPU
		{95F8687D-51A1-435B-A3CC-3448F2813E85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95F8687D-51A1-435B-A3CC-3448F2813E85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95F8687D-51A1-435B-A3CC-3448F2813E85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95F8687D-51A1-435B-A3CC-3448F2813E85}.Release|Any CPU.Build.0 = Release|Any CPU
		{9902681B-2666-4680-AB46-710C0ACE5E06}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9902681B-2666-4680-AB46-710C0ACE5E06}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9902681B-2666-4680-AB46-710C0ACE5E06}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9902681B-2666-4680-AB46-710C0ACE5E06}.Release|Any CPU.Build.0 = Release|Any CPU
		{C62CAF73-87DF-4379-8545-A812805EA7AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C62CAF73-87DF-4379-8545-A812805EA7AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C62CAF73-87DF-4379-8545-A812805EA7AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C62CAF73-87DF-4379-8545-A812805EA7AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{5783F226-2818-45CF-847F-81B33931DACF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5783F226-2818-45CF-847F-81B33931DACF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5783F226-2818-45CF-847F-81B33931DACF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5783F226-2818-45CF-847F-81B33931DACF}.Release|Any CPU.Build.0 = Release|Any CPU
		{63BCAC23-D8BF-433D-BA82-73350BBA8BED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63BCAC23-D8BF-433D-BA82-73350BBA8BED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63BCAC23-D8BF-433D-BA82-73350BBA8BED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63BCAC23-D8BF-433D-BA82-73350BBA8BED}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E9F4BEB-114F-408B-BBDE-835A166924F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E9F4BEB-114F-408B-BBDE-835A166924F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E9F4BEB-114F-408B-BBDE-835A166924F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E9F4BEB-114F-408B-BBDE-835A166924F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA353A11-7AC8-49F2-9B49-5F20FC920AA4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA353A11-7AC8-49F2-9B49-5F20FC920AA4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA353A11-7AC8-49F2-9B49-5F20FC920AA4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA353A11-7AC8-49F2-9B49-5F20FC920AA4}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4B0A1E2-BF3B-435B-AE83-E49D35314070}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4B0A1E2-BF3B-435B-AE83-E49D35314070}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4B0A1E2-BF3B-435B-AE83-E49D35314070}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4B0A1E2-BF3B-435B-AE83-E49D35314070}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A836D87-586C-483B-8FF7-D4CDA2784893}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A836D87-586C-483B-8FF7-D4CDA2784893}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A836D87-586C-483B-8FF7-D4CDA2784893}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A836D87-586C-483B-8FF7-D4CDA2784893}.Release|Any CPU.Build.0 = Release|Any CPU
		{8917029C-D68F-4C9A-9313-2210CB29AAA4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8917029C-D68F-4C9A-9313-2210CB29AAA4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8917029C-D68F-4C9A-9313-2210CB29AAA4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8917029C-D68F-4C9A-9313-2210CB29AAA4}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8F61E98-90AD-46DD-8764-35A813016064}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8F61E98-90AD-46DD-8764-35A813016064}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8F61E98-90AD-46DD-8764-35A813016064}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8F61E98-90AD-46DD-8764-35A813016064}.Release|Any CPU.Build.0 = Release|Any CPU
		{9211DCEA-2783-41C3-8ED5-6E6F42B3B313}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9211DCEA-2783-41C3-8ED5-6E6F42B3B313}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9211DCEA-2783-41C3-8ED5-6E6F42B3B313}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9211DCEA-2783-41C3-8ED5-6E6F42B3B313}.Release|Any CPU.Build.0 = Release|Any CPU
		{25E62BCE-99EF-432B-93C5-ACCC63441D17}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25E62BCE-99EF-432B-93C5-ACCC63441D17}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25E62BCE-99EF-432B-93C5-ACCC63441D17}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25E62BCE-99EF-432B-93C5-ACCC63441D17}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B64641E-C45F-401B-B296-E45964B58D63}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B64641E-C45F-401B-B296-E45964B58D63}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B64641E-C45F-401B-B296-E45964B58D63}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B64641E-C45F-401B-B296-E45964B58D63}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7E88FF1-86E0-4D99-A8C0-35CA5E08FF6D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7E88FF1-86E0-4D99-A8C0-35CA5E08FF6D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7E88FF1-86E0-4D99-A8C0-35CA5E08FF6D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7E88FF1-86E0-4D99-A8C0-35CA5E08FF6D}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB84E099-2B15-434E-BD85-B3FC8280224F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB84E099-2B15-434E-BD85-B3FC8280224F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB84E099-2B15-434E-BD85-B3FC8280224F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB84E099-2B15-434E-BD85-B3FC8280224F}.Release|Any CPU.Build.0 = Release|Any CPU
		{63ABA175-988A-4B77-AA96-55C6614C9E2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{63ABA175-988A-4B77-AA96-55C6614C9E2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{63ABA175-988A-4B77-AA96-55C6614C9E2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{63ABA175-988A-4B77-AA96-55C6614C9E2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B74BE7A-0458-4539-9952-EB2CD91C61FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B74BE7A-0458-4539-9952-EB2CD91C61FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B74BE7A-0458-4539-9952-EB2CD91C61FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B74BE7A-0458-4539-9952-EB2CD91C61FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{58260D08-7F47-4D0A-9A8D-E6DF1DE42615}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58260D08-7F47-4D0A-9A8D-E6DF1DE42615}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58260D08-7F47-4D0A-9A8D-E6DF1DE42615}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58260D08-7F47-4D0A-9A8D-E6DF1DE42615}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7F906A3-584C-4CF5-B220-CC71654263E6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7F906A3-584C-4CF5-B220-CC71654263E6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7F906A3-584C-4CF5-B220-CC71654263E6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7F906A3-584C-4CF5-B220-CC71654263E6}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D32848B-F5C3-4FB2-B9AF-66CAD7308AAF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D32848B-F5C3-4FB2-B9AF-66CAD7308AAF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D32848B-F5C3-4FB2-B9AF-66CAD7308AAF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D32848B-F5C3-4FB2-B9AF-66CAD7308AAF}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9D71F2A-F7E9-4718-8137-B9AEEDF106B3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9D71F2A-F7E9-4718-8137-B9AEEDF106B3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9D71F2A-F7E9-4718-8137-B9AEEDF106B3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9D71F2A-F7E9-4718-8137-B9AEEDF106B3}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF4C5C5D-6C8B-47A6-83B4-84F8A37C8D48}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF4C5C5D-6C8B-47A6-83B4-84F8A37C8D48}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF4C5C5D-6C8B-47A6-83B4-84F8A37C8D48}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF4C5C5D-6C8B-47A6-83B4-84F8A37C8D48}.Release|Any CPU.Build.0 = Release|Any CPU
		{94C49B13-8B28-4E28-85DE-7C6BDB489AD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94C49B13-8B28-4E28-85DE-7C6BDB489AD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94C49B13-8B28-4E28-85DE-7C6BDB489AD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94C49B13-8B28-4E28-85DE-7C6BDB489AD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{049C2476-F911-4DF5-B424-4E7E59AF0709}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{049C2476-F911-4DF5-B424-4E7E59AF0709}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{049C2476-F911-4DF5-B424-4E7E59AF0709}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{049C2476-F911-4DF5-B424-4E7E59AF0709}.Release|Any CPU.Build.0 = Release|Any CPU
		{610169A8-CE07-45EC-B2BA-71262C77BC7E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{610169A8-CE07-45EC-B2BA-71262C77BC7E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{610169A8-CE07-45EC-B2BA-71262C77BC7E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{610169A8-CE07-45EC-B2BA-71262C77BC7E}.Release|Any CPU.Build.0 = Release|Any CPU
		{1679D778-7E4A-4BF4-B0F8-D95559817222}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1679D778-7E4A-4BF4-B0F8-D95559817222}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1679D778-7E4A-4BF4-B0F8-D95559817222}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1679D778-7E4A-4BF4-B0F8-D95559817222}.Release|Any CPU.Build.0 = Release|Any CPU
		{A6374F90-9E8F-437D-9020-1DF6A75C0769}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A6374F90-9E8F-437D-9020-1DF6A75C0769}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A6374F90-9E8F-437D-9020-1DF6A75C0769}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A6374F90-9E8F-437D-9020-1DF6A75C0769}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA7DBA4C-4F95-4DDD-B98B-477B8973D13D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA7DBA4C-4F95-4DDD-B98B-477B8973D13D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA7DBA4C-4F95-4DDD-B98B-477B8973D13D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA7DBA4C-4F95-4DDD-B98B-477B8973D13D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0F798E1-FFEA-46AE-BD64-63325D62E7BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0F798E1-FFEA-46AE-BD64-63325D62E7BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0F798E1-FFEA-46AE-BD64-63325D62E7BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0F798E1-FFEA-46AE-BD64-63325D62E7BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{1DCC37B6-6731-4076-A293-B2B14ABAA752}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DCC37B6-6731-4076-A293-B2B14ABAA752}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DCC37B6-6731-4076-A293-B2B14ABAA752}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DCC37B6-6731-4076-A293-B2B14ABAA752}.Release|Any CPU.Build.0 = Release|Any CPU
		{B27D866A-13E7-4496-A378-D7BCAF44B935}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B27D866A-13E7-4496-A378-D7BCAF44B935}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B27D866A-13E7-4496-A378-D7BCAF44B935}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B27D866A-13E7-4496-A378-D7BCAF44B935}.Release|Any CPU.Build.0 = Release|Any CPU
		{A57A7DBC-AF81-449E-AAA2-C468B2913556}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A57A7DBC-AF81-449E-AAA2-C468B2913556}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A57A7DBC-AF81-449E-AAA2-C468B2913556}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A57A7DBC-AF81-449E-AAA2-C468B2913556}.Release|Any CPU.Build.0 = Release|Any CPU
		{401C1260-1874-4F13-BAD1-2E429F34D9DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{401C1260-1874-4F13-BAD1-2E429F34D9DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{401C1260-1874-4F13-BAD1-2E429F34D9DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{401C1260-1874-4F13-BAD1-2E429F34D9DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4C3D9C7-7C35-4CA4-87B7-439A2F5D2D58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4C3D9C7-7C35-4CA4-87B7-439A2F5D2D58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4C3D9C7-7C35-4CA4-87B7-439A2F5D2D58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4C3D9C7-7C35-4CA4-87B7-439A2F5D2D58}.Release|Any CPU.Build.0 = Release|Any CPU
		{5AA58587-7B68-4AE6-9BF5-BF89771562BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5AA58587-7B68-4AE6-9BF5-BF89771562BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5AA58587-7B68-4AE6-9BF5-BF89771562BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5AA58587-7B68-4AE6-9BF5-BF89771562BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{0309CFFF-BA10-4BF2-893B-FEEBD0288943}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0309CFFF-BA10-4BF2-893B-FEEBD0288943}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0309CFFF-BA10-4BF2-893B-FEEBD0288943}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0309CFFF-BA10-4BF2-893B-FEEBD0288943}.Release|Any CPU.Build.0 = Release|Any CPU
		{D48E22F2-F1B8-4255-8771-2352077B169D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D48E22F2-F1B8-4255-8771-2352077B169D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D48E22F2-F1B8-4255-8771-2352077B169D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D48E22F2-F1B8-4255-8771-2352077B169D}.Release|Any CPU.Build.0 = Release|Any CPU
		{8CC03043-4BAD-4AD5-BE16-462E4BB89B15}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CC03043-4BAD-4AD5-BE16-462E4BB89B15}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CC03043-4BAD-4AD5-BE16-462E4BB89B15}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CC03043-4BAD-4AD5-BE16-462E4BB89B15}.Release|Any CPU.Build.0 = Release|Any CPU
		{B466D70A-8DE1-443F-AC12-D68C2B48D4C6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B466D70A-8DE1-443F-AC12-D68C2B48D4C6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B466D70A-8DE1-443F-AC12-D68C2B48D4C6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B466D70A-8DE1-443F-AC12-D68C2B48D4C6}.Release|Any CPU.Build.0 = Release|Any CPU
		{738EDA87-5642-49CA-98C5-916A4C950AE1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{738EDA87-5642-49CA-98C5-916A4C950AE1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{738EDA87-5642-49CA-98C5-916A4C950AE1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{738EDA87-5642-49CA-98C5-916A4C950AE1}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E104B11-20DF-46DA-89AB-259568E08552}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E104B11-20DF-46DA-89AB-259568E08552}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E104B11-20DF-46DA-89AB-259568E08552}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E104B11-20DF-46DA-89AB-259568E08552}.Release|Any CPU.Build.0 = Release|Any CPU
		{99307D2B-27E6-4C32-82B5-08A86EE55EF1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{99307D2B-27E6-4C32-82B5-08A86EE55EF1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{99307D2B-27E6-4C32-82B5-08A86EE55EF1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{99307D2B-27E6-4C32-82B5-08A86EE55EF1}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7B4544D-BFD3-4CE3-9DD7-5D129413F5BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7B4544D-BFD3-4CE3-9DD7-5D129413F5BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7B4544D-BFD3-4CE3-9DD7-5D129413F5BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7B4544D-BFD3-4CE3-9DD7-5D129413F5BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{BDD4F6B9-83A5-4A8E-B41C-33421DD65992}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BDD4F6B9-83A5-4A8E-B41C-33421DD65992}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BDD4F6B9-83A5-4A8E-B41C-33421DD65992}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BDD4F6B9-83A5-4A8E-B41C-33421DD65992}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADDB75B7-5BEA-4056-A831-A732295D6E54}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADDB75B7-5BEA-4056-A831-A732295D6E54}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADDB75B7-5BEA-4056-A831-A732295D6E54}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADDB75B7-5BEA-4056-A831-A732295D6E54}.Release|Any CPU.Build.0 = Release|Any CPU
		{A927D714-9E9B-4BD2-918C-0FDF26215A49}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A927D714-9E9B-4BD2-918C-0FDF26215A49}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A927D714-9E9B-4BD2-918C-0FDF26215A49}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A927D714-9E9B-4BD2-918C-0FDF26215A49}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4331A2B-E26B-4788-AF1D-4B7B1BF851C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4331A2B-E26B-4788-AF1D-4B7B1BF851C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4331A2B-E26B-4788-AF1D-4B7B1BF851C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4331A2B-E26B-4788-AF1D-4B7B1BF851C8}.Release|Any CPU.Build.0 = Release|Any CPU
		{240E246C-EC99-4AF1-BF41-8065F4251A83}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{240E246C-EC99-4AF1-BF41-8065F4251A83}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{240E246C-EC99-4AF1-BF41-8065F4251A83}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{240E246C-EC99-4AF1-BF41-8065F4251A83}.Release|Any CPU.Build.0 = Release|Any CPU
		{85B06483-A4BE-4A56-A75A-9B826BB33EF7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{85B06483-A4BE-4A56-A75A-9B826BB33EF7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{85B06483-A4BE-4A56-A75A-9B826BB33EF7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{85B06483-A4BE-4A56-A75A-9B826BB33EF7}.Release|Any CPU.Build.0 = Release|Any CPU
		{4076EB7C-CFC0-4577-9C60-0C2B3D19F571}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4076EB7C-CFC0-4577-9C60-0C2B3D19F571}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4076EB7C-CFC0-4577-9C60-0C2B3D19F571}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4076EB7C-CFC0-4577-9C60-0C2B3D19F571}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB4804C0-7018-469A-9D66-9BDEE9943BF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB4804C0-7018-469A-9D66-9BDEE9943BF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB4804C0-7018-469A-9D66-9BDEE9943BF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB4804C0-7018-469A-9D66-9BDEE9943BF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{07235FE6-EE99-4CD2-9698-B61769C8DBC9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{07235FE6-EE99-4CD2-9698-B61769C8DBC9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{07235FE6-EE99-4CD2-9698-B61769C8DBC9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{07235FE6-EE99-4CD2-9698-B61769C8DBC9}.Release|Any CPU.Build.0 = Release|Any CPU
		{04C1FAEF-3E28-4939-A2F5-594361D0D5F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04C1FAEF-3E28-4939-A2F5-594361D0D5F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04C1FAEF-3E28-4939-A2F5-594361D0D5F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04C1FAEF-3E28-4939-A2F5-594361D0D5F7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{EB63F568-CF91-4674-A8FB-0688DCB10450} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{DEF11438-0CE5-45AC-94C9-4FC3BC4356C4} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{4812C253-1C93-4AFA-85D0-1EADD9776383} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{5CA73FDF-3815-42BF-90DA-32EBE24B00B8} = {DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}
		{C4E0EBEB-ECFE-4281-B71D-7248EE0BCBC1} = {DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}
		{0F67852C-BDBA-4A65-B791-3C8844320034} = {DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}
		{888B33D1-677A-457D-A6F5-0A7769F5D654} = {DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}
		{056308D9-ABC7-4067-84F0-B2FC8CB962A4} = {DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}
		{DB2F8E6D-FDBA-4E59-9D73-3762D5B29197} = {1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}
		{8AB9E215-DBCE-456A-9AF5-D421233A10C7} = {EB63F568-CF91-4674-A8FB-0688DCB10450}
		{89F6F52B-E151-4951-A35D-273DE405F102} = {1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}
		{93E43857-3B84-494E-A95C-627BB256A493} = {4812C253-1C93-4AFA-85D0-1EADD9776383}
		{66C4378F-B232-46CF-ACEE-FA37F66CC97C} = {1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}
		{95F8687D-51A1-435B-A3CC-3448F2813E85} = {66C4378F-B232-46CF-ACEE-FA37F66CC97C}
		{9902681B-2666-4680-AB46-710C0ACE5E06} = {89F6F52B-E151-4951-A35D-273DE405F102}
		{C62CAF73-87DF-4379-8545-A812805EA7AD} = {89F6F52B-E151-4951-A35D-273DE405F102}
		{5783F226-2818-45CF-847F-81B33931DACF} = {DB2F8E6D-FDBA-4E59-9D73-3762D5B29197}
		{63BCAC23-D8BF-433D-BA82-73350BBA8BED} = {DB2F8E6D-FDBA-4E59-9D73-3762D5B29197}
		{6E9F4BEB-114F-408B-BBDE-835A166924F3} = {EB63F568-CF91-4674-A8FB-0688DCB10450}
		{EA353A11-7AC8-49F2-9B49-5F20FC920AA4} = {EB63F568-CF91-4674-A8FB-0688DCB10450}
		{B4B0A1E2-BF3B-435B-AE83-E49D35314070} = {EB63F568-CF91-4674-A8FB-0688DCB10450}
		{5A836D87-586C-483B-8FF7-D4CDA2784893} = {89F6F52B-E151-4951-A35D-273DE405F102}
		{8917029C-D68F-4C9A-9313-2210CB29AAA4} = {89F6F52B-E151-4951-A35D-273DE405F102}
		{734A6B28-C410-4111-98A4-8D711D5EE8D7} = {CCC41DE2-6DDF-43F2-892E-88B3F482DDBB}
		{8E3F9B02-445C-41F1-8232-581CD3D239B2} = {CCC41DE2-6DDF-43F2-892E-88B3F482DDBB}
		{B8F61E98-90AD-46DD-8764-35A813016064} = {94571347-3B1F-425E-A529-B0780D5C0332}
		{9211DCEA-2783-41C3-8ED5-6E6F42B3B313} = {94571347-3B1F-425E-A529-B0780D5C0332}
		{63F21D58-5B6B-4743-A7AA-647E144A0261} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{25E62BCE-99EF-432B-93C5-ACCC63441D17} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{5B64641E-C45F-401B-B296-E45964B58D63} = {4812C253-1C93-4AFA-85D0-1EADD9776383}
		{F7E88FF1-86E0-4D99-A8C0-35CA5E08FF6D} = {4812C253-1C93-4AFA-85D0-1EADD9776383}
		{DB84E099-2B15-434E-BD85-B3FC8280224F} = {4812C253-1C93-4AFA-85D0-1EADD9776383}
		{63ABA175-988A-4B77-AA96-55C6614C9E2A} = {EB63F568-CF91-4674-A8FB-0688DCB10450}
		{670883C4-C5DB-44E4-979F-FF373A470D4E} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{2D5BF9DC-422C-4CB7-8E6D-E2020E5824DE} = {1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}
		{3B74BE7A-0458-4539-9952-EB2CD91C61FF} = {2D5BF9DC-422C-4CB7-8E6D-E2020E5824DE}
		{58260D08-7F47-4D0A-9A8D-E6DF1DE42615} = {2D5BF9DC-422C-4CB7-8E6D-E2020E5824DE}
		{F7F906A3-584C-4CF5-B220-CC71654263E6} = {670883C4-C5DB-44E4-979F-FF373A470D4E}
		{2D32848B-F5C3-4FB2-B9AF-66CAD7308AAF} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{F9D71F2A-F7E9-4718-8137-B9AEEDF106B3} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{AF4C5C5D-6C8B-47A6-83B4-84F8A37C8D48} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{94C49B13-8B28-4E28-85DE-7C6BDB489AD9} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{049C2476-F911-4DF5-B424-4E7E59AF0709} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{610169A8-CE07-45EC-B2BA-71262C77BC7E} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{1679D778-7E4A-4BF4-B0F8-D95559817222} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{42F48DC9-1CF9-4E31-84C2-ED58D69B1392} = {1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}
		{A6374F90-9E8F-437D-9020-1DF6A75C0769} = {42F48DC9-1CF9-4E31-84C2-ED58D69B1392}
		{EA7DBA4C-4F95-4DDD-B98B-477B8973D13D} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{B0F798E1-FFEA-46AE-BD64-63325D62E7BF} = {42F48DC9-1CF9-4E31-84C2-ED58D69B1392}
		{1DCC37B6-6731-4076-A293-B2B14ABAA752} = {63F21D58-5B6B-4743-A7AA-647E144A0261}
		{B27D866A-13E7-4496-A378-D7BCAF44B935} = {42F48DC9-1CF9-4E31-84C2-ED58D69B1392}
		{A57A7DBC-AF81-449E-AAA2-C468B2913556} = {42F48DC9-1CF9-4E31-84C2-ED58D69B1392}
		{401C1260-1874-4F13-BAD1-2E429F34D9DF} = {670883C4-C5DB-44E4-979F-FF373A470D4E}
		{C4C3D9C7-7C35-4CA4-87B7-439A2F5D2D58} = {670883C4-C5DB-44E4-979F-FF373A470D4E}
		{5AA58587-7B68-4AE6-9BF5-BF89771562BC} = {DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}
		{0309CFFF-BA10-4BF2-893B-FEEBD0288943} = {DB2F8E6D-FDBA-4E59-9D73-3762D5B29197}
		{D48E22F2-F1B8-4255-8771-2352077B169D} = {4812C253-1C93-4AFA-85D0-1EADD9776383}
		{8CC03043-4BAD-4AD5-BE16-462E4BB89B15} = {78073787-31A1-4A50-889A-3E38E24900C1}
		{B466D70A-8DE1-443F-AC12-D68C2B48D4C6} = {670883C4-C5DB-44E4-979F-FF373A470D4E}
		{738EDA87-5642-49CA-98C5-916A4C950AE1} = {2D5BF9DC-422C-4CB7-8E6D-E2020E5824DE}
		{9E104B11-20DF-46DA-89AB-259568E08552} = {670883C4-C5DB-44E4-979F-FF373A470D4E}
		{99307D2B-27E6-4C32-82B5-08A86EE55EF1} = {670883C4-C5DB-44E4-979F-FF373A470D4E}
		{A7B4544D-BFD3-4CE3-9DD7-5D129413F5BB} = {670883C4-C5DB-44E4-979F-FF373A470D4E}
		{BDD4F6B9-83A5-4A8E-B41C-33421DD65992} = {42F48DC9-1CF9-4E31-84C2-ED58D69B1392}
		{ADDB75B7-5BEA-4056-A831-A732295D6E54} = {42F48DC9-1CF9-4E31-84C2-ED58D69B1392}
		{9D0B7F9C-ACFD-4C9C-A323-A119AD77BC61} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{EBC08947-2D44-4345-AE14-537525E5FF1D} = {1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}
		{A927D714-9E9B-4BD2-918C-0FDF26215A49} = {9D0B7F9C-ACFD-4C9C-A323-A119AD77BC61}
		{D4331A2B-E26B-4788-AF1D-4B7B1BF851C8} = {EBC08947-2D44-4345-AE14-537525E5FF1D}
		{3E01F9DA-7363-4152-B244-3EC8C24BF7B6} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{CDA315F6-7798-4483-99A2-08A77A1433E4} = {1F9DD4F4-367C-4B67-BDB8-AFDA2779153C}
		{240E246C-EC99-4AF1-BF41-8065F4251A83} = {CDA315F6-7798-4483-99A2-08A77A1433E4}
		{85B06483-A4BE-4A56-A75A-9B826BB33EF7} = {3E01F9DA-7363-4152-B244-3EC8C24BF7B6}
		{4076EB7C-CFC0-4577-9C60-0C2B3D19F571} = {DEF11438-0CE5-45AC-94C9-4FC3BC4356C4}
		{F3DF3618-B80E-479A-B05D-C001BFFA1A2A} = {EDA9874D-AD05-46CE-AD31-63198C438901}
		{FB4804C0-7018-469A-9D66-9BDEE9943BF8} = {F3DF3618-B80E-479A-B05D-C001BFFA1A2A}
		{07235FE6-EE99-4CD2-9698-B61769C8DBC9} = {F3DF3618-B80E-479A-B05D-C001BFFA1A2A}
		{04C1FAEF-3E28-4939-A2F5-594361D0D5F7} = {F3DF3618-B80E-479A-B05D-C001BFFA1A2A}
	EndGlobalSection
EndGlobal
