syntax = 'proto3';

package grpc.services.recommendation.v1;

import 'google/protobuf/timestamp.proto';
import 'google/protobuf/field_mask.proto';
import 'google/protobuf/wrappers.proto';
import 'pos.proto';
import 'page.proto';

service RecommendationServices {
	rpc GetRecommendations(GetRecommendationsRequest) returns (GetRecommendationsResponse);
}

message GetRecommendationsRequest {
	int32 location_id = 1;
	google.protobuf.Timestamp start_date = 2;
	google.protobuf.Timestamp end_date = 3;
	google.protobuf.StringValue type = 4;
	google.protobuf.FieldMask field_mask = 5;
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
	
	repeated string veh_ids = 14;
}

message GetRecommendationsResponse {
	repeated grpc.models.v1.pos.RecommendationRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}