syntax = 'proto3';

package grpc.services.appointment.v1;

import 'google/protobuf/timestamp.proto';
import 'google/protobuf/field_mask.proto';
import 'google/protobuf/wrappers.proto';
import 'pos.proto';
import 'page.proto';

service AppointmentServices {
  rpc GetAppointments(GetAppointmentsRequest) returns (GetAppointmentsResponse);
}

message GetAppointmentsRequest {
	int32 location_id = 1;
	google.protobuf.Timestamp start_date = 2;
	google.protobuf.Timestamp end_date = 3;
	google.protobuf.BoolValue arrived = 4;
	google.protobuf.FieldMask field_mask = 5;
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
}

message GetAppointmentsResponse {
	repeated grpc.models.v1.pos.AppointmentRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}