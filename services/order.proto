syntax = 'proto3';

package grpc.services.order.v1;

import 'google/protobuf/timestamp.proto';
import 'google/protobuf/field_mask.proto';
import 'google/protobuf/wrappers.proto';
import 'pos.proto';
import 'page.proto';
import 'page_google_convention.proto';

service OrderServices {
	rpc GetOrders(GetOrdersRequest) returns (GetOrdersResponse);
	rpc GetOrdersForCampaigns(GetOrdersForCampaignsRequest) returns (GetOrdersResponse);
	rpc GetOrderById(GetOrderByIdRequest) returns(GetOrderByIdResponse);
	rpc GetOrdersStream(GetOrdersRequest) returns(stream GetOrdersResponse);
	rpc GetCustomerOrders(GetOrderByIdRequest) returns(GetOrdersResponse);
	rpc GetCustomerOrdersGoogleConvention(GetOrderByIdRequest) returns(GetOrdersResponseGoogleConvention);
	rpc GetOrdersByExpectedMileage(GetOrdersByExpectedMileageRequest) returns (GetOrdersResponse);
}

message GetOrdersRequest {
	int32 location_id = 1;
	google.protobuf.Timestamp start_date = 2; //start close date
	google.protobuf.Timestamp end_date = 3; //end close date
	google.protobuf.FieldMask field_mask = 4;
	string labor_description = 5 [deprecated=true];
	//repeated string labor_descriptions = 5; //swap when no consumers
	repeated string excluded_labors = 6;
	bool target_declined_labors = 7;
	
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
	repeated string order_ids = 11;
	repeated string excluded_order_ids = 12;
	repeated string cust_ids = 13;
	repeated string veh_ids = 14;
	
	OrderType order_type = 16;
	google.protobuf.Timestamp start_open_date = 17;
	google.protobuf.Timestamp end_open_date = 18;

	OrdersSort sort_by = 30;
	bool sort_descending = 31;
	repeated OrdersSearch orders_search_type = 32;
	string search_keyword = 33;
}

//Filtration by labors is expected to return only the most recent order
message GetOrdersForCampaignsRequest {
	int32 location_id = 1;
	google.protobuf.Timestamp start_date = 2; //start close date
	google.protobuf.Timestamp end_date = 3; //end close date
	google.protobuf.FieldMask field_mask = 4;
	repeated string labor_descriptions = 5;
	repeated string excluded_labors = 6;
	bool target_declined_labors = 7;
	
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
	repeated string order_ids = 11;
	repeated string excluded_order_ids = 12;
	repeated string cust_ids = 13;
	repeated string veh_ids = 14;

	OrderType order_type = 16;
	google.protobuf.Timestamp start_open_date = 17;
	google.protobuf.Timestamp end_open_date = 18;

	repeated int32 location_ids = 20;

	OrdersSort sort_by = 30;
	bool sort_descending = 31;
	repeated OrdersSearch orders_search_type = 32;
	string search_keyword = 33;
}

enum OrdersSearch {
	orders_search_unspecified = 0;
	orders_search_order_ids = 1;
	orders_search_order_number = 2;
	orders_search_vin = 11;
	orders_search_first_names = 21;
	orders_search_last_names = 22;
	orders_search_company_names = 23;
}

enum OrderType {
	order_type_closed_orders = 0;
	order_type_open_orders = 1;
	order_type_all_orders = 2;
}

enum OrdersSort {
	orders_sort_unspecified = 0;
	orders_sort_order_id = 1;
	orders_sort_order_number = 2;
	orders_sort_open_date = 3;
	orders_sort_close_date = 4;
}

message GetOrdersResponse {
	repeated grpc.models.v1.pos.OrderRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}

message GetOrdersResponseGoogleConvention {
	repeated grpc.models.v1.pos.OrderRpc data = 1;
	grpc.models.v1.page_google_convention.PageObjectGoogleConventionRpc pageObject = 2;
}

message GetOrderByIdRequest {
	int32 location_id = 1;
	string id = 2;
	google.protobuf.Timestamp start_date = 3;
	google.protobuf.Timestamp end_date = 4;
	google.protobuf.FieldMask field_mask = 5;
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
}

message GetOrderByIdResponse {
	grpc.models.v1.pos.OrderRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}

message GetOrdersByExpectedMileageRequest {
	int32 location_id = 1;
	google.protobuf.Timestamp min_date = 2;
	google.protobuf.Timestamp max_date = 3;
	google.protobuf.FieldMask field_mask = 4;
	string labor_description = 5 [deprecated=true];
	repeated string excluded_labors = 6;
	bool target_declined_labors = 7;
	repeated string labor_descriptions = 8;

	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
	int32 target_mileage = 11;
	double default_average_mileage = 12;
	double min_average_mileage = 13;
	double max_average_mileage = 14;
	bool is_mileage_absolute = 15;
}

