syntax = 'proto3';

package grpc.services.customer.v1;

import 'google/protobuf/timestamp.proto';
import 'google/protobuf/field_mask.proto';
import 'google/protobuf/wrappers.proto';
import 'pos.proto';
import 'page.proto';

service CustomerServices {
	rpc GetCustomers(GetCustomersRequest) returns (GetCustomersResponse);
	rpc GetCustomerById(GetCustomerByIdRequest) returns(CustomerByIdResponse);
	rpc GetCustomerByName(GetCustomerByNameRequest) returns (CustomerByNameResponse);
	rpc GetCustomersByEmail(GetCustomersByEmailRequest) returns (GetCustomersByEmailResponse);
	rpc GetCustomersByPhone(GetCustomersByPhoneRequest) returns (GetCustomersByPhoneResponse);
}

message GetCustomersRequest {
	int32 location_id = 1;
	google.protobuf.Timestamp start_date = 2;
	google.protobuf.Timestamp end_date = 3;
	repeated string vehicle_makes = 4;
	repeated string customer_list = 5;
	google.protobuf.BoolValue fleet_account = 6;
	google.protobuf.FieldMask field_mask = 7;
	google.protobuf.Timestamp birth_date = 8;
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
}

message GetCustomersResponse {
	repeated grpc.models.v1.pos.CustomerRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}

message GetCustomerByIdRequest {
	int32 location_id = 1;
	string id = 2;
	google.protobuf.FieldMask field_mask = 3;
}

message CustomerByIdResponse {
	grpc.models.v1.pos.CustomerRpc data = 1;
}

message GetCustomerByNameRequest {
	int32 location_id = 1;
	string name = 2;
	google.protobuf.FieldMask field_mask = 3;
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
}

message CustomerByNameResponse {
	repeated grpc.models.v1.pos.CustomerRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}

message GetCustomersByEmailRequest {
	int32 location_id = 1;
	string email_address = 2;
	google.protobuf.FieldMask field_mask = 3;
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
}

message GetCustomersByEmailResponse {
	repeated grpc.models.v1.pos.CustomerRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}

message GetCustomersByPhoneRequest {
	int32 location_id = 1;
	string phone_number = 2;
	google.protobuf.FieldMask field_mask = 3;
	google.protobuf.Int32Value page_offset = 9;
	int32 pageSize = 10;
}

message GetCustomersByPhoneResponse {
	repeated grpc.models.v1.pos.CustomerRpc data = 1;
	grpc.models.v1.page.PageObjectRpc pageObject = 2;
}