namespace Authentication.Keycloak.WebApi;

using Grpc.Services.Order.V1;
using Kukui.Infrastructure.Authentication.Keycloak;
using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.Hosting.Web.Common;
using Kukui.Infrastructure.Hosting.Web.MinimalApi;

public class WebServerConfigurator : WebConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddMinimalApiServices();
        //services.RegisterAuthenticatedHttpClient<IAuthApiClient, AuthApiClient>(configuration.ServerConfiguration);
        services.RegisterGrpcClients(
            configure => { configure.RegisterClient<OrderServices.OrderServicesClient>(); },
            configuration.ServerConfiguration,
            "Grpc");
    }


    public override async Task ConfigureServicesAsync(
        IServiceProvider serviceProvider,
        WebServerConfiguration configuration)
    {
        var logger = serviceProvider.GetRequiredService<ILogger<WebServerConfigurator>>();
        var grpcService = serviceProvider.GetRequiredService<OrderServices.OrderServicesClient>();
        var order = await grpcService.GetOrderByIdAsync(
            new GetOrderByIdRequest
            {
                LocationId = 1,
                Id = "107964113"
            });

        logger.LogInformation("Order: {@Order}", order);

        // var client = serviceProvider.GetRequiredService<IAuthApiClient>();
        // var logger = serviceProvider.GetRequiredService<ILogger<WebServerConfigurator>>();
        // var response = await client.SearchByEmailAddressAsync("<EMAIL>", default);
        // logger.LogInformation("Response: {@Response}", response);
    }
}