<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

 <ItemGroup>
    <PackageReference Include="Grpc.Net.Client" />
    <PackageReference Include="Grpc.Net.ClientFactory" />
    <PackageReference Include="Google.Protobuf" />
    <PackageReference Include="Grpc.Tools">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="pos-grpc" />
  </ItemGroup>


 <ItemGroup>
    <Protobuf Include="$(NuGetPackageRoot)pos-grpc\$(PackageVersion)\pos-grpc\*.proto" GrpcServices="None" ProtoRoot="$(NuGetPackageRoot)pos-grpc\$(PackageVersion)\pos-grpc" />
    <Protobuf Include="$(NuGetPackageRoot)pos-grpc\$(PackageVersion)\pos-grpc\services\*.proto" GrpcServices="Client" ProtoRoot="$(NuGetPackageRoot)pos-grpc\$(PackageVersion)\pos-grpc" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Web.MinimalApi\Kukui.Hosting.Web.MinimalApi.csproj" />
    <ProjectReference Include="..\..\src\Authentication.Keycloak\Authentication.Keycloak.csproj"
                      OutputItemType="Analyzer"
                      ReferenceOutputAssembly="true"
                      PrivateAssets="all" />

  </ItemGroup>

</Project>