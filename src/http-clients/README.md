This package configures automatic keycloak authentication for all internal http clients by leveraging http
middleware support
provided by `DelegatingHandler`. It caches the token and refreshes it when it expires. Using this approach removes
all the wiring and cross-cutting concerns from the client code, where all the business logic should be.

You can further extend and add different mechanisms for your http processing pipeline like retry policies, circuit
breakers,
etc. as described
[here](https://learn.microsoft.com/en-us/aspnet/core/fundamentals/http-requests?view=aspnetcore-9.0#use-polly-based-handlers)

1. We have some http client that needs to be authenticated with keycloak.

```csharp
public class AuthApiClient : IAuthApiClient
{
    private readonly HttpClient _httpClient;

    public AuthApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<KeycloakUser?> SearchByEmailAddressAsync(string emailAddress, CancellationToken cancellationToken)
    {
       // your code here
    }
}
```

2. We need to provide some configuration for this client as well as how to authenticate with keycloak.

<h5 a><strong><code>config.json</code></strong></h5>

```json
{
  "Server": {
    "InternalServices": {
      "AuthenticationProvider": {
        "BaseUrl": "http://docker-sandbox.server.cluster:8100/auth"
      },
      "AuthApiClient": {
        "Url": "http://docker-sandbox.server.cluster:7100/",
        "Security": {
          "ClientId": "client-id",
          "ClientSecret": "secret",
          "Realm": "realm"
        }
      }
    }
  }
}
```

3. In order to wire everything, we need to:

- Install nuget package `Kukui.Infrastructure.HttpClients.Authentication.Keycloak`
- Register `AuthApiClient` in `Startup.cs` file. using the `RegisterHttpClient` extension method like this:

```csharp
  services.RegisterHttpClient<IAuthApiClient, AuthApiClient>(configuration.ServerConfiguration);
```

- Annotate the `AuthApiClient` with `GenerateKeycloakAuthHandler` attribute like this:
  ```csharp
  [GenerateKeycloakAuthHandler]
  public class AuthApiClient : IAuthApiClient
  {
      private readonly HttpClient _httpClient;

      public AuthApiClient(HttpClient httpClient)
      {
          _httpClient = httpClient;
      }

      public async Task<KeycloakUser?> SearchByEmailAddressAsync(string emailAddress, CancellationToken cancellationToken)
      {
         // your code here
      }
  }

The same authentication provider can be used for grpc clients as well. The only difference is that you need to use
`RegisterGrpcClients` extension method instead of `RegisterHttpClient` and provide `GrpcClientFactoryOptions` instead of
`HttpClient` in the constructor of your client.

```csharp
  services.RegisterGrpcClients(
            configure =>
            {
                configure.RegisterClient<OrderServices.OrderServicesClient>();
            },
            configuration.ServerConfiguration,
            // optional, if you want to load configuration from different section(default one is "Pos.Grpc)"
            grpcSectionName: "Grpc");
```

and here is the configuration

```json
  {
  "InternalServices": {
    "AuthenticationProvider": {
      "BaseUrl": "http://docker-sandbox.server.cluster:8100/auth"
    },
    "Grpc": {
      "Url": "https://docker-sandbox.server.cluster:12300",
      "Security": {
        "ClientId": "MSO",
        "ClientSecret": "secret",
        "Realm": "pos-grpc-server"
      }
    }
  }
}
```