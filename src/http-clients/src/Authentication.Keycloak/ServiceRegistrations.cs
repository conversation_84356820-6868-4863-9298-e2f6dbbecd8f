namespace Kukui.Infrastructure.Authentication.Keycloak;

using Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;

public static class ServiceRegistrations
{
    public static IHttpClientBuilder RegisterAuthenticatedHttpClient<TClient, TImplementation>(
        this IServiceCollection services,
        ServerConfiguration configuration,
        string? clientConfigurationSectionName = null)
        where TClient : class
        where TImplementation : class, TClient
    {
        var implementationType = typeof(TImplementation);
        var handlerType = implementationType.Assembly.GetType(
            $"{implementationType.Namespace}.{implementationType.Name}AuthenticationHandler");
        if (handlerType == null || !typeof(KeycloakAuthenticationHandler).IsAssignableFrom(handlerType))
        {
            throw new InvalidOperationException(
                $"Could not find a matching Keycloak authentication handler for {typeof(TImplementation).Name}. " +
                $"Expected a class named '{typeof(TImplementation).Name}AuthenticationHandler' that inherits from KeycloakAuthenticationHandler. " +
                "Use the [GenerateKeycloakAuthHandler] attribute to generate one.");
        }

        var serviceConfig =
            configuration.GetInternalServiceConfiguration(
                clientConfigurationSectionName ?? typeof(TImplementation).Name);

        services.AddMemoryCache();
        services.AddTransient(handlerType);

        return services
            .AddHttpClient<TClient, TImplementation>(client => { client.BaseAddress = new Uri(serviceConfig.Url); })
            .AddHttpMessageHandler(sp => (KeycloakAuthenticationHandler) sp.GetRequiredService(handlerType));
    }

    public static IHttpClientBuilder RegisterAuthenticatedHttpClient<TImplementation>(
        this IServiceCollection services,
        ServerConfiguration configuration,
        string? clientConfigurationSectionName = null)
        where TImplementation : class
    {
        var implementationType = typeof(TImplementation);
        var handlerType = implementationType.Assembly.GetType(
            $"{implementationType.Namespace}.{implementationType.Name}AuthenticationHandler");
        if (handlerType == null || !typeof(KeycloakAuthenticationHandler).IsAssignableFrom(handlerType))
        {
            throw new InvalidOperationException(
                $"Could not find a matching Keycloak authentication handler for {typeof(TImplementation).Name}. " +
                $"Expected a class named '{typeof(TImplementation).Name}AuthenticationHandler' that inherits from KeycloakAuthenticationHandler. " +
                "Use the [GenerateKeycloakAuthHandler] attribute to generate one.");
        }

        var serviceConfig =
            configuration.GetInternalServiceConfiguration(
                clientConfigurationSectionName ?? typeof(TImplementation).Name);

        services.AddMemoryCache();
        services.AddTransient(handlerType);

        return services.AddHttpClient<TImplementation>(client => client.BaseAddress = new Uri(serviceConfig.Url))
            .AddHttpMessageHandler(sp => (KeycloakAuthenticationHandler) sp.GetRequiredService(handlerType));
    }

    public static IServiceCollection RegisterGrpcClients(
        this IServiceCollection services,
        Action<GrpcClientOptionsBuilder> configure,
        ServerConfiguration configuration,
        string grpcSectionName = "Pos.Grpc")
    {
        services.AddMemoryCache();

        var builder = new GrpcClientOptionsBuilder(services, configuration, grpcSectionName);
        configure(builder);

        services.AddTransient<GrpcAuthenticationHandler>()
            .AddHttpClient<GrpcAuthenticationHandler>(httpClient =>
            {
                httpClient.BaseAddress = new Uri(builder.Configuration.KeycloakConfiguration.BaseUrl);
            });

        return services;
    }
}