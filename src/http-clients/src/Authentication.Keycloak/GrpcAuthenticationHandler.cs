namespace Kukui.Infrastructure.Authentication.Keycloak;

using Hosting.Common.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

public class GrpcAuthenticationHandler : KeycloakAuthenticationHandler
{
    public GrpcAuthenticationHandler(
        HttpClient client,
        IMemoryCache memoryCache,
        ServerConfiguration serverConfiguration,
        IOptions<GrpcClientConfiguration> options) : base(client, memoryCache, serverConfiguration)
    {
        ServiceName = options.Value.SectionName;
    }

    protected override string ServiceName { get; }
}