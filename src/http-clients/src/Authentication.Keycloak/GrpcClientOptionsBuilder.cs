namespace Kukui.Infrastructure.Authentication.Keycloak;

using Grpc.Net.ClientFactory;
using Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;

public class GrpcClientOptionsBuilder
{
    public GrpcClientOptionsBuilder(
        IServiceCollection services,
        ServerConfiguration serverConfiguration,
        string sectionName)
    {
        Services = services;
        Configuration = serverConfiguration.GetInternalServiceConfiguration(sectionName);
        services.Configure<GrpcClientConfiguration>(options => options.SectionName = sectionName);
    }

    internal IServiceCollection Services { get; }

    internal InternalServiceConfiguration Configuration { get; }

    public IHttpClientBuilder RegisterClient<TClient>(Action<GrpcClientFactoryOptions>? configure = null)
        where TClient : class
    {
        return Services.AddGrpcClient<TClient>(options =>
            {
                configure?.Invoke(options);
                options.Address = new Uri(Configuration.Url);
            })
            .AddHttpMessageHandler<GrpcAuthenticationHandler>();
    }

    public IHttpClientBuilder RegisterClient<TClient>(Action<IServiceProvider, GrpcClientFactoryOptions>? configure)
        where TClient : class
    {
        return Services.AddGrpcClient<TClient>((provider, options) =>
            {
                configure?.Invoke(provider, options);
                options.Address = new Uri(Configuration.Url);
            })
            .AddHttpMessageHandler<GrpcAuthenticationHandler>();
    }
}