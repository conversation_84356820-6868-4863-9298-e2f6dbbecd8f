<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Authentication.Keycloak</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Authentication.Keycloak</RootNamespace>
  </PropertyGroup>

 <PropertyGroup>
  <IsRoslynComponent>true</IsRoslynComponent>
  <IncludeBuildOutput>true</IncludeBuildOutput>
  <DevelopmentDependency>true</DevelopmentDependency>
  <EnforceExtendedAnalyzerRules>true</EnforceExtendedAnalyzerRules>
 </PropertyGroup>

 <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <PackageReference Include="Grpc.Net.ClientFactory" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" PrivateAssets="all" />
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" PrivateAssets="all" />
  </ItemGroup>

 <ItemGroup>
   <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Common\Kukui.Hosting.Common.csproj" />
 </ItemGroup>

  <ItemGroup>
    <None Include="$(OutputPath)\$(AssemblyName).dll" Pack="true"
          PackagePath="analyzers/dotnet/cs" Visible="false" />
  </ItemGroup>

</Project>