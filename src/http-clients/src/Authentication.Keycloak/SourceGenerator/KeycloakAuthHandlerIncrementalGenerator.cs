namespace Kukui.Infrastructure.Authentication.Keycloak.SourceGenerator;

using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using ClassDeclarationOutput =
    (Microsoft.CodeAnalysis.CSharp.Syntax.ClassDeclarationSyntax ClassDeclaration, string ClassName, string ServiceName,
    string Namespace);

[Generator(LanguageNames.CSharp)]
public class KeycloakAuthHandlerIncrementalGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // Create a pipeline to find all classes with the [GenerateKeycloakAuthHandler] attribute
        var classDeclarations = context.SyntaxProvider.CreateSyntaxProvider(
                static (s, _) => s is ClassDeclarationSyntax { AttributeLists.Count: > 0 },
                static (ctx, _) => GetClassWithAttributeInfo(ctx))
            .Where(static m => m.ClassDeclaration is not null);

        // Register the source output
        context.RegisterSourceOutput(
            classDeclarations,
            static (spc, info) => GenerateHandler(spc, info.ClassName!, info.ServiceName, info.Namespace));
    }

    private static ClassDeclarationOutput GetClassWithAttributeInfo(GeneratorSyntaxContext context)
    {
        var classDeclaration = (ClassDeclarationSyntax) context.Node;
        // Check if the class has our attribute
        var hasAttribute = false;
        // Get the class name
        var className = classDeclaration.Identifier.Text;
        var serviceName = className;
        foreach (var attribute in classDeclaration.AttributeLists.SelectMany(x => x.Attributes))
        {
            var attributeSymbol = context.SemanticModel.GetSymbolInfo(attribute).Symbol;
            if (attributeSymbol == null) continue;

            var containingType = attributeSymbol.ContainingType;
            if (containingType == null) continue;

            // Check for the attribute by name - this handles both old and new namespace
            if (containingType.Name == nameof(GenerateKeycloakAuthHandlerAttribute))
            {
                hasAttribute = true;
                serviceName = GetServiceNameFromAttribute(context, attribute) ?? className;
                break;
            }

            if (hasAttribute) break;
        }

        if (!hasAttribute)
        {
            return (null, null, null, null)!;
        }

        var symbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration);
        var namespaceName = symbol?.ContainingNamespace?.ToDisplayString();

        if (string.IsNullOrEmpty(namespaceName))
        {
            var syntaxRoot = classDeclaration.SyntaxTree.GetRoot();

            var fileScopedNamespace = syntaxRoot.DescendantNodes()
                .OfType<FileScopedNamespaceDeclarationSyntax>()
                .FirstOrDefault();
            if (fileScopedNamespace != null)
            {
                namespaceName = fileScopedNamespace.Name.ToString();
            }
            else
            {
                var regularNamespace =
                    syntaxRoot.DescendantNodes().OfType<NamespaceDeclarationSyntax>().FirstOrDefault();
                namespaceName = regularNamespace?.Name.ToString();
            }
        }

        if (string.IsNullOrEmpty(namespaceName))
        {
            namespaceName = "GlobalNamespace";
        }

        return (classDeclaration, className, serviceName, namespaceName!);
    }

    private static void GenerateHandler(
        SourceProductionContext context,
        string className,
        string serviceName,
        string namespaceName)
    {
        if (string.IsNullOrEmpty(className)) return;

        var sourceText = $$"""
                           // <auto-generated/>
                           using System.Net.Http;
                           using Kukui.Infrastructure.Hosting.Common.Models;
                           using Kukui.Infrastructure.Authentication.Keycloak;
                           using Microsoft.Extensions.Caching.Memory;

                           namespace {{namespaceName}}
                           {
                               public class {{className}}AuthenticationHandler : KeycloakAuthenticationHandler
                               {
                                   public {{className}}AuthenticationHandler(
                                       HttpClient client,
                                       IMemoryCache memoryCache,
                                       ServerConfiguration configuration) : base(client, memoryCache, configuration)
                                   {
                                   }

                                   protected override string ServiceName { get; } = "{{serviceName}}";
                               }
                           }
                           """;

        context.AddSource($"{className}AuthenticationHandler.g.cs", sourceText);
    }

    private static string? GetServiceNameFromAttribute(GeneratorSyntaxContext context, AttributeSyntax attribute)
    {
        // Default to null if not specified
        string? serviceName = null;

        // Check for constructor arguments (positional parameters)
        if (attribute.ArgumentList?.Arguments.Count > 0)
        {
            var firstArg = attribute.ArgumentList.Arguments[0];

            // Get the constant value from the semantic model
            var constantValue = context.SemanticModel.GetConstantValue(firstArg.Expression);
            if (constantValue.HasValue && constantValue.Value is string value)
            {
                serviceName = value;
            }
        }

        // Check for named arguments
        foreach (var arg in attribute.ArgumentList?.Arguments ?? Enumerable.Empty<AttributeArgumentSyntax>())
        {
            if (arg.NameEquals != null && arg.NameEquals.Name.Identifier.Text == "ServiceName")
            {
                var constantValue = context.SemanticModel.GetConstantValue(arg.Expression);
                if (constantValue.HasValue && constantValue.Value is string value)
                {
                    serviceName = value;
                }
            }
        }

        return serviceName;
    }
}