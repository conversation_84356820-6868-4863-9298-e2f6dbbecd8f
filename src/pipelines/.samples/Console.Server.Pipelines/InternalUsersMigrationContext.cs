namespace Console.Server.Pipelines;

using Kukui.Infrastructure.Pipeline.Processing;
using Microsoft.Extensions.Logging;

public class InternalUser
{
}

public class ChannelMigrator : MigratorBase
{
    public ChannelMigrator(ILogger<ChannelMigrator> logger) : base(logger)
    {
    }
}

public class InternalUserSettingsMigrator : IPipelineMiddleware<LocationMigrationContext>
{
    public Task ExecuteAsync(
        LocationMigrationContext context,
        PipelineRequestDelegate<LocationMigrationContext> next,
        CancellationToken cancellationToken = default) =>
        throw new NotImplementedException();
}