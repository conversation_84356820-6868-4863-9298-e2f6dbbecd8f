// See https://aka.ms/new-console-template for more information

using Console.Server.Pipelines;
using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.Hosting.Server;
using Kukui.Infrastructure.Pipeline.Processing;
using Microsoft.Extensions.DependencyInjection;

await ServerHostRunner.RunAppAsync(args, RegisterServices, ConfigureServices);

return;

void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services.AddTransient<ILocationService, LocationService>();
    services.ConfigurePipelines(options => options.ConfigureProcessing<MigrationContext>(configure =>
        {
            configure.AddPipelineStep<MigrateAutoReplies>()
                .AddPipelineStep<MigrateCannedMessages>()
                .AddPipelineStep<ChannelMigrator>()
                .AddPipelineStep<MigrateInternalUsers>()
                .WithLifeTime(ServiceLifetime.Scoped);
        })
        .ConfigureProcessing<LocationMigrationContext>(configure =>
            configure.AddPipelineStep<InternalUserSettingsMigrator>()));
}

async Task ConfigureServices(IServiceProvider serviceProvider, ServerConfiguration configuration)
{
    var pipeline = serviceProvider.GetRequiredService<IPipeline<MigrationContext>>();
    var service = serviceProvider.GetRequiredService<ILocationService>();
    var context = new MigrationContext(new Location(Guid.NewGuid(), "Test"));
    await pipeline.ExecutePipelineAsync(context, (ctx, token) => service.GetLocation(ctx, token), default);
}