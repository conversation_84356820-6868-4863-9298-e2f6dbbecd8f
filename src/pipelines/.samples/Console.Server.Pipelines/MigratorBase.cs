namespace Console.Server.Pipelines;

using Kukui.Infrastructure.Pipeline.Processing;
using Microsoft.Extensions.Logging;

public abstract class MigratorBase : IPipelineMiddleware<MigrationContext>
{
    private readonly ILogger _logger;

    protected MigratorBase(ILogger logger)
    {
        _logger = logger;
    }

    public async Task ExecuteAsync(
        MigrationContext context,
        PipelineRequestDelegate<MigrationContext> next,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Executing migrator for {Migrator}...", GetType().Name);
        await MigrateAsync(context);
        await next(context, cancellationToken);
    }

    protected virtual Task MigrateAsync(MigrationContext context) => Task.CompletedTask;
}