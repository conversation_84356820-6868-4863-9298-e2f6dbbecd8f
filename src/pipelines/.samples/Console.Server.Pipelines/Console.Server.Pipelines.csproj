<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Server\Kukui.Hosting.Server.csproj" />
    <ProjectReference Include="..\..\src\Pipeline.Processing\Pipeline.Processing.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="configs\config.json" />
    <Content Include="configs\config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>