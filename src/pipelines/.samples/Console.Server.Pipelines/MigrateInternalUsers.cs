namespace Console.Server.Pipelines;

using Kukui.Infrastructure.Pipeline.Processing;
using Microsoft.Extensions.Logging;

public class MigrateInternalUsers : MigratorBase
{
    private readonly IPipeline<LocationMigrationContext> _pipeline;

    public MigrateInternalUsers(
        ILogger<MigrateInternalUsers> logger,
        IPipeline<LocationMigrationContext> pipeline) : base(logger)
    {
        _pipeline = pipeline;
    }

    protected override async Task MigrateAsync(MigrationContext context)
    {
        var ctx = new LocationMigrationContext(context.Location);
        await _pipeline.ExecutePipelineAsync(ctx, (_, _) => Task.CompletedTask, default);
    }
}