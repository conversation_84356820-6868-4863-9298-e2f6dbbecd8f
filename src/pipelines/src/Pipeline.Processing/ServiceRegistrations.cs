namespace Kukui.Infrastructure.Pipeline.Processing;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

public static class ServiceRegistrations
{
    public static IServiceCollection ConfigurePipelines(
        this IServiceCollection services,
        Action<PipelineBuilder> configure)
    {
        services.TryAddSingleton(typeof(IPipeline<>), typeof(Pipeline<>));
        var pipelineBuilder = new PipelineBuilder();
        configure(pipelineBuilder);

        var readOnlyCollection = pipelineBuilder.Build();
        services.Add(readOnlyCollection);

        return services;
    }
}