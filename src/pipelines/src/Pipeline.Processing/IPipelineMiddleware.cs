namespace Kukui.Infrastructure.Pipeline.Processing;

public delegate Task PipelineRequestDelegate<in TContext>(TContext context, CancellationToken cts = default)
    where TContext : class;

public interface IPipelineMiddleware
{
}

public interface IPipelineMiddleware<TContext> : IPipelineMiddleware
    where TContext : class
{
    Task ExecuteAsync(TContext context, PipelineRequestDelegate<TContext> next, CancellationToken cancellationToken);
}