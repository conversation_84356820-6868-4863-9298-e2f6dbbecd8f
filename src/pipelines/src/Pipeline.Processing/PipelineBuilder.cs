namespace Kukui.Infrastructure.Pipeline.Processing;

using Microsoft.Extensions.DependencyInjection;

public class PipelineBuilder
{
    public static Type[] PipelineContextTypes { get; } = [typeof(IPipelineMiddleware<>)];

    private readonly List<ServiceDescriptor> _pipelineDescriptors = [];

    public PipelineBuilder ConfigureProcessing<TContext>(Action<PipelineMiddlewareBuilder<TContext>> configure)
        where TContext : class
    {
        var middleWares = new PipelineMiddlewareBuilder<TContext>();
        configure(middleWares);
        _pipelineDescriptors.AddRange(middleWares.Build());

        return this;
    }

    internal IReadOnlyCollection<ServiceDescriptor> Build() => _pipelineDescriptors;
}

public class PipelineMiddlewareBuilder<TContext>
    where TContext : class
{
    private readonly HashSet<Type> _pipelineStepTypes = [];
    private ServiceLifetime _serviceLifetime = ServiceLifetime.Transient;

    public PipelineMiddlewareBuilder<TContext> AddPipelineStep<TPipelineStep>()
        where TPipelineStep : IPipelineMiddleware<TContext>
    {
        _pipelineStepTypes.Add(typeof(TPipelineStep));

        return this;
    }

    public PipelineMiddlewareBuilder<TContext> WithLifeTime(ServiceLifetime lifetime)
    {
        _serviceLifetime = lifetime;

        return this;
    }

    internal IReadOnlyCollection<ServiceDescriptor> Build()
    {
        return _pipelineStepTypes.SelectMany(
                x => x.GetInterfaces(),
                (x, i) => new ServiceDescriptor(i, x, _serviceLifetime))
            .ToList();
    }
}