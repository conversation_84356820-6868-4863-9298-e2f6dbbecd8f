namespace Kukui.Infrastructure.Pipeline.Processing;

public interface IPipeline<TContext>
    where TContext : class
{
    Task ExecutePipelineAsync(
        TContext context,
        Func<TContext, CancellationToken, Task> pipelineAction,
        CancellationToken cancellationToken);

    Task ExecutePipelineAsync(TContext context, CancellationToken cancellationToken);
}

public class Pipeline<TContext> : IPipeline<TContext>
    where TContext : class
{
    private readonly IEnumerable<IPipelineMiddleware<TContext>> _pipelineSteps;

    public Pipeline(IEnumerable<IPipelineMiddleware<TContext>> pipelineSteps)
    {
        _pipelineSteps = pipelineSteps;
    }

    public async Task ExecutePipelineAsync(
        TContext context,
        Func<TContext, CancellationToken, Task> pipelineAction,
        CancellationToken cancellationToken)
    {
        await _pipelineSteps.Reverse()
            .Aggregate(
                (PipelineRequestDelegate<TContext>) PipelineAction,
                (next, pipeline) => (ctx, token) => pipeline.ExecuteAsync(ctx, next, token))(
                context,
                cancellationToken);
        return;

        Task PipelineAction(TContext ctx, CancellationToken token) => pipelineAction(context, token);
    }

    public Task ExecutePipelineAsync(TContext context, CancellationToken cancellationToken) =>
        ExecutePipelineAsync(context, (_, _) => Task.CompletedTask, cancellationToken);
}