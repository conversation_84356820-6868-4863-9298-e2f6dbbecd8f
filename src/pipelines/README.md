# Provides support for configuring and executing pipelines.

```csharp
services.ConfigurePipelines(options =>
    options.ConfigureProcessing<MigrationContext>(configure =>

        configure
            .AddPipelineStep<MigrateAutoReplies>()
            .AddPipelineStep<MigrateCannedMessages>()
            .AddPipelineStep<MigrateInternalUsers>()
            .AddPipelineStep<ChannelMigrator>()
            .WithLifeTime(ServiceLifetime.Scoped);
    )
    .ConfigureProcessing<LocationMigrationContext>(configure =>
        configure
            .AddPipelineStep<InternalUserSettingsMigrator>()
    ));
```