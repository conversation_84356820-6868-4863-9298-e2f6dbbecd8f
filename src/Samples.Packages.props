<Project>
<!--  <Import Project="$([MSBuild]::GetPathOfFileAbove('Directory.Packages.props', '$(MSBuildThisFileDirectory)/'))" />-->

 <PropertyGroup>
  <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
 </PropertyGroup>

 <ItemGroup>
  <PackageVersion Include="AutoMapper" Version="14.0.0" />
  <PackageVersion Include="EFCore.NamingConventions" Version="9.0.0" />
  <PackageVersion Include="pos-grpc" Version="1.0.2" />
  <PackageVersion Include="Google.Protobuf" Version="3.27.2" />
  <PackageVersion Include="Grpc.Net.Client" Version="2.71.0" />
  <PackageVersion Include="Grpc.Net.ClientFactory" Version="2.71.0" />
  <PackageVersion Include="Grpc.Tools" Version="2.64.0" />
 </ItemGroup>

</Project>