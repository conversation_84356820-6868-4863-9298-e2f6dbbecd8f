### Outbox implementation using ScyllaDb

- Outbox pattern is used to ensure reliable message delivery with at least one delivery semantics.

#### Requirements

- Each scylladb table should have the following columns:
    - `outbox_partition` : TIMESTAMP # with hour precision
    - `outbox_offset` : TIMESTAMP
  - `outbox_events` : TEXT # JSON serialized collection of `OutboxEntry` objects
- Materialized view with name `outbox_{base_table_name}` within the same keyspace as base table.
- Materialized view should have the following columns(including prumary keys from base table)
    - `outbox_partition` : TIMESTAMP # partition key
    - `created_offset` : TIMESTAMP
    - `outbox_events` : TEXT
- Once given messages are processed and published to the broker, outbox service will update the offset using
  the `nats kv store`

#### Usage

- Need to inherit `IOutboxEntity` interface in the entity class which needs to be tracked for outbox events.

```csharp
public interface IOutboxEntity
{
    DateTime? OutboxPartition { get; set; }

    public DateTimeOffset? OutboxOffset { get; set; }

    public string? OutboxEvents { get; set; }
}
```

```csharp
public class BusinessEntity : IOutboxEntity
{
    public Guid Id { get; set; }

    public string Name { get; set; }

    public string AdminEmail { get; set; }

    public bool Enabled { get; set; }

    public DateTimeOffset CreatedDate { get; set; }

    public DateTimeOffset? ModifiedDate { get; set; }

    public List<string> NameSearch { get; set; }

    public DateTime? OutboxPartition { get; set; }

    public DateTimeOffset? OutboxOffset { get; set; }

    public string? OutboxEvents { get; set; }
}
```

This is what `IOutboxEntity` interface looks like.
> !!! important Always use `AddOutboxEvents` or `AppendOutboxEvents` methods to store outbox events associated with the
> given entity. It is using
> specific json converter who knows how to serialize and deserialize `IEvent` events.

```csharp
 public static void AddOutboxEvents(this IOutboxEntity entity, params IEvent[] events)
    {
        if (events.Length == 0)
        {
            return;
        }

        entity.OutboxPartition = DateTimeOffset.UtcNow.Date.AddHours(DateTimeOffset.UtcNow.Hour);
        entity.OutboxOffset = DateTimeOffset.UtcNow;
        var outboxEntry = new OutboxEntry(entity.OutboxOffset.Value, events);
        entity.OutboxEvents = JsonSerializer.Serialize(new[] { outboxEntry }, SystemTextOutboxSerializer.Options);
    }

    public static void AppendOutboxEvents(this IOutboxEntity entity, params IEvent[] events)
    {
        if (string.IsNullOrWhiteSpace(entity.OutboxEvents))
        {
            entity.AddOutboxEvents(events);
            return;
        }

        entity.OutboxPartition = DateTimeOffset.UtcNow.Date.AddHours(DateTimeOffset.UtcNow.Hour);
        entity.OutboxOffset = DateTimeOffset.UtcNow;
        var outboxEntries = JsonSerializer.Deserialize<OutboxEntry[]>(
            entity.OutboxEvents,
            SystemTextOutboxSerializer.Options);
        var outboxEntry = new OutboxEntry(entity.OutboxOffset.Value, events);
        entity.OutboxEvents = JsonSerializer.Serialize(
            outboxEntries.Append(outboxEntry),
            SystemTextOutboxSerializer.Options);
    }
```

#### Registrations

- Need to configure scylladb cluster connection with added entities mappings configuration.
- Need to configure which assembly to scan for outbox entities. It will apply default outbox configuration for all
  entities from <code>config.json</code> file from `OutBox` section.

```json
 {
  "Server": {
    "Outbox": {
      "Enabled": true,
      "Interval": "00:00:01",
      "Limit": 100
    }
  }
}
```

<h6 a><strong><code>Program.cs</code></strong></h6>

```csharp
void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services
        .AddScyllaDb(configuration, options => options.AddMappings(typeof(Program).Assembly))
        .AddOutboxServices(configuration, options => options.RegisterOutboxEntitiesFromAssembly<Program>());
}

await ServerHostRunner.RunApp(args, RegisterServices);
```

- Alternatively you can overwrite default outbox configuration for specific outbox entities if needed by using the
  following overload.

<h6 a><strong><code>Program.cs</code></strong></h6>

```csharp
void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services
        .AddScyllaDb(configuration, options => options.AddMappings(typeof(Program).Assembly))
        .AddOutboxServices(configuration,
            options =>
            {
                options
                    .RegisterOutboxEntitiesFromAssembly<Program>();
                        // Register all entities from the assembly using default configuration
                options.AddOutboxEntity<BusinessEntity>(
                    new OutboxConfiguration(TimeSpan.FromMilliseconds(500),200));
                        // Register a specific entity with custom configuration
                options.AddOutboxEntity<BusinessEntity>(
                      OutboxConfiguration.Disable());
                        // Disable outbox for a specific entity
            });
}

await ServerHostRunner.RunApp(args, RegisterServices);
```