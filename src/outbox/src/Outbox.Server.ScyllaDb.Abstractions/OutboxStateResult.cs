namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

using System.Text.Json.Serialization;

public class OutboxStateResult
{
    [JsonConstructor]
    public OutboxStateResult(DateTime partition, int partitionRecords, int eventsCount)
    {
        Partition = partition;
        PartitionRecords = partitionRecords;
        EventsCount = eventsCount;
    }

    public DateTime Partition { get; }

    public int PartitionRecords { get; }

    public int EventsCount { get; }

    public static OutboxStateResult CurrentState()
    {
        var partition = DateTime.UtcNow
            .Date
            .AddHours(DateTime.UtcNow.Hour)
            .AddMinutes(DateTime.UtcNow.Minute);
        return new OutboxStateResult(partition, default, default);
    }

    public static OutboxStateResult Delayed(TimeSpan delay)
    {
        var partition = DateTime.UtcNow
            .Date
            .AddHours(DateTime.UtcNow.Hour)
            .AddMinutes(DateTime.UtcNow.Minute)
            .Add(delay);

        return new OutboxStateResult(partition, default, default);
    }

    public static OutboxStateResult CustomDate(DateTime dateTime)
    {
        var partition = dateTime.Date
            .AddHours(dateTime.Hour)
            .AddMinutes(dateTime.Minute);

        return new OutboxStateResult(partition, default, default);
    }
}