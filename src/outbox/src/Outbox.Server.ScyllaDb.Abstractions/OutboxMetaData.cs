namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

using Database.ScyllaDb.Abstractions.Mappings;

public class OutboxMetaData
{
    private string? _fetchOutboxEntriesQuery;
    private string? _findPartitionQuery;

    public OutboxMetaData(ITypeDefinitionCache typeDefinitionCache)
    {
        PartitionColumn = typeDefinitionCache.GetColumn<IOutboxEntity, DateTime?>(x => x.OutboxPartition);
        EventsColumn = typeDefinitionCache.GetColumn<IOutboxEntity, string?>(x => x.OutboxEvents);
        EventsCountColumn = typeDefinitionCache.GetColumn<IOutboxEntity, int>(x => x.OutboxEventsCount);
        MaterializedView = $"outbox_{typeDefinitionCache.TypeDefinition!.TableName}";
        KeySpace = typeDefinitionCache.TypeDefinition!.KeyspaceName;
    }

    public string PartitionColumn { get; }

    public string EventsColumn { get; }

    public string EventsCountColumn { get; set; }

    public string MaterializedView { get; }

    public string KeySpace { get; }

    public string FetchOutboxEntriesQuery
    {
        get
        {
            if (_fetchOutboxEntriesQuery != null) return _fetchOutboxEntriesQuery;

            _fetchOutboxEntriesQuery = $"""
                                        SELECT {PartitionColumn}, {EventsColumn}
                                        FROM {KeySpace}.{MaterializedView}
                                        WHERE {PartitionColumn} = ?
                                        """;
            return _fetchOutboxEntriesQuery;
        }
    }

    public string FindPartitionQuery
    {
        get
        {
            if (_findPartitionQuery != null) return _findPartitionQuery;

            _findPartitionQuery = $"""
                                   SELECT {PartitionColumn}
                                   FROM {KeySpace}.{MaterializedView}
                                   """;
            return _findPartitionQuery;
        }
    }
}