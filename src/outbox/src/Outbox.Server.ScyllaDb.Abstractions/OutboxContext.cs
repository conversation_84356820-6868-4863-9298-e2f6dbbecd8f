namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

using Database.ScyllaDb.Abstractions.Mappings;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serialization;
using Server.Abstractions;

public class OutboxContext
{
    public OutboxContext(
        IServiceProvider serviceProvider,
        OutboxConfiguration configuration,
        ITypeDefinitionCache typeDefinitionCache,
        ILogger logger)
    {
        ContextId = Guid.NewGuid();
        ServiceProvider = serviceProvider;
        Logger = logger;
        Configuration = configuration;
        OutboxSerializer = serviceProvider.GetRequiredService<IOutboxSerializer>();
        TypeDefinitionCache = typeDefinitionCache;
        OutboxMetaData = new OutboxMetaData(TypeDefinitionCache);
        CacheKeyFormat = $"{TypeDefinitionCache.TypeDefinition!.TableName}.events.{{0}}";
    }

    public Guid ContextId { get; }

    public ITypeDefinitionCache TypeDefinitionCache { get; }

    public IServiceProvider ServiceProvider { get; }

    public OutboxConfiguration Configuration { get; }

    public IOutboxSerializer OutboxSerializer { get; }

    public OutboxMetaData OutboxMetaData { get; }

    public string CacheKeyFormat { get; }

    public ILogger Logger { get; }
}