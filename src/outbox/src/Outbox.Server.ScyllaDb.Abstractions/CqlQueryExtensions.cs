namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

using System.Linq.Expressions;
using System.Reflection;
using Cassandra.Data.Linq;

public static class CqlQueryExtensions
{
    public static CqlUpdate UpdateWithOutboxEvents<T>(
        this CqlQuery<T> query,
        T instance,
        Expression<Func<T, T>> updateExpression)
        where T : IOutboxEntity
    {
        var outboxProperties =
            typeof(IOutboxEntity).GetProperties(BindingFlags.Instance | BindingFlags.Public | BindingFlags.SetProperty);

        if (updateExpression.Body is not MemberInitExpression memberInitExpression)
        {
            throw new ArgumentException($"Update expression must be {typeof(MemberInitExpression)} type");
        }

        var typeParameter = Expression.Parameter(typeof(T), "x");
        var newExpression = Expression.New(typeof(T));

        MemberAssignment[] outboxBindings = [];
        var outboxValues = outboxProperties.Select(
                property => new
                {
                    Property = property,
                    Value = property.GetValue(instance)
                })
            .ToArray();
        if (outboxValues.All(x => x.Value != null))
        {
            outboxBindings = outboxProperties.Select(
                    property => Expression.Bind(property, Expression.Constant(property.GetValue(instance))))
                .ToArray();
        }

        var memberInit = Expression.MemberInit(newExpression, memberInitExpression.Bindings.Concat(outboxBindings));
        var lambda = Expression.Lambda<Func<T, T>>(memberInit, typeParameter);

        return query.Select(lambda).Update();
    }
}