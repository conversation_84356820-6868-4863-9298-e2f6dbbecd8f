namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

public class DatabaseOutboxResult
{
    public DatabaseOutboxResult(
        DateTime? partition,
        IEnumerable<DatabaseOutboxEntity> entities,
        DatabasePartitionResult partitionResult)
    {
        Entities = entities.ToList();
        Partition = partition;
        PartitionResult = partitionResult;
    }

    public IReadOnlyCollection<DatabaseOutboxEntity> Entities { get; }

    public DateTime? Partition { get; }

    public DatabasePartitionResult PartitionResult { get; }

    public static DatabaseOutboxResult Empty(DateTime? partition)
    {
        return new DatabaseOutboxResult(partition, [], DatabasePartitionResult.Empty());
    }
}