namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

using System.Text.Json;
using MessageBus.Abstractions.Events;
using Serialization;

public static class OutboxEntityExtensions
{
    public static void AddOutboxEvents(this IOutboxEntity entity, params IEvent[] events)
    {
        if (events.Length == 0) return;

        var currentState = OutboxStateResult.CurrentState();
        entity.OutboxPartition = currentState.Partition;
        var outboxEntry = new OutboxEntry(DateTime.UtcNow, events);
        entity.AddEvents(outboxEntry);
    }

    public static void AddOutboxEventsWithDelay(this IOutboxEntity entity, TimeSpan timeSpan, params IEvent[] events)
    {
        if (events.Length == 0) return;

        var currentState = OutboxStateResult.Delayed(timeSpan);
        entity.OutboxPartition = currentState.Partition;
        var outboxEntry = new OutboxEntry(DateTime.UtcNow.Add(timeSpan), events);

        entity.AddEvents(outboxEntry);
    }

    public static void AddOutboxEventsForDate(this IOutboxEntity entity, DateTime dateTime, params IEvent[] events)
    {
        if (events.Length == 0) return;

        var currentState = OutboxStateResult.CustomDate(dateTime);
        entity.OutboxPartition = currentState.Partition;
        var outboxEntry = new OutboxEntry(dateTime, events);
        entity.AddEvents(outboxEntry);
    }

    public static void AppendOutboxEvents(this IOutboxEntity entity, params IEvent[] events)
    {
        if (string.IsNullOrWhiteSpace(entity.OutboxEvents))
        {
            entity.AddOutboxEvents(events);

            return;
        }

        entity.OutboxPartition = OutboxStateResult.CurrentState()
            .Partition;
        var outboxEntry = new OutboxEntry(DateTime.UtcNow, events);
        entity.AppendEvents(outboxEntry);
    }

    public static void AppendOutboxEventsWithDelay(this IOutboxEntity entity, TimeSpan delay, params IEvent[] events)
    {
        if (string.IsNullOrWhiteSpace(entity.OutboxEvents))
        {
            entity.AddOutboxEvents(events);

            return;
        }

        entity.OutboxPartition = OutboxStateResult.Delayed(delay)
            .Partition;
        var outboxEntry = new OutboxEntry(DateTime.UtcNow.Add(delay), events);
        entity.AppendEvents(outboxEntry);
    }

    public static void AppendOutboxEventsForDate(this IOutboxEntity entity, DateTime dateTime, params IEvent[] events)
    {
        if (string.IsNullOrWhiteSpace(entity.OutboxEvents))
        {
            entity.AddOutboxEvents(events);

            return;
        }

        entity.OutboxPartition = OutboxStateResult.CustomDate(dateTime)
            .Partition;
        var outboxEntry = new OutboxEntry(dateTime, events);
        entity.AppendEvents(outboxEntry);
    }

    public static IOutboxEntity CreateOutboxEvents<TEntity>(this IEnumerable<IEvent> events)
        where TEntity : class, IOutboxEntity, new()
    {
        var entity = new TEntity();

        entity.AddOutboxEvents(events.ToArray());

        return entity;
    }

    public static IOutboxEntity CreateOutboxEvent<TEntity>(this IEvent @event)
        where TEntity : class, IOutboxEntity, new()
    {
        return CreateOutboxEvents<TEntity>([@event]);
    }

    private static void AddEvents(this IOutboxEntity entity, OutboxEntry outboxEntry)
    {
        entity.OutboxEvents = JsonSerializer.Serialize(new[] { outboxEntry }, SystemTextOutboxSerializer.Options);
        entity.OutboxEventsCount = outboxEntry.Events.Length;
    }

    private static void AppendEvents(this IOutboxEntity entity, OutboxEntry outboxEntry)
    {
        var outboxEntries = JsonSerializer.Deserialize<OutboxEntry[]>(
            entity.OutboxEvents ?? string.Empty,
            SystemTextOutboxSerializer.Options) ?? [];
        entity.OutboxEvents = JsonSerializer.Serialize(
            outboxEntries.Append(outboxEntry),
            SystemTextOutboxSerializer.Options);
        entity.OutboxEventsCount = outboxEntries.SelectMany(x => x.Events)
            .Count() + outboxEntry.Events.Length;
    }
}