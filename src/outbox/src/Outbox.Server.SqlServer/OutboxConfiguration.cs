namespace Kukui.Infrastructure.Outbox.Server.SqlServer;

using Microsoft.EntityFrameworkCore;
using Relational.Models;

public static class OutboxConfiguration
{
    public static ModelBuilder AddOutboxConfiguration(this ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<OutboxError>(
            entity =>
            {
                entity.ToTable("OutboxErrors", "dbo");

                entity.HasKey(e => e.Id);

                entity.Property(x => x.Id).ValueGeneratedNever();

                entity.Property(e => e.EventJson).IsRequired();

                entity.Property(e => e.DateCreated).IsRequired();
            });

        modelBuilder.Entity<OutboxEvent>(
            entity =>
            {
                entity.ToTable("OutboxEvents", "dbo");

                entity.HasKey(e => e.Id);

                entity.Property(x => x.Id).ValueGeneratedOnAdd();

                entity.Property(e => e.EventJson).IsRequired();

                entity.Property(e => e.DateCreated).IsRequired();
            });

        modelBuilder.Entity<OutboxOffset>(
            entity =>
            {
                entity.ToTable("OutboxOffset", "dbo");

                entity.HasKey(e => e.Id);

                entity.Property(x => x.Id).ValueGeneratedNever();
            });

        return modelBuilder;
    }
}