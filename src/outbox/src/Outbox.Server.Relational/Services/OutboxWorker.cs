namespace Kukui.Infrastructure.Outbox.Server.Relational.Services;

using System.Runtime.Serialization;
using Abstractions;
using MessageBus.Abstractions.Events;
using MessageBus.Abstractions.Publisher;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Models;
using Serialization;

public class OutboxWorker<TContext>
    where TContext : OutboxDbContextBase
{
    private readonly TContext _context;
    private readonly IPublisherFactory _publisherFactory;
    private readonly IOutboxSerializer _outboxSerializer;
    private readonly ILogger<OutboxWorker<TContext>> _logger;

    public OutboxWorker(
        TContext context,
        IPublisherFactory publisherFactory,
        IOutboxSerializer outboxSerializer,
        ILogger<OutboxWorker<TContext>> logger)
    {
        _context = context;
        _publisherFactory = publisherFactory;
        _outboxSerializer = outboxSerializer;
        _logger = logger;
    }

    public async Task ProcessOutboxRecordsAsync(OutboxConfiguration configuration, CancellationToken stoppingToken)
    {
        var offset = _context.OutboxOffset.FirstOrDefault();
        var outboxOffset = offset?.Id ?? 0;
        var outboxEvents = await _context.OutboxEvents.Where(e => e.Id > outboxOffset)
            .OrderBy(x => x.Id)
            .Take(configuration.BatchSize)
            .ToListAsync(stoppingToken);

        if (outboxEvents.Count == 0)
        {
            return;
        }

        foreach (var outboxEvent in outboxEvents)
        {
            try
            {
                await RetryPolicy(
                    3,
                    TimeSpan.FromSeconds(10),
                    async () =>
                    {
                        var @event = _outboxSerializer.Deserialize<IEvent>(outboxEvent.EventJson) ??
                                     throw new SerializationException("Failed to deserialize event");

                        var publisher = _publisherFactory.Create(@event.GetType());
                        await publisher.PublishAsync(@event, cancellationToken: stoppingToken);
                        _logger.LogInformation(
                            "Outbox event {EventType} with Id {IdentityValue} has been published",
                            outboxEvent.EventName,
                            outboxEvent.Id);
                    });
            }
            catch (Exception)
            {
                _logger.LogError("Failed to process event {@Event}", outboxEvent);
                _context.OutboxErrors.Add(OutboxError.Create(outboxEvent));
                _context.OutboxEvents.Remove(outboxEvent);
            }
        }

        var eventsOffset = outboxEvents.Last().Id;
        if (eventsOffset > outboxOffset)
        {
            if (offset != null)
            {
                _context.OutboxOffset.Remove(offset);
            }

            _context.OutboxOffset.Add(
                new OutboxOffset
                {
                    Id = outboxEvents.Last().Id
                });
        }

        await _context.SaveChangesAsync(stoppingToken);
    }

    private static async Task RetryPolicy(int attempt, TimeSpan delay, Func<Task> action)
    {
        var retried = 0;
        while (retried < attempt)
        {
            try
            {
                await action();
                break;
            }
            catch (Exception)
            {
                if (++retried == attempt)
                {
                    throw;
                }

                await Task.Delay(retried * delay);
            }
        }
    }
}