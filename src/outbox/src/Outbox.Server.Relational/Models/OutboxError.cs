namespace Kukui.Infrastructure.Outbox.Server.Relational.Models;

using System.Diagnostics.CodeAnalysis;

[SuppressMessage("ReSharper", "EntityFramework.ModelValidation.UnlimitedStringLength")]
public class OutboxError
{
    public int Id { get; set; }

    public string EventName { get; set; }

    public string EventJson { get; set; }

    public DateTimeOffset DateCreated { get; set; } = DateTimeOffset.UtcNow;

    public static OutboxError Create(OutboxEvent outboxEvent)
    {
        return new OutboxError
        {
            Id = outboxEvent.Id,
            EventName = outboxEvent.EventName,
            EventJson = outboxEvent.EventJson
        };
    }
}