namespace Kukui.Infrastructure.Outbox.Server.Relational.Models;

using System.Diagnostics.CodeAnalysis;

[SuppressMessage("ReSharper", "EntityFramework.ModelValidation.UnlimitedStringLength")]
public class OutboxEvent
{
    public int Id { get; set; }

    public string EventName { get; set; }

    public string EventJson { get; set; }

    public DateTimeOffset DateCreated { get; set; } = DateTimeOffset.UtcNow;
}