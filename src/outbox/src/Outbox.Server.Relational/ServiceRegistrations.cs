namespace Kukui.Infrastructure.Outbox.Server.Relational;

using Abstractions;
using Hosting.Common.Models;
using MessageBus.Nats.JetStream;
using Microsoft.Extensions.DependencyInjection;
using Serialization;
using Services;

public static class ServiceRegistrations
{
    public static IServiceCollection AddOutboxServices<TContext>(
        this IServiceCollection services,
        ServerConfiguration serverConfiguration)
        where TContext : OutboxDbContextBase
    {
        var configuration = new RelationalOutboxConfiguration();
        serverConfiguration.BindServerConfig(OutboxConfiguration.SectionName, configuration);
        return services.AddSingleton(configuration)
            .AddSingleton<IOutboxSerializer, SystemTextOutboxSerializer>()
            .AddNatsJetStream(serverConfiguration)
            .AddHostedService<OutboxHostedService<TContext>>()
            .AddScoped<OutboxWorker<TContext>>();
    }
}