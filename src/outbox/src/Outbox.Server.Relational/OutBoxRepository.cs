namespace Kukui.Infrastructure.Outbox.Server.Relational;

using MessageBus.Abstractions.Events;

public abstract class OutBoxRepository<TContext> : IOutboxRepository
    where TContext : OutboxDbContextBase
{
    private readonly TContext _context;

    protected OutBoxRepository(TContext context)
    {
        _context = context;
    }

    public void AddOutboxEvents(params IEvent[] outboxEvents)
    {
        _context.AddOutboxEvents(outboxEvents);
    }
}