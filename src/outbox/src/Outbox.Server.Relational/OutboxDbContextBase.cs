namespace Kukui.Infrastructure.Outbox.Server.Relational;

using System.Text.Json;
using MessageBus.Abstractions.Events;
using Microsoft.EntityFrameworkCore;
using Models;
using Serialization;

public abstract class OutboxDbContextBase : DbContext
{
    protected OutboxDbContextBase(DbContextOptions options) : base(options)
    {
    }

    public virtual DbSet<OutboxEvent> OutboxEvents { get; set; }

    public virtual DbSet<OutboxOffset> OutboxOffset { get; set; }

    public virtual DbSet<OutboxError> OutboxErrors { get; set; }


    protected abstract void OnOutboxModelCreating(ModelBuilder modelBuilder);

    protected sealed override void OnModelCreating(ModelBuilder modelBuilder)
    {
        OnOutboxModelCreating(modelBuilder);
    }

    public void AddOutboxEvents(params IEvent[] outboxEvents)
    {
        var events = outboxEvents.Select(
                outbox => new OutboxEvent
                {
                    EventName = outbox.GetType().Name,
                    EventJson = JsonSerializer.Serialize(outbox, SystemTextOutboxSerializer.Options),
                    DateCreated = DateTimeOffset.UtcNow
                })
            .ToList();

        AddRange(events);
    }
}