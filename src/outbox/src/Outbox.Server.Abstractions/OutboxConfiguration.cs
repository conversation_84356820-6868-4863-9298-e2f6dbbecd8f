namespace Kukui.Infrastructure.Outbox.Server.Abstractions;

public class OutboxConfiguration
{
    public const string SectionName = "Outbox";

    public OutboxConfiguration()
    {
    }

    private OutboxConfiguration(TimeSpan interval, bool enabled = true)
    {
        Interval = interval;
        Enabled = enabled;
    }

    public static OutboxConfiguration Create(TimeSpan interval)
    {
        return new OutboxConfiguration(interval);
    }

    public static OutboxConfiguration CreateWithMilliseconds(int milliseconds)
    {
        return new OutboxConfiguration(TimeSpan.FromMilliseconds(milliseconds));
    }

    public static OutboxConfiguration Disable()
    {
        return new OutboxConfiguration(TimeSpan.FromSeconds(1), false);
    }

    public TimeSpan Interval { get; set; } = TimeSpan.FromSeconds(1);

    public int BatchSize { get; set; } = 100;

    public bool Enabled { get; set; } = true;
}