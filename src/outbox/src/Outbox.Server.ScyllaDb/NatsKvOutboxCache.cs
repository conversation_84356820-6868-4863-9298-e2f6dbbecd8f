namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

using System.Collections.Concurrent;
using System.Text.Json;
using Abstractions;
using MessageBus.Nats.KeyValue;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NATS.Client.KeyValueStore;

public class NatsKvOutboxCache<TEntity> : IOutboxCache<TEntity>
    where TEntity : IOutboxEntity
{
    private readonly PeriodicTimer _evictionTimer;
    private readonly ILogger<NatsKvOutboxCache<TEntity>> _logger;
    private readonly string _outboxFileCache = $"{typeof(TEntity).Name}.cache";
    private readonly IKeyValueStore _outboxStore;
    private ConcurrentDictionary<string, DateTimeOffset> _keys = new();

    public NatsKvOutboxCache(
        [FromKeyedServices(OutboxRegistrations.KvStoreName)] IKeyValueStore outboxStore,
        ILogger<NatsKvOutboxCache<TEntity>> logger)
    {
        _outboxStore = outboxStore;
        _logger = logger;
        _evictionTimer = new PeriodicTimer(TimeSpan.FromMinutes(30));
        Task.Run(ScheduleEvictingCacheEntries);

        if (!Directory.Exists(OutboxRegistrations.KvStoreName))
        {
            Directory.CreateDirectory(OutboxRegistrations.KvStoreName);
        }
    }

    public string CacheDirectory => Path.Combine(OutboxRegistrations.KvStoreName, _outboxFileCache);

    public void AddMemoryCacheEntry(string key)
    {
        var ttl = DateTimeOffset.UtcNow.AddMinutes(15);
        _keys.TryAdd(key, ttl);
    }


    public bool ContainsKey(string key) => _keys.ContainsKey(key);

    public void LoadCacheEntries()
    {
        if (!File.Exists(CacheDirectory))
        {
            return;
        }

        _keys = JsonSerializer.Deserialize<ConcurrentDictionary<string, DateTimeOffset>>(
            File.ReadAllText(CacheDirectory)) ?? new ConcurrentDictionary<string, DateTimeOffset>();

        _logger.LogInformation(
            "Loaded {Count} outbox cache keys for {OutboxEntity} outbox entity",
            _keys.Count,
            typeof(TEntity).Name);
    }

    public void Dispose()
    {
        FlushCacheEntries();
        _evictionTimer.Dispose();
    }

    private void FlushCacheEntries()
    {
        File.WriteAllText(CacheDirectory, JsonSerializer.Serialize(_keys));
        _logger.LogInformation(
            "Saved {Count} outbox cache keys for {OutboxEntity} outbox entity to {CacheDirectory}",
            _keys.Count,
            typeof(TEntity).Name,
            CacheDirectory);
    }

    private async Task ScheduleEvictingCacheEntries()
    {
        while (await _evictionTimer.WaitForNextTickAsync())
        {
            foreach (var key in _keys.Where(x => DateTimeOffset.UtcNow > x.Value))
            {
                try
                {
                    if (_keys.TryRemove(key))
                    {
                        await _outboxStore.PurgeAsync(key.Key);
                    }
                }
                catch (NatsKVKeyNotFoundException)
                {
                }
            }
        }
    }
}