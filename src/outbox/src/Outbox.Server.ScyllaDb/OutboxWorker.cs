namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

using Abstractions;
using Cassandra;
using Database.ScyllaDb.Mapping.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Server.Abstractions;

public class OutboxWorker<TEntity> : IOutboxWorker<TEntity>
    where TEntity : IOutboxEntity
{
    private readonly IServiceProvider _serviceProvider;
    private readonly OutboxConfiguration _configuration;
    private readonly IOutboxCache<TEntity> _outboxCache;

    public OutboxWorker(IServiceProvider serviceProvider, OutboxConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _outboxCache = serviceProvider.GetRequiredService<IOutboxCache<TEntity>>();
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        var logger = _serviceProvider.GetRequiredService<ILogger<OutboxWorker<TEntity>>>();
        _serviceProvider.GetRequiredService<ISession>();
        var typeCache = _serviceProvider.GetRequiredService<TypeDefinitionCache<TEntity>>();

        if (typeCache.TypeDefinition is null)
        {
            logger.LogError("Failed to get mapping information for {DbEntityType}", typeof(TEntity).Name);

            return;
        }

        // Cassandra driver tries to get metadata from the cluster associated with and tries to parse it to double, but they no longer exists:
        // - `dclocal_read_repair_chance` and `read_repair_chance`
        // Once it's fixed, we can use the following code:
        // var materializedView = session.Cluster.Metadata.GetOutboxMaterializedViewMetadata(typeCache);

        _outboxCache.LoadCacheEntries();

        while (!cancellationToken.IsCancellationRequested)
        {
            await Task.Delay(_configuration.Interval, cancellationToken);

            var outboxContext = new OutboxContext(_serviceProvider, _configuration, typeCache, logger);

            if (_configuration.Enabled) await DoWorkAsync(outboxContext, cancellationToken);
        }
    }

    private async Task DoWorkAsync(OutboxContext outboxContext, CancellationToken cancellationToken)
    {
        try
        {
            if (cancellationToken.IsCancellationRequested) return;

            var outboxWorkerHandler = _serviceProvider.GetRequiredService<IOutboxWorkerHandler<TEntity>>();
            outboxContext.Logger.LogDebug(
                "Outbox worker start processing outbox events from {TableName}",
                outboxContext.TypeDefinitionCache.TypeDefinition!.TableName);

            await outboxWorkerHandler.HandleAsync(outboxContext, cancellationToken);

            outboxContext.Logger.LogDebug(
                "Outbox worker finished processing outbox events from {TableName}",
                outboxContext.TypeDefinitionCache.TypeDefinition.TableName);
        }
        catch (Exception e)
        {
            outboxContext.Logger.LogError(
                e,
                "Outbox worker failed to process outbox events from {TableName}",
                outboxContext.TypeDefinitionCache.TypeDefinition!.TableName);
        }
    }

    public void Dispose()
    {
        _outboxCache.Dispose();
    }
}