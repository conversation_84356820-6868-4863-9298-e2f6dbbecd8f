namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

using Abstractions;
using Server.Abstractions;

public class OutboxServiceConfiguration
{
    internal readonly Dictionary<Type, OutboxConfiguration> OutboxServices = new();

    public OutboxServiceConfiguration(OutboxConfiguration? configuration = null)
    {
        Configuration = configuration ?? new OutboxConfiguration();
    }

    public OutboxConfiguration Configuration { get; }

    public OutboxServiceConfiguration AddOutboxEntity<TEntity>(OutboxConfiguration? configuration = null)
        where TEntity : IOutboxEntity
    {
        AddOutboxEntity(typeof(TEntity), configuration);

        return this;
    }

    // ReSharper disable once MemberCanBePrivate.Global
    public OutboxServiceConfiguration AddOutboxEntity(Type outboxType, OutboxConfiguration? configuration = null)
    {
        OutboxServices[outboxType] = (configuration ?? Configuration)!;

        return this;
    }

    public OutboxServiceConfiguration RegisterOutboxEntitiesFromAssembly<TOutbox>()
    {
        // ReSharper disable once ComplexConditionExpression
        var outboxTypes = typeof(TOutbox).Assembly
            .GetTypes()
            .Where(type => typeof(IOutboxEntity).IsAssignableFrom(type) && type is { IsClass: true, IsAbstract: false })
            .ToList();

        foreach (var outboxType in outboxTypes)
        {
            AddOutboxEntity(outboxType);
        }

        return this;
    }
}