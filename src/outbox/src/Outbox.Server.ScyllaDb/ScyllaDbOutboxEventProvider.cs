namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

using Abstractions;
using Cassandra;
using Cassandra.Mapping;
using Microsoft.Extensions.Logging;

public class ScyllaDbOutboxEventProvider : IOutboxEventProvider
{
    private readonly ISession _session;
    private const int DefaultPageSize = 1000;

    public ScyllaDbOutboxEventProvider(ISession session)
    {
        _session = session;
    }

    public async Task<DatabaseOutboxResult> FetchEvents(OutboxContext context, OutboxStateResult? outboxState)
    {
        if (outboxState != null) return await GetDbResults(context, outboxState);

        var firstPartition = await GetFirstPartitionDataAsync(context);

        if (firstPartition == null) return DatabaseOutboxResult.Empty(outboxState?.Partition);

        return await GetDbResults(context, new OutboxStateResult(firstPartition.Value, default, default));
    }

    private async Task<DatabaseOutboxResult> GetDbResults(OutboxContext context, OutboxStateResult outboxState)
    {
        var partitionResult = await GetPartitionRecordsCountAsync(context, outboxState.Partition);

        if (partitionResult.PartitionCount == outboxState.PartitionRecords &&
            partitionResult.EventsCount == outboxState.EventsCount)
            return DatabaseOutboxResult.Empty(outboxState.Partition);

        var dbResult = await GetPartitionDbResultAsync(context, outboxState, partitionResult);

        if (dbResult.Entities.Count == 0) return DatabaseOutboxResult.Empty(outboxState.Partition);

        context.Logger.LogDebug(
            "Fetched {OutboxRecordsCount} outbox records for {OutboxEntity} with {OutboxPartition} partition",
            dbResult.Entities.Count,
            context.OutboxMetaData.MaterializedView,
            outboxState.Partition);

        return dbResult;
    }

    private async Task<DateTime?> GetFirstPartitionDataAsync(OutboxContext context)
    {
        var mapper = new Mapper(_session);
        var queryResult = new List<DateTime>();
        byte[]? pageState = default;
        do
        {
            var pageResult = await mapper.FetchPageAsync<DateTime>(
                DefaultPageSize,
                pageState,
                context.OutboxMetaData.FindPartitionQuery,
                null);

            queryResult.AddRange(pageResult);
            pageState = pageResult.PagingState;
        } while (pageState != null);

        return queryResult.OrderBy(x => x)
            .FirstOrDefault();
    }

    private async Task<DatabaseOutboxResult> GetPartitionDbResultAsync(
        OutboxContext context,
        OutboxStateResult outboxState,
        DatabasePartitionResult partitionResult)
    {
        var mapper = new Mapper(_session);
        var partitionEntities = new List<DatabaseOutboxEntity>(partitionResult.PartitionCount);
        byte[]? pageState = default;
        do
        {
            var result = await mapper.FetchPageAsync<DatabaseOutboxEntity>(
                DefaultPageSize,
                pageState,
                context.OutboxMetaData.FetchOutboxEntriesQuery,
                [outboxState.Partition]);
            partitionEntities.AddRange(result);

            pageState = result.PagingState;
        } while (pageState != null);

        return new DatabaseOutboxResult(outboxState.Partition, partitionEntities, partitionResult);
    }

    private async Task<DatabasePartitionResult> GetPartitionRecordsCountAsync(OutboxContext context, DateTime partition)
    {
        var mapper = new Mapper(_session);

        return await mapper.FirstAsync<DatabasePartitionResult>(
            $"""
             SELECT cast(COUNT(*) AS INT) as partition_count, cast(sum({context.OutboxMetaData.EventsCountColumn}) AS INT) as events_count
             FROM {context.OutboxMetaData.KeySpace}.{context.OutboxMetaData.MaterializedView}
             WHERE {context.OutboxMetaData.PartitionColumn} = ?
             """,
            partition);
    }
}