<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="$([MSBuild]::GetPathOfFileAbove('AppLibrary.Build.props', '$(MSBuildThisFileDirectory)../../'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Outbox.Server.ScyllaDb</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Outbox.Server.ScyllaDb</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\database\src\ScyllaDb.Hosting.Extensions\ScyllaDb.Hosting.Extensions.csproj" />
    <ProjectReference Include="..\..\..\message-bus\src\MessageBus.Nats.JetStream\MessageBus.Nats.JetStream.csproj" />
    <ProjectReference Include="..\..\..\message-bus\src\MessageBus.Nats.KeyValue\MessageBus.Nats.KeyValue.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
  </ItemGroup>

</Project>