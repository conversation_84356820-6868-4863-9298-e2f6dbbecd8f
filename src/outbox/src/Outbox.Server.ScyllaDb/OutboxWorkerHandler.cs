namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

using System.Collections.Concurrent;
using Abstractions;
using MessageBus.Abstractions.Events;
using MessageBus.Abstractions.Publisher;
using MessageBus.Nats.KeyValue;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NATS.Client.KeyValueStore;
using Serialization;

public class OutboxWorkerHandler<TEntity> : IOutboxWorkerHandler<TEntity>
    where TEntity : IOutboxEntity
{
    private readonly IKeyValueStore _outboxStore;
    private readonly IOutboxCache<TEntity> _outboxCache;
    private readonly IOutboxEventProvider _outboxEventProvider;
    private readonly ILogger _logger;
    private readonly ConcurrentDictionary<string, IPublisher> _publishers = new();

    public OutboxWorkerHandler(
        [FromKeyedServices(OutboxRegistrations.KvStoreName)] IKeyValueStore outboxStore,
        IOutboxCache<TEntity> outboxCache,
        IOutboxEventProvider outboxEventProvider,
        ILogger<OutboxWorkerHandler<TEntity>> logger)
    {
        _outboxStore = outboxStore;
        _outboxCache = outboxCache;
        _outboxEventProvider = outboxEventProvider;
        _logger = logger;
    }

    public async Task HandleAsync(OutboxContext context, CancellationToken cancellationToken)
    {
        var typeDefinitionTableName = context.TypeDefinitionCache.TypeDefinition!.TableName;

        var outboxStateEntry = await GetOutboxStateEntryAsync(context, cancellationToken);

        var outboxResult = await GetDatabaseOutboxRecords(context, outboxStateEntry.Value);

        var outboxEvents = outboxResult.Entities.SelectMany(
                record =>
                {
                    var outboxEntries =
                        context.OutboxSerializer.Deserialize<IEnumerable<OutboxEntrySerializationResult>>(
                            record.OutboxEvents) ?? throw new OutboxSerializationException(
                            record.OutboxPartition,
                            record.OutboxEvents);

                    return outboxEntries.Where(x => x.Partition > record.OutboxPartition);
                })
            .SelectMany(x => x.Events)
            .ToList();

        await PublishOutboxEventsAsync(context, outboxEvents, cancellationToken);

        if (outboxResult.Entities.Count > 0)
        {
            var outboxState = new OutboxStateResult(
                outboxResult.Partition!.Value,
                outboxResult.PartitionResult.PartitionCount,
                outboxResult.PartitionResult.EventsCount);

            await _outboxStore.PutAsync(typeDefinitionTableName, outboxState, cancellationToken);

            context.Logger.LogInformation(
                "Outbox state changed to {@OutboxState} for {DatabaseTable}",
                outboxState,
                typeDefinitionTableName);
        }

        else
        {
            var currentState = OutboxStateResult.CurrentState();
            if (outboxStateEntry.Value?.Partition < currentState.Partition)
            {
                await _outboxStore.PutAsync(typeDefinitionTableName, currentState, cancellationToken);
                context.Logger.LogDebug(
                    "Outbox state changed to  {@OutboxState} for {DatabaseTable}",
                    new
                    {
                        currentState.Partition,
                        currentState.PartitionRecords
                    },
                    typeDefinitionTableName);
            }
        }
    }

    private async Task PublishOutboxEventsAsync(
        OutboxContext context,
        IReadOnlyCollection<SerializationResult<IEvent?>> outboxSerializationResults,
        CancellationToken cancellationToken)
    {
        var publishedEventsCount = 0;
        foreach (var serializationResult in outboxSerializationResults)
        {
            if (!serializationResult.Succeed)
            {
                _logger.LogWarning("Failed to deserialize outbox event {@OutboxEvent}", serializationResult);
                continue;
            }

            if (_outboxCache.ContainsKey(
                    string.Format(context.CacheKeyFormat, serializationResult.Value!.Metadata.IdempotenceKey)))
            {
                continue;
            }

            var publisher = _publishers.GetOrAdd(
                serializationResult.Value.TypeInfo,
                typeInfo =>
                {
                    var publisherType = typeof(IPublisher<>).MakeGenericType(Type.GetType(typeInfo)!);

                    return (IPublisher) context.ServiceProvider.GetRequiredService(publisherType);
                });

            await publisher.PublishAsync(serializationResult.Value, cancellationToken);
            publishedEventsCount++;

            _outboxCache.AddMemoryCacheEntry(
                string.Format(context.CacheKeyFormat, serializationResult.Value.Metadata.IdempotenceKey));
        }

        if (outboxSerializationResults.Count > 0)
            context.Logger.LogInformation(
                "Published {OutboxEventsCount} {MaterializedView} outbox events",
                publishedEventsCount,
                context.TypeDefinitionCache.TypeDefinition!.TableName);
    }

    private async Task<DatabaseOutboxResult> GetDatabaseOutboxRecords(
        OutboxContext context,
        OutboxStateResult? outboxState)
    {
        do
        {
            if (outboxState == null)
            {
                context.Logger.LogDebug("Outbox offset is empty, fetching events from the beginning");

                return await _outboxEventProvider.FetchEvents(context, outboxState);
            }

            var databaseOutboxResult = await _outboxEventProvider.FetchEvents(context, outboxState);

            if (databaseOutboxResult.Entities.Count == 0)
            {
                var nextPartition = outboxState.Partition.AddMinutes(1);
                context.Logger.LogDebug(
                    "No outbox events found for {OutboxPartition} partition, requesting events from next partition {NextPartition}",
                    outboxState.Partition,
                    nextPartition);

                outboxState = new OutboxStateResult(nextPartition, default, default);
            }
            else
            {
                context.Logger.LogDebug(
                    "Found {OutboxRecordsCount} outbox events for {OutboxPartition} partition",
                    databaseOutboxResult.Entities.Count,
                    outboxState.Partition);

                return databaseOutboxResult;
            }
        } while (outboxState.Partition <= OutboxStateResult.CurrentState().Partition);

        return DatabaseOutboxResult.Empty(outboxState.Partition);
    }

    private async Task<NatsKVEntry<OutboxStateResult?>> GetOutboxStateEntryAsync(
        OutboxContext context,
        CancellationToken cancellationToken)
    {
        var outboxStateEntry = await _outboxStore.GetEntryIfExistsAsync<OutboxStateResult>(
            context.TypeDefinitionCache.TypeDefinition!.TableName,
            cancellationToken);

        context.Logger.LogDebug("Fetched outbox offset: {@OutboxOffset}", outboxStateEntry.Value);

        return outboxStateEntry;
    }
}