namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

using Abstractions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

public class OutboxHostedService : BackgroundService
{
    private readonly ILogger _logger;
    private readonly IReadOnlyCollection<IOutboxWorker> _workers;

    public OutboxHostedService(
        ILogger<OutboxHostedService> logger,
        IServiceProvider serviceProvider,
        OutboxServiceConfiguration outboxServiceConfiguration)
    {
        _logger = logger;
        _workers = outboxServiceConfiguration.OutboxServices.Select(
                x => (IOutboxWorker) Activator.CreateInstance(
                    typeof(OutboxWorker<>).MakeGenericType(x.Key),
                    serviceProvider,
                    x.Value)!)
            .ToList();
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("OutboxHostedService is starting");
        foreach (var worker in _workers)
        {
            worker.StartAsync(stoppingToken);
        }

        return Task.CompletedTask;
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        foreach (var worker in _workers)
        {
            worker.Dispose();
        }

        return Task.CompletedTask;
    }
}