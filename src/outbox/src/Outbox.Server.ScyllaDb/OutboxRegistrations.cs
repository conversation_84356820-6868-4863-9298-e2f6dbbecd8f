namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

using Abstractions;
using Database.ScyllaDb.Mapping.Extensions;
using Hosting.Common.Models;
using MessageBus.Hosting.Extensions;
using MessageBus.Nats.KeyValue;
using Microsoft.Extensions.DependencyInjection;
using NATS.Client.KeyValueStore;
using Serialization;
using Server.Abstractions;

public static class OutboxRegistrations
{
    public const string KvStoreName = "outbox";

    public static IServiceCollection AddOutboxServices(
        this IServiceCollection services,
        ServerConfiguration serverConfiguration,
        Action<OutboxServiceConfiguration> configure)
    {
        OutboxServiceConfiguration outboxServiceConfiguration = new();
        serverConfiguration.BindServerConfig(OutboxConfiguration.SectionName, outboxServiceConfiguration.Configuration);
        configure(outboxServiceConfiguration);

        if (!outboxServiceConfiguration.Configuration.Enabled) return services;

        var natsConnectionOptions = new NatsConnectionOptions(serverConfiguration.ServerConfigSection);
        return services.AddSingleton(outboxServiceConfiguration)
            .AddNatsKeyValueStorage(
                serverConfiguration,
                options => options.AddStore(
                    new NatsKVConfig(KvStoreName)
                    {
                        Compression = true,
                        History = 1,
                        NumberOfReplicas = natsConnectionOptions.NumberOfNodes
                    }))
            .AddMemoryCache()
            .AddSingleton(typeof(IOutboxCache<>), typeof(NatsKvOutboxCache<>))
            .AddSingleton(typeof(IOutboxWorkerHandler<>), typeof(OutboxWorkerHandler<>))
            .AddSingleton<IOutboxEventProvider, ScyllaDbOutboxEventProvider>()
            .AddSingleton<IOutboxSerializer, SystemTextOutboxSerializer>()
            .AddSingleton(typeof(TypeDefinitionCache<>))
            .AddHostedService<OutboxHostedService>();
    }
}