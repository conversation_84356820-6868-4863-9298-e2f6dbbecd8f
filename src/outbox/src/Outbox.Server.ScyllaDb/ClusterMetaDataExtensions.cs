using Cassandra;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

namespace Kukui.Infrastructure.Outbox.Server.ScyllaDb;

public static class ClusterMetaDataExtensions
{
    public static MaterializedViewMetadata GetOutboxMaterializedViewMetadata(this Metadata clusterMetadata,
        ITypeDefinitionCache typeDefinitionCache)
    {
        var materializedViewName = $"outbox_{typeDefinitionCache.TypeDefinition!.TableName}";
        var keyspaceName = typeDefinitionCache.TypeDefinition.KeyspaceName;
        var outboxMaterializedView =
            clusterMetadata.GetMaterializedView(keyspaceName, materializedViewName);

        if (outboxMaterializedView == null)
        {
            throw new InvalidOperationException(
                $"Materialized view '{materializedViewName}' not found in keyspace '{keyspaceName}'");
        }

        if (outboxMaterializedView.PartitionKeys.Length > 1 ||
            !outboxMaterializedView
                .PartitionKeys.Select(x => x.Name)
                .Contains(typeDefinitionCache.GetColumn(nameof(IOutboxEntity.OutboxPartition))))
        {
            throw new InvalidOperationException(
                $"Materialized view '{materializedViewName}' must have a single partition key and the partition key must be '{nameof(IOutboxEntity.OutboxPartition)}'");
        }

        return outboxMaterializedView;
    }
}