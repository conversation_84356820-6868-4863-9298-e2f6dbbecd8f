namespace Kukui.Infrastructure.Outbox.Server.Serialization;

public class OutboxSerializationException : Exception
{
    public DateTime Partition { get; }

    public string JsonEvents { get; }

    public OutboxSerializationException(DateTime partition, string jsonEvents, Exception? innerException = null) : base(
        "Error during serialization/deserialization of outbox events",
        innerException)
    {
        Partition = partition;
        JsonEvents = jsonEvents;
    }
}