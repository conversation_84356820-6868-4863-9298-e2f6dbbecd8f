using System.Text.Json;
using System.Text.Json.Serialization;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;

namespace Kukui.Infrastructure.Outbox.Server.Serialization;

public class OutboxJsonConverter : JsonConverter<IEvent>
{
    public override IEvent? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var element = JsonElement.ParseValue(ref reader);
        var typeInfo = element
            .GetProperty("$type")
            .GetString();
        var convertType = Type.GetType(typeInfo!);

        if (convertType == null)
        {
            return null;
        }

        return (IEvent?)element.Deserialize(convertType, options);
    }

    public override void Write(Utf8JsonWriter writer, IEvent value, JsonSerializerOptions options)
    {
        JsonSerializer.Serialize(writer, value, Type.GetType(value.TypeInfo)!, options);
    }
}