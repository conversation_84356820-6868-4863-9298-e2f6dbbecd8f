namespace Kukui.Infrastructure.Outbox.Server.Serialization;

using System.Text.Json;
using System.Text.Json.Serialization;
using MessageBus.Abstractions.Events;

public class OutboxJsonResultConverter : JsonConverter<SerializationResult<IEvent>>
{
    public override SerializationResult<IEvent> Read(
        ref Utf8JsonReader reader,
        Type typeToConvert,
        JsonSerializerOptions options)
    {
        var element = JsonElement.ParseValue(ref reader);
        var typeInfo = element.GetProperty("$type").GetString();
        var convertType = Type.GetType(typeInfo!);

        if (convertType == null)
        {
            return new SerializationResult<IEvent>(element.GetRawText());
        }

        return new SerializationResult<IEvent>((IEvent?) element.Deserialize(convertType, options));
    }

    public override void Write(Utf8JsonWriter writer, SerializationResult<IEvent> value, JsonSerializerOptions options)
    {
        if (value.Value == null)
        {
            throw new ArgumentNullException(nameof(value), "Unable to serialize null outbox event");
        }

        JsonSerializer.Serialize(writer, value.Value, Type.GetType(value.Value.TypeInfo)!, options);
    }
}