namespace Kukui.Infrastructure.Outbox.Server.Serialization;

using System.Text.Json;
using System.Text.Json.Serialization;
using MessageBus.Abstractions.Events;

public class SystemTextOutboxSerializer : IOutboxSerializer
{
    public static JsonSerializerOptions Options { get; } = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingDefault,
        Converters =
        {
            new JsonStringEnumConverter(JsonNamingPolicy.CamelCase),
            new OutboxJsonConverter(),
            new OutboxJsonResultConverter()
        }
    };

    public void Serialize(IEnumerable<IEvent> messages)
    {
        JsonSerializer.Serialize(messages, Options);
    }

    public T? Deserialize<T>(string outboxEvents)
    {
        return JsonSerializer.Deserialize<T>(outboxEvents, Options);
    }

    public ValueTask<T?> DeserializeAsync<T>(Stream stream, CancellationToken cancellationToken)
    {
        return JsonSerializer.DeserializeAsync<T>(stream, Options, cancellationToken);
    }

    public T? Deserialize<T>(ReadOnlySpan<byte> span)
    {
        return JsonSerializer.Deserialize<T>(span, Options);
    }
}