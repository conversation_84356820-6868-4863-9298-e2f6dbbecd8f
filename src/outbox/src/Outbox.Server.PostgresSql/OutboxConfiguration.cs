namespace Kukui.Infrastructure.Outbox.Server.PostgresSql;

using Microsoft.EntityFrameworkCore;
using Relational.Models;

public static class OutboxConfiguration
{
    public static ModelBuilder AddOutboxConfiguration(this ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<OutboxError>(
            entity =>
            {
                entity.ToTable("outbox_errors");

                entity.HasKey(e => e.Id);

                entity.Property(x => x.Id)
                    .HasColumnName("id")
                    .ValueGeneratedNever();

                entity.Property(x => x.EventName)
                    .HasColumnName("event_name")
                    .IsRequired();

                entity.Property(e => e.EventJson)
                    .HasColumnType("jsonb")
                    .HasColumnName("event_json")
                    .IsRequired();

                entity.Property(e => e.DateCreated)
                    .HasColumnName("date_created")
                    .IsRequired();
            });

        modelBuilder.Entity<OutboxEvent>(
            entity =>
            {
                entity.ToTable("outbox_events");

                entity.HasKey(e => e.Id);

                entity.Property(x => x.Id)
                    .HasColumnName("id")
                    .ValueGeneratedOnAdd();

                entity.Property(x => x.EventName)
                    .HasColumnName("event_name")
                    .IsRequired();

                entity.Property(e => e.EventJson)
                    .HasColumnType("jsonb")
                    .HasColumnName("event_json")
                    .IsRequired();

                entity.Property(e => e.DateCreated)
                    .HasColumnName("date_created")
                    .IsRequired();
            });

        modelBuilder.Entity<OutboxOffset>(
            entity =>
            {
                entity.ToTable("outbox_offset");

                entity.HasKey(e => e.Id);

                entity.Property(x => x.Id)
                    .HasColumnName("id")
                    .ValueGeneratedNever();
            });

        return modelBuilder;
    }
}