namespace Kukui.Infrastructure.Outbox.Server.PostgresSql;

using Microsoft.EntityFrameworkCore;
using Relational;

public class OutboxDbContext : OutboxDbContextBase
{
    public OutboxDbContext(DbContextOptions options) : base(options)
    {
    }

    protected virtual void OnDbModelCreating(ModelBuilder modelBuilder)
    {
    }

    protected sealed override void OnOutboxModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.AddOutboxConfiguration();
        OnDbModelCreating(modelBuilder);
    }
}