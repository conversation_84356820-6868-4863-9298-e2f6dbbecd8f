namespace Sample.Outbox.Events;

using Kukui.Infrastructure.MessageBus.Abstractions.Events;

public class BusinessEntityCreatedEvent : EventBase<OutboxCoreStream>
{
    public BusinessEntityCreatedEvent(string id) : base(id)
    {
    }

    public string Name { get; set; }

    public string AdminEmail { get; set; }
}

public class BusinessEntityUpdatedEvent : EventBase<OutboxCoreStream>
{
    public BusinessEntityUpdatedEvent(string id) : base(id)
    {
    }

    public string Name { get; set; }
}

public class LocationCreatedEvent : EventBase<OutboxCoreStream>
{
    public LocationCreatedEvent(string id) : base(id)
    {
    }

    public string Name { get; set; }

    public string PosId { get; set; }
}

public class LocationUpdatedEvent : EventBase<OutboxCoreStream>
{
    public LocationUpdatedEvent(string id) : base(id)
    {
    }

    public string Name { get; set; }

    public string PosId { get; set; }
}