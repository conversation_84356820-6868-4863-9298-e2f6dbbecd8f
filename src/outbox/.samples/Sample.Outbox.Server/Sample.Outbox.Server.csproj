<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\..\..\database\src\ScyllaDb.Hosting.Extensions\ScyllaDb.Hosting.Extensions.csproj" />
    <ProjectReference Include="..\..\..\message-bus\src\MessageBus.Nats.KeyValue\MessageBus.Nats.KeyValue.csproj" />
    <ProjectReference Include="..\..\..\outbox\src\Outbox.Server.ScyllaDb\Outbox.Server.ScyllaDb.csproj" />
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Server\Kukui.Hosting.Server.csproj" />
    <ProjectReference Include="..\Sample.Outbox.Events\Sample.Outbox.Events.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="configs\**">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>