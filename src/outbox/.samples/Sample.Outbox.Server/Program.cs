using Cassandra;
using Cassandra.Data.Linq;
using Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;
using Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;
using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.Hosting.Server;
using Kukui.Infrastructure.MessageBus.Nats.JetStream;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;
using Microsoft.Extensions.DependencyInjection;
using Sample.Outbox.Events;
using Sample.Outbox.Server;

void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services.AddScyllaDb(configuration, options => options.AddMappings(typeof(Program).Assembly))
        .AddNatsJetStream(configuration, options => options.RegisterConsumersFromAssemblyContaining<Program>())
        .AddOutboxServices(
            configuration,
            options =>
            {
                options.AddOutboxEntity<LocationEntity>();
                // Register all entities from the assembly using default configuration
                //options.AddOutboxEntity<BusinessEntity>(
                //    OutboxConfiguration.Disable()); // Register a specific entity with custom configuration
            });
}

void ConfigureServices(IServiceProvider serviceProvider, ServerConfiguration serverConfiguration)
{
    var session = serviceProvider.GetRequiredService<ISession>();
    var entities = session.GetTable<BusinessEntity>().Execute();

    // foreach (var businessEntity in entities)
    // {
    //     businessEntity.AddOutboxEvents(new BusinessEntityCreatedEvent(businessEntity.Id.ToString()));
    //
    //     session.GetTable<BusinessEntity>()
    //         .Where(x => x.Id == businessEntity.Id && x.Name == businessEntity.Name)
    //         .Select(
    //             x => new BusinessEntity
    //             {
    //                 OutboxEvents = businessEntity.OutboxEvents,
    //                 OutboxPartition = businessEntity.OutboxPartition
    //             })
    //         .Update()
    //         .Execute();
    // }
    //
    var locations = session.GetTable<LocationEntity>().Take(1).Execute();

    foreach (var locationEntity in locations)
    {
        locationEntity.AddOutboxEvents(new LocationCreatedEvent(locationEntity.Id.ToString()));
        session.GetTable<LocationEntity>()
            .Where(x => x.Id == locationEntity.Id && x.Name == locationEntity.Name)
            .UpdateWithOutboxEvents(
                locationEntity,
                x => new LocationEntity
                {
                    Enabled = false
                })
            .Execute();
    }
}

await ServerHostRunner.RunApp(args, RegisterServices, ConfigureServices);