namespace Sample.Outbox.Server;

using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

public class LocationEntityMapping : IEntityMapping<LocationEntity>
{
    public void Configure(IEntityMappingBuilder<LocationEntity> builder)
    {
        builder.WithTable(
                "location",
                "core",
                map => map.PartitionKey(x => x.Id)
                    .ClusteringKey(x => x.Name))
            .WithUdtMap(
                x => x.Address,
                map => map.WithName("location_address", "core")
                    .AutoMap())
            .WithUdtMap(
                x => x.Address.Coordinates,
                map => map.WithName("geo_coordinate", "core")
                    .AutoMap())
            .AutoMapColumns(
                options => options.Column(x => x.CreatedDate, map => map.WithName("created_date_time"))
                    .Column(x => x.ModifiedDate, map => map.WithName("modified_date_time")))
            .WithCollectionIndex(x => x.ExternalIdentification, CollectionIndexKind.Values)
            .WithCollectionIndex(x => x.NameSearch, CollectionIndexKind.Values)
            .WithCollectionIndex(x => x.CpNameSearch, CollectionIndexKind.Values);
    }
}