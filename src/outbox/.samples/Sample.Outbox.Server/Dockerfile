FROM mcr.microsoft.com/dotnet/runtime:8.0 AS base
USER $APP_UID
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Sample.Outbox.Server/Sample.Outbox.Server.csproj", "Sample.Outbox.Server/"]
RUN dotnet restore "Sample.Outbox.Server/Sample.Outbox.Server.csproj"
COPY . .
WORKDIR "/src/Sample.Outbox.Server"
RUN dotnet build "Sample.Outbox.Server.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Sample.Outbox.Server.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Sample.Outbox.Server.dll"]
