namespace Sample.Outbox.Server.Consumers;

using Events;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class LocationEntityCreatedConsumer : IEventConsumer<LocationCreatedEvent>
{
    private readonly ILogger<LocationEntityCreatedConsumer> _logger;

    public LocationEntityCreatedConsumer(ILogger<LocationEntityCreatedConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<LocationCreatedEvent> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "{Consumer} consumed event {EventType} ",
            nameof(LocationEntityCreatedConsumer),
            nameof(LocationCreatedEvent));

        return Task.CompletedTask;
    }
}