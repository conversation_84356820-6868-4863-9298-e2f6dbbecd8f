namespace MSO.Retention.Infrastructure.Database.EntityModels.UserDefinedTypes;

public class RecipientFilters
{
    public int? LastVisitStart { get; set; }

    public int? LastVisitEnd { get; set; }

    public decimal? MinSpent { get; set; }

    public decimal? MaxSpent { get; set; }

    public IEnumerable<string> VehicleMakes { get; set; }

    public bool ExcludeFleetAccounts { get; set; }

    public override bool Equals(object? obj)
    {
        if (obj == null || !(obj is RecipientFilters)) return false;

        var objToCompare = (RecipientFilters) obj;

        if (LastVisitStart != objToCompare.LastVisitStart || LastVisitEnd != objToCompare.LastVisitEnd ||
            MinSpent != objToCompare.MinSpent || MaxSpent != objToCompare.MaxSpent ||
            ExcludeFleetAccounts != objToCompare.ExcludeFleetAccounts || !Enumerable.SequenceEqual(
                VehicleMakes ?? [],
                objToCompare.VehicleMakes ?? []))
            return false;

        return true;
    }

    public override int GetHashCode()
    {
        var hashCode = new HashCode();
        hashCode.Add(LastVisitStart);
        hashCode.Add(LastVisitEnd);
        hashCode.Add(MinSpent);
        hashCode.Add(MaxSpent);
        hashCode.Add(ExcludeFleetAccounts);

        if (VehicleMakes != null)
            foreach (var make in VehicleMakes)
                hashCode.Add(make);

        return hashCode.ToHashCode();
    }
}