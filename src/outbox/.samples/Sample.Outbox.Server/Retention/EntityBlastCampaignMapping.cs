namespace MSO.Retention.Infrastructure.Database.EntityMappings;

using Cassandra.Mapping;
using EntityModels;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using static Constants;

public class EntityBlastCampaignMapping : IEntityMapping<EntityBlastCampaign>
{
    public void Configure(IEntityMappingBuilder<EntityBlastCampaign> builder)
    {
        builder.WithTable(
                "entity_blast_campaigns",
                RetentionKeyspace,
                map => map.PartitionKey(x => x.EntityId)
                    .ClusteringKey(x => x.Id, SortOrder.Descending))
            .WithUdtMap(
                x => x.RecipientFilters,
                map => map.WithName("recipient_filters", RetentionKeyspace)
                    .AutoMap())
            .WithUdtMap(
                x => x.CampaignSettings,
                map => map.WithName("campaign_settings", RetentionKeyspace)
                    .AutoMap())
            .AutoMapColumns(
                options => options.Column(x => x.RecipientFilters, map => map.WithName("recipients_filters")))
            .WithCollectionIndex(x => x.SearchKeywords, CollectionIndexKind.Values)
            .WithMaterializedView<EntityBlastCampaignOutboxView>(
                "outbox_entity_blast_campaigns",
                RetentionKeyspace,
                map => map.PartitionKey(x => x.OutboxPartition)
                    .ClusteringKey(x => x.EntityId)
                    .ClusteringKey(x => x.Id, SortOrder.Ascending));
    }
}