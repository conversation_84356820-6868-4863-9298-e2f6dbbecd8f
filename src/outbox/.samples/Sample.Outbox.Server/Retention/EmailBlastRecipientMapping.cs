namespace MSO.Retention.Infrastructure.Database.EntityMappings;

using Cassandra.Mapping;
using EntityModels;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using static Constants;

public class EmailBlastRecipientMapping : IEntityMapping<EmailBlastRecipient>
{
    public void Configure(IEntityMappingBuilder<EmailBlastRecipient> builder)
    {
        builder.WithTable(
                "email_blast_recipients",
                RetentionKeyspace,
                map => map.PartitionKey(x => x.LocationCampaignId)
                    .ClusteringKey(x => x.EmailAddress)
                    .ClusteringKey(x => x.Id))
            .AutoMapColumns()
            .WithUdtMap(
                x => x.Vehicles,
                map => map.WithName("customer_vehicle", RetentionKeyspace)
                    .AutoMap())
            .WithMaterializedView<EmailRecipientOutboxView>(
                "outbox_email_blast_recipients",
                RetentionKeyspace,
                map => map.PartitionKey(x => x.OutboxPartition)
                    .ClusteringKey(x => x.LocationCampaignId, SortOrder.Ascending)
                    .Clustering<PERSON>ey(x => x.Id, SortOrder.Ascending));
    }
}