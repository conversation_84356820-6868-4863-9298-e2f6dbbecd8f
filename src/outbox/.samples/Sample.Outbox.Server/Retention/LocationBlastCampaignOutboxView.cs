namespace MSO.Retention.Infrastructure.Database.EntityModels;

using Cassandra;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

public abstract class LocationBlastCampaignOutboxView : IOutboxEntity
{
    public TimeUuid EntityCampaignId { get; set; }

    public TimeUuid Id { get; set; }

    public DateTime? OutboxPartition { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }
}