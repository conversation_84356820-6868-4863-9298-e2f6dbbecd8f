namespace MSO.Retention.Infrastructure.Database.EntityModels;

using Cassandra;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;
using UserDefinedTypes;

public class EmailBlastRecipient : IOutboxEntity
{
    public EmailBlastRecipient()
    {
        Id = TimeUuid.NewId();
    }

    public TimeUuid EntityCampaignId { get; set; }

    public TimeUuid LocationCampaignId { get; set; }

    public TimeUuid Id { get; set; }

    public string EmailAddress { get; set; }

    public string CustomerId { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public bool FleetAccount { get; set; }

    public IEnumerable<CustomerVehicle> Vehicles { get; set; }

    public DateTimeOffset? LastVisit { get; set; }

    public decimal? TotalAmountSpent { get; set; }

    public bool Read { get; set; }

    public DateTimeOffset? ReadDate { get; set; }

    public bool Converted { get; set; }

    public DateTimeOffset? ConvertedDate { get; set; }

    public bool Unsubscribed { get; set; }

    public DateTimeOffset? UnsubscribedDate { get; set; }

    public bool Reviewed { get; set; }

    public DateTimeOffset? ReviewedDate { get; set; }

    public bool EmailFailed { get; set; }

    public DateTimeOffset? EmailFailedDate { get; set; }

    /// <summary>
    /// Either INSERTED | UPDATED | DELETED
    /// </summary>
    public string State { get; set; }

    public bool Validated { get; set; }

    public DateTime? OutboxPartition { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }
}