namespace MSO.Retention.Infrastructure.Database.EntityModels;

using Cassandra;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

public abstract class EmailRecipientOutboxView : IOutboxEntity
{
    public DateTime? OutboxPartition { get; set; }

    public TimeUuid Id { get; set; }

    public TimeUuid LocationCampaignId { get; set; }

    public string EmailAddress { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }
}