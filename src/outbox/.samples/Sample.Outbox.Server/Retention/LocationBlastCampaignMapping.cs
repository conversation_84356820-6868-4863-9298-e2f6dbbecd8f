namespace MSO.Retention.Infrastructure.Database.EntityMappings;

using Cassandra.Mapping;
using EntityModels;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using static Constants;

public class LocationBlastCampaignMapping : IEntityMapping<LocationBlastCampaign>
{
    public void Configure(IEntityMappingBuilder<LocationBlastCampaign> builder)
    {
        builder.WithTable(
                "location_blast_campaigns",
                RetentionKeyspace,
                map => map.PartitionKey(x => x.EntityCampaignId)
                    .ClusteringKey(x => x.Id, SortOrder.Descending))
            .AutoMapColumns()
            .WithMaterializedView<LocationBlastCampaignOutboxView>(
                "outbox_location_blast_campaigns",
                RetentionKeyspace,
                map => map.PartitionKey(x => x.OutboxPartition)
                    .ClusteringKey(x => x.EntityCampaignId)
                    .ClusteringKey(x => x.Id, SortOrder.Ascending))
            .WithGlobalIndex(x => x.Id)
            .WithLocalIndex(x => x.LocationId);
    }
}