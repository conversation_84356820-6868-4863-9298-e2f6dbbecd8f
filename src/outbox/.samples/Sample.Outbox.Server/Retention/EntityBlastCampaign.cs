namespace MSO.Retention.Infrastructure.Database.EntityModels;

using Cassandra;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;
using UserDefinedTypes;

public class EntityBlastCampaign : IOutboxEntity
{
    public TimeUuid Id { get; set; }

    public Guid EntityId { get; set; }

    public Guid TemplateId { get; set; }

    public Guid EventId { get; set; }

    public ISet<Guid> Locations { get; set; }

    public ISet<string> Groups { get; set; }

    public ISet<Guid> GroupSet { get; set; }

    public string Name { get; set; }

    public string CategoryName { get; set; }

    public string EventName { get; set; }

    public ICollection<string> SearchKeywords { get; set; }

    public string Status { get; set; }

    public string State { get; set; }

    public string Body { get; set; }

    public string Subject { get; set; }

    public string SenderDisplayName { get; set; }

    public string SenderEmail { get; set; }

    public DateTimeOffset? SendDate { get; set; }

    public RecipientFilters? RecipientFilters { get; set; }

    public CampaignSettings? CampaignSettings { get; set; }

    public int RecipientsCount { get; set; }

    public int SentEmailsCount { get; set; }

    public int OpenedCount { get; set; }

    public int ClickedCount { get; set; }

    public int UnsubscribedCount { get; set; }

    public decimal? RevenueGenerated { get; set; }

    public bool Enabled { get; set; }

    public bool Deleted { get; set; }

    public DateTimeOffset CreatedDate { get; set; }

    public DateTimeOffset? ModifiedDate { get; set; }

    public DateTime? OutboxPartition { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }

    public override bool Equals(object? obj)
    {
        if (obj == null || obj is not EntityBlastCampaign) return false;

        var objToCompare = (EntityBlastCampaign) obj;

        if (Body != objToCompare.Body || Name != objToCompare.Name || SendDate != objToCompare.SendDate ||
            SenderDisplayName != objToCompare.SenderDisplayName || SenderEmail != objToCompare.SenderEmail ||
            Status != objToCompare.Status || Subject != objToCompare.Subject ||
            (RecipientFilters == null && objToCompare.RecipientFilters != null) ||
            (RecipientFilters != null && !RecipientFilters.Equals(objToCompare.RecipientFilters)) ||
            (CampaignSettings == null && objToCompare.CampaignSettings != null) ||
            (CampaignSettings != null && !CampaignSettings.Equals(objToCompare.CampaignSettings)))
            return false;

        return true;
    }

    public override int GetHashCode()
    {
        var hashCode = new HashCode();

        hashCode.Add(Id);
        hashCode.Add(EntityId);
        hashCode.Add(Body);
        hashCode.Add(Name);
        hashCode.Add(SendDate);
        hashCode.Add(SenderDisplayName);
        hashCode.Add(SenderEmail);
        hashCode.Add(Status);
        hashCode.Add(Subject);
        if (RecipientFilters != null) hashCode.Add(RecipientFilters);

        if (CampaignSettings != null) hashCode.Add(CampaignSettings);

        return hashCode.ToHashCode();
    }
}