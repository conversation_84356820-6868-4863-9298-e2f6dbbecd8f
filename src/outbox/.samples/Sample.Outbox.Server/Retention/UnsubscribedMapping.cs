namespace MSO.Retention.Infrastructure.Database.EntityMappings;

using EntityModels;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using static Constants;

public class UnsubscribedMapping : IEntityMapping<Unsubscribed>
{
    public void Configure(IEntityMappingBuilder<Unsubscribed> builder)
    {
        builder.WithTable(
                "unsubscribed",
                RetentionKeyspace,
                map => map.PartitionKey(x => x.EntityId)
                    .ClusteringKey(x => x.EmailAddress))
            .AutoMapColumns()
            .WithUdtMap(
                x => x.InactiveVehicles,
                map => map.WithName("customer_vehicle", RetentionKeyspace)
                    .AutoMap());
    }
}