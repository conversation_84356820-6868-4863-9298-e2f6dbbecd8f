namespace MSO.Retention.Infrastructure.Database.EntityModels;

using Cassandra;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

public record LocationBlastCampaign : IOutboxEntity
{
    public TimeUuid Id { get; set; }

    public Guid EntityId { get; set; }

    public TimeUuid EntityCampaignId { get; set; }

    public Guid LocationId { get; set; }

    public string Status { get; set; }

    public string State { get; set; }

    public int RecipientsCount { get; set; }

    public int SentEmailsCount { get; set; }

    public int OpenedCount { get; set; }

    public int ClickedCount { get; set; }

    public int UnsubscribedCount { get; set; }

    public int FailedEmailsCount { get; set; }

    public decimal? RevenueGenerated { get; set; }

    public bool Enabled { get; set; }

    public bool Deleted { get; set; }

    public DateTimeOffset CreatedDate { get; set; }

    public DateTimeOffset? ModifiedDate { get; set; }

    public DateTime? OutboxPartition { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }
}