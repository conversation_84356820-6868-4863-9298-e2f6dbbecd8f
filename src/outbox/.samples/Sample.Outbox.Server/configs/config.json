{"DisplayName": "Sample ScyllaDb Application", "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"ServerUrl": "http://localhost:5341", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}, {"Name": "File", "Args": {"path": "logs/application.log", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "fileSizeLimitBytes": "104857600", "restrictedToMinimumLevel": "Warning", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}]}}, "Server": {"DataBase": {"ConnectionString": "Cluster Name=local-cluster;Contact Points=host.docker.internal:9042", "DefaultPageSize": 120, "DefaultKeyspace": "core", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}, "Metrics": {"Enabled": false}}, "MessageService": {"Enabled": true, "ConsumerPrefix": "sample-outbox-server", "Url": "nats://host.docker.internal:4222", "Username": "nats-user", "Password": "password"}, "Outbox": {"Enabled": true, "Interval": "00:00:00.10", "Limit": 100}, "Vault": {"Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}]}}}