namespace Sample.Outbox.Server;

using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

public class LocationEntity : IOutboxEntity
{
    public Guid Id { get; set; }

    public string Name { get; set; }

    public Guid? EntityId { get; set; }

    public List<string> GroupNames { get; set; }

    public string Timezone { get; set; }

    public LocationAddress Address { get; set; }

    public bool Enabled { get; set; }

    public DateTimeOffset CreatedDate { get; set; }

    public DateTimeOffset? ModifiedDate { get; set; }

    public Dictionary<string, int> ExternalIdentification { get; set; }

    public List<string> CpNameSearch { get; set; }

    public List<string> NameSearch { get; set; }

    public sbyte CpIndex { get; set; }

    public DateTime? OutboxPartition { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }
}