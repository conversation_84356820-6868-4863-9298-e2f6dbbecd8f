namespace Sample.Outbox.Server;

using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using Kukui.Infrastructure.Outbox.Server.ScyllaDb.Abstractions;

public record BusinessEntity : IOutboxEntity
{
    public Guid Id { get; set; }

    public string Name { get; set; }

    public string AdminEmail { get; set; }

    public bool Enabled { get; set; }

    public DateTimeOffset CreatedDate { get; set; }

    public DateTimeOffset? ModifiedDate { get; set; }

    public List<string> NameSearch { get; set; }

    public DateTime? OutboxPartition { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }
}

public class BusinessEntityMapping : IEntityMapping<BusinessEntity>
{
    public void Configure(IEntityMappingBuilder<BusinessEntity> builder)
    {
        builder.WithTable(
                "business_entity",
                "core",
                map => map.PartitionKey(x => x.Id)
                    .ClusteringKey(x => x.Name))
            .AutoMapColumns(
                options => options.Column(x => x.CreatedDate, map => map.WithName("created_date_time"))
                    .Column(x => x.ModifiedDate, map => map.WithName("modified_date_time")));
    }
}

public abstract class OutboxView : IOutboxEntity
{
    public DateTime? OutboxPartition { get; set; }

    public string? OutboxEvents { get; set; }

    public int OutboxEventsCount { get; set; }
}