{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Server": {"MessageService": {"Enabled": true, "Url": "nats://host.docker.internal:4222,nats://host.docker.internal:4223,nats://host.docker.internal:4224", "Username": "cp-admin", "Password": "password"}, "Outbox": {"Enabled": true, "Interval": "00:00:01", "DbContext": ["RetentionDbContext"]}}}