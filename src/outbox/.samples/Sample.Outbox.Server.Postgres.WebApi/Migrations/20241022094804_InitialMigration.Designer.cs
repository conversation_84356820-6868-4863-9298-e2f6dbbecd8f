// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using Sample.Outbox.Server.Postgres.WebApi;

#nullable disable

namespace Sample.Outbox.Server.Postgres.WebApi.Migrations
{
    [DbContext(typeof(RetentionDbContext))]
    [Migration("20241022094804_InitialMigration")]
    partial class InitialMigration
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Outbox.Server.Relational.Models.OutboxError", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("DateCreated")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_created");

                    b.Property<string>("EventJson")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("event_json");

                    b.Property<string>("EventName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("event_name");

                    b.HasKey("Id");

                    b.ToTable("outbox_errors", (string)null);
                });

            modelBuilder.Entity("Outbox.Server.Relational.Models.OutboxEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTimeOffset>("DateCreated")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("date_created");

                    b.Property<string>("EventJson")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("event_json");

                    b.Property<string>("EventName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("event_name");

                    b.HasKey("Id");

                    b.ToTable("outbox_events", (string)null);
                });

            modelBuilder.Entity("Outbox.Server.Relational.Models.OutboxOffset", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer")
                        .HasColumnName("id");

                    b.HasKey("Id");

                    b.ToTable("outbox_offset", (string)null);
                });

            modelBuilder.Entity("Sample.Outbox.Server.Postgres.WebApi.EmailCampaign", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("LocationId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("EmailCampaigns");
                });
#pragma warning restore 612, 618
        }
    }
}
