using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;
using Kukui.Infrastructure.MessageBus.Abstractions.Streams;
using Kukui.Infrastructure.Outbox.Server.Relational;
using Microsoft.EntityFrameworkCore;
using Sample.Outbox.Server.Postgres.WebApi;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddDbContext<RetentionDbContext>(
    options => options.UseNpgsql("Host=localhost;Database=retention;Username=********;Password=********"));

builder.Services.AddOutboxServices<RetentionDbContext>(new ServerConfiguration(builder.Configuration));
var app = builder.Build();

if (app.Environment.IsDevelopment())
{
}

app.MapPost(
    "campaigns",
    (EmailCreateRequest request, RetentionDbContext context) =>
    {
        var email = new EmailCampaign
        {
            Id = Guid.NewGuid(),
            Name = request.Name,
            LocationId = request.LocationId
        };
        context.Add(email);
        context.AddOutboxEvents(new EmailBlastCreated(email.Id.ToString(), email.Name, email.LocationId));
        context.SaveChanges();
        return Results.Created($"/campaigns/{email.Id}", email);
    });
app.UseHttpsRedirection();

var context = app.Services
    .CreateScope()
    .ServiceProvider
    .GetRequiredService<RetentionDbContext>();
await context.Database.MigrateAsync();

app.Run();

public record EmailCreateRequest(int LocationId, string Name);

public class EmailBlastCreated : EventBase<RetentionTestStream>
{
    public EmailBlastCreated(string id, string name, int locationId) : base(id)
    {
        Name = name;
        LocationId = locationId;
    }

    public string Name { get; set; }

    public int LocationId { get; set; }
}


public class RetentionTestStream : NatsStreamBase
{
}