namespace Sample.Outbox.Server.Postgres.WebApi;

using Kukui.Infrastructure.Outbox.Server.PostgresSql;
using Microsoft.EntityFrameworkCore;

public class RetentionDbContext : OutboxDbContext
{
    public RetentionDbContext(DbContextOptions<RetentionDbContext> options) : base(options)
    {
    }

    public virtual DbSet<EmailCampaign> EmailCampaigns { get; set; }

    protected override void OnDbModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<EmailCampaign>().ToTable("EmailCampaigns");
    }
}