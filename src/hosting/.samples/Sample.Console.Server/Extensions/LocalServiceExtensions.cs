using Kukui.Infrastructure.Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Sample.Console.Server.Services.Migration;

namespace Sample.Console.Server.Extensions;

public static class LocalServiceExtensions
{
    public static IServiceCollection AddLocalServices(this IServiceCollection services,
        ServerConfiguration configuration)
    {
        return services
            .AddSingleton<IMigrationService, MigrationService>();
    }
}