using Microsoft.Extensions.Logging;

namespace Sample.Console.Server.Services.Migration;

public interface IMigrationService
{
    void Execute();
}

public class MigrationService : IMigrationService
{
    private readonly ILogger<MigrationService> _logger;

    public MigrationService(ILogger<MigrationService> logger)
    {
        _logger = logger;
    }

    public void Execute()
    {
        _logger.LogInformation("Executing migration...");
    }
}