{"DisplayName": "Sample Console Server", "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Debug"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Kukui.Infrastructure.Hosting.Serilog.Formatters.DatadogJsonFormatter, Kukui.Infrastructure.Hosting.Serilog"}}]}}, "Server": {"DataBase": {"ConnectionString": "secret", "DefaultPageSize": 100, "DefaultKeyspace": "retention", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}, "Metrics": {"Enabled": false}}, "MessageService": {"Url": "nats://host.docker.internal", "Username": "nats-user", "Password": "secret"}, "Vault": {"Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"Name": "database", "ConfigurationSectionName": "server:Database", "Keys": [{"Source": "retention-scylladb", "Target": "ConnectionString"}]}, {"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}