using Kukui.Infrastructure.Hosting.Serilog.Formatters;
using Kukui.Infrastructure.Hosting.Server;
using Microsoft.Extensions.Configuration;
using Sample.Console.Server.Extensions;
using Serilog;

var configuration = new ConfigurationBuilder().AddJsonFile("configs/config.json").Build();
var loggerConfig = new LoggerConfiguration().WriteTo.Console(new DatadogJsonFormatter());

await ServerHostRunner.RunApp(
    args,
    (services, config) => services.AddLocalServices(config),
    serilogConfig: config => config.Enrich.WithMachineName());

//await ServerHostRunner.RunAppAsync<ServerConfigurator>(args);

// void ConfigureServices(IServiceProvider serviceProvider, ServerConfiguration serverConfiguration)
// {
//     if (serverConfiguration.Environment.IsDevelopment())
//     {
//         var migrationService = serviceProvider.GetRequiredService<IMigrationService>();
//         migrationService.Execute();
//     }
// }
//
// async Task ConfigureServicesAsync(IServiceProvider serviceProvider, ServerConfiguration serverConfiguration)
// {
//     if (serverConfiguration.Environment.IsDevelopment())
//     {
//         await Task.CompletedTask;
//         var migrationService = serviceProvider.GetRequiredService<IMigrationService>();
//         migrationService.Execute();
//     }
// }
//
// void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
// {
//     services.AddLocalServices(configuration);
// }