namespace Sample.Console.Server;

using Extensions;
using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.Hosting.Server;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Services.Migration;

public class ServerConfigurator : ServerConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, ServerConfiguration serverConfig)
    {
        services.AddLocalServices(serverConfig);
    }

    public override async Task ConfigureServicesAsync(
        IServiceProvider serviceProvider,
        ServerConfiguration serverConfiguration)
    {
        if (serverConfiguration.Environment.IsDevelopment())
        {
            await Task.CompletedTask;
            var migrationService = serviceProvider.GetRequiredService<IMigrationService>();
            migrationService.Execute();
        }
    }

    public override void ConfigureSerilog(LoggerConfiguration configuration)
    {
        configuration.Enrich.WithMachineName();
    }
}