namespace Sample.MinimalApi.Features.Customers;

using Kukui.Infrastructure.Hosting.Web.MinimalApi;
using Kukui.Infrastructure.Hosting.Web.MinimalApi.RouterBuilderExtensions;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using MinimalApi.Endpoints.Customers.GetCustomersById;

public static class Endpoints
{
    public static IEndpointRouteBuilder RegisterCustomerEndpoints(this IEndpointRouteBuilder app)
    {
        var customerGroups = app.MapGroup("/customers");

        customerGroups.MapGetWithResponse<Results<Ok<GetCustomersResponse>, NotFound>>("/").WithOpenApi();
        customerGroups.MapGet<GetCustomerRequest>("/{id:int}").WithOpenApi();

        customerGroups.MapGet<GetCustomerRequest, Ok<GetCustomerResponseResult>>("/result/{id:int}")
            .AddEndpointFilter<ValidationErrorFilter>()
            .WithOpenApi();
        app.MapGet<GetOrdersByLocationIdRequest>("/orders")
            .WithName("get-orders-by-location-id")
            .WithDescription("Get orders by location id")
            .WithOpenApi();
        return app;
    }
}

public class GetOrdersByLocationIdRequest : IWebRequest
{
    [FromQuery] public int LocationId { get; set; }

    public HttpContext HttpContext { get; set; }
}

public class GetOrdersByLocationIdRequestHandler : IWebRequestHandler<GetOrdersByLocationIdRequest>
{
    public async Task<IResult> Handle(GetOrdersByLocationIdRequest request, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return Results.Ok();
    }
}

public class GetOrdersByCustomerIdRequest : IWebRequest
{
    [FromQuery] public int CustomerId { get; set; }

    public HttpContext HttpContext { get; set; }
}

public class GetOrdersByCustomerIdRequestHandler : IWebRequestHandler<GetOrdersByCustomerIdRequest>
{
    public async Task<IResult> Handle(GetOrdersByCustomerIdRequest request, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return Results.Ok();
    }
}

public class GetCustomersResponse
{
}

public class GetCustomersRequestHandler : IWebHandler<Results<Ok<GetCustomersResponse>, NotFound>>
{
    public async Task<Results<Ok<GetCustomersResponse>, NotFound>> Handle(
        WebRequest<Results<Ok<GetCustomersResponse>, NotFound>> request,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return TypedResults.Ok(new GetCustomersResponse());
    }
}