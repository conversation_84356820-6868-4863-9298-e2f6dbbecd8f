namespace Sample.MinimalApi.Endpoints.Customers.GetCustomersById;

using Kukui.Infrastructure.Hosting.Web.MinimalApi;
using Microsoft.AspNetCore.Http.HttpResults;

public class GetCustomerRequestHandlerResult : IWebRequestHandler<GetCustomerRequest, Ok<GetCustomerResponseResult>>
{
    public async Task<Ok<GetCustomerResponseResult>> Handle(
        GetCustomerRequest request,
        CancellationToken cancellationToken)
    {
        await Task.Delay(100, cancellationToken);

        return TypedResults.Ok(new GetCustomerResponseResult($"Customer {request.CustomerId}"));
    }
}