namespace Sample.MinimalApi.Endpoints.Customers.GetCustomersById;

using Kukui.Infrastructure.Hosting.Web.MinimalApi;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;

public record GetCustomerRequest([FromRoute(Name = "id")] int CustomerId) : IWebRequest,
    IWebRequest<Ok<GetCustomerResponseResult>>;

public record CustomerResponse(string Name);

public record GetCustomerResponseResult(string Name);