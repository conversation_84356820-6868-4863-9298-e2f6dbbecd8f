namespace Sample.MinimalApi.Features.Clients;

using CreateClient;
using GetCustomers;
using Kukui.Infrastructure.Hosting.Web.MinimalApi.RouterBuilderExtensions;

public static class Endpoints
{
    public static IEndpointRouteBuilder RegisterClientsEndpoints(this IEndpointRouteBuilder endpoints)
    {
        var clients = endpoints.MapGroup("/clients").WithOpenApi();

        clients.MapGet<GetClientsRequest>("{id:int}");

        clients.MapPostWithParameters<CreateClientRequest>("{locationId:int}").WithDisplayName("Create client");

        return endpoints;
    }
}