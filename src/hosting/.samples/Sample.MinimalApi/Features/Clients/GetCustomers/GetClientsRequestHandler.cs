namespace Sample.MinimalApi.Features.Clients.GetCustomers;

using Kukui.Infrastructure.Hosting.Web.MinimalApi;

public class GetClientsRequestHandler : IWebRequestHandler<GetClientsRequest>
{
    public Task<IResult> Handle(GetClientsRequest request, CancellationToken cancellationToken)
    {
        var result = request.Package == Package.None ? Results.BadRequest("Package is required") : Results.Ok(request);

        return Task.FromResult(result);
    }
}