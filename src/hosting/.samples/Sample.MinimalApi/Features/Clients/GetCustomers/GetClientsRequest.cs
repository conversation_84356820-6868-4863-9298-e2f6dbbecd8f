namespace Sample.MinimalApi.Features.Clients.GetCustomers;

using Kukui.Infrastructure.Hosting.Web.MinimalApi;
using Microsoft.AspNetCore.Mvc;

public class GetClientsRequest : IWebRequest
{
    [FromRoute]
    public int Id { get; set; }

    [FromQuery(Name = "pkg")]
    public Package Package { get; set; }

    public bool? Paging { get; set; } = true;
}

public enum Package
{
    None = 0,
    Crm = 1,
    Premium = 2,
    WebSiteOnly = 3
}