namespace Sample.MinimalApi;

using Features.Clients;
using Features.Customers;
using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.Hosting.Web.Common;
using Kukui.Infrastructure.Hosting.Web.MinimalApi;
using Scalar.AspNetCore;

public class WebServerConfigurator : WebConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddMinimalApiServices(options =>
            options.ConfigureSerializerOptions(SerializationOptions.SetDefaultOptions));
    }

    protected override void RegisterApplicationEndpoints(IEndpointRouteBuilder endpoints)
    {
        endpoints.RegisterCustomerEndpoints().RegisterClientsEndpoints();
    }

    protected override void ConfigureScalar(ScalarOptions options)
    {
        options.WithTheme(ScalarTheme.Purple);
    }
}