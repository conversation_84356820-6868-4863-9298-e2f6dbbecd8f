{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "", "applicationUrl": "http://localhost:5500", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "scalar/v1", "applicationUrl": "https://localhost:5501;http://localhost:5500", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}