FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY [".samples/Sample.WebApi.Server/Sample.WebApi.Server.csproj", ".samples/Sample.WebApi.Server/"]
RUN dotnet restore ".samples/Sample.WebApi.Server/Sample.WebApi.Server.csproj"
COPY . .
WORKDIR "/src/.samples/Sample.WebApi.Server"
RUN dotnet build "Sample.WebApi.Server.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "Sample.WebApi.Server.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Sample.WebApi.Server.dll"]
