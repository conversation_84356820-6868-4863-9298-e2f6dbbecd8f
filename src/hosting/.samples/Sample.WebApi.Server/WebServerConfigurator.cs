namespace Sample.WebApi.Server;

using Extensions;
using HealthChecks.MassTransit.RabbitMq;
using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.Hosting.Web.Common;
using MassTransit;
using Microsoft.Extensions.Options;

public class WebServerConfigurator : WebConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddMassTransit(x => x.UsingRabbitMq());
        services.AddApplicationDependencies()
            .AddHttpClient<ExternalServiceClient>(httpClient =>
            {
                httpClient.BaseAddress = new Uri(
                    configuration.ServerConfiguration
                        .GetExternalServiceConfiguration(ExternalServiceClient.ServiceName)
                        .Url);
            });
    }


    protected override void ConfigureApplicationDefinedHealthChecks(
        IHealthChecksBuilder healthChecksBuilder,
        WebServerConfiguration configuration)
    {
        healthChecksBuilder.AddMassTransitRabbitMq();
    }

    public override void ConfigureServices(IServiceProvider serviceProvider, WebServerConfiguration configuration)
    {
        var clientConfig = configuration.ServerConfiguration.GetInternalServiceConfiguration("AuthClient");
    }

    public override async Task ConfigureServicesAsync(
        IServiceProvider serviceProvider,
        WebServerConfiguration configuration)
    {
        await Task.CompletedTask;
        //var clientConfig = configuration.ServerConfiguration.GetInternalServiceConfiguration("AuthClient");
        var config = serviceProvider.GetService<IOptions<MassTransitHostOptions>>();
    }
}

public class ExternalServiceClient
{
    public const string ServiceName = "AuthApi";
    private readonly HttpClient _httpClient;

    public ExternalServiceClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }
}