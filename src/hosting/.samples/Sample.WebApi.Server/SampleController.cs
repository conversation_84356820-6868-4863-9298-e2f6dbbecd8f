namespace Sample.WebApi.Server;

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

[ApiController]
[Route("/api/[controller]")]
public class SampleController : ControllerBase
{
    private static readonly Dictionary<int, TodoItem> _cache = new();

    [HttpGet("todo")]
    [Description("get todo item by id")]
    [ProducesResponseType(typeof(TodoItem), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public IActionResult Get(int id)
    {
        if (!_cache.TryGetValue(id, out var value))
        {
            throw new InvalidOperationException("Todo item not found.");
        }

        return Ok(value);
    }

    [HttpPost("todos")]
    [Description("get todo item by id")]
    [ProducesResponseType(typeof(TodoItem), StatusCodes.Status200OK)]
    public IActionResult Create([FromBody] TodoItem item)
    {
        if (!_cache.TryAdd(item.Id, item))
        {
            return BadRequest("Todo item with the same id already exists.");
        }

        return CreatedAtAction(nameof(Get), new { id = item.Id }, item);
    }

    public class TodoItem
    {
        public int Id { get; set; }

        [Required] public string Name { get; set; } = null!;

        [DefaultValue(false)] public bool IsComplete { get; set; }
    }
}