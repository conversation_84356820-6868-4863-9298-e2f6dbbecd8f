FROM mcr.microsoft.com/dotnet/aspnet:8.0

# Open Container Initiative (OCI) labels (See https://github.com/opencontainers/image-spec/blob/master/annotations.md).
LABEL org.opencontainers.image.title="Test WebServer App " \
    org.opencontainers.image.description="Test WebServer App Description"

ARG source
WORKDIR /app
COPY ${source:-obj/Docker/publish} .
ENTRYPOINT ["dotnet", "Template.WebServerApp.dll"]