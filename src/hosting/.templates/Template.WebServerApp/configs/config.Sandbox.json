{"Security": {"Authentication": {"IdentityServerHost": "http://docker-sandbox.server.cluster:8100/auth/realms/ARX"}}, "Kestrel": {"Endpoints": {"HttpsDefaultCert": {"Url": "https://*:5210"}}, "Certificates": {"Default": {"Path": "/https/server.cluster.pfx", "Password": "secret"}}}, "Logging": {"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}}}, "Server": {"Vault": {"Enabled": true, "Address": "http://docker-sandbox.server.cluster:8200"}}}