{"DisplayName": "Template Web Server App", "Description": "Some basic description", "Security": {"EnforceHttps": false, "AllowedHosts": "*", "AllowedCorsOrigins": ["http://localhost", "https://localhost", "https://*.server.cluster", "https://*.kukui.com"], "Authentication": {"Enabled": true, "IdentityServerHost": "https://localhost"}}, "Kestrel": {"AddServerHeader": true, "Endpoints": {"Http": {"Url": "http://*:5200"}}}, "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Information", "Cassandra": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Json.JsonFormatter"}}]}}, "Server": {"DataBase": {"ConnectionString": "secret"}, "Vault": {"MountPath": "kukui", "Username": "kukui-read-only", "Sections": [{"Name": "https", "ConfigurationSectionName": "Kestrel:Certificates:<PERSON><PERSON><PERSON>", "Keys": [{"Source": "certificate-password", "Target": "Password"}]}]}}}