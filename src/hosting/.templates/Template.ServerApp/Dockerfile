FROM mcr.microsoft.com/dotnet/runtime:8.0

# Open Container Initiative (OCI) labels (See https://github.com/opencontainers/image-spec/blob/master/annotations.md).
LABEL org.opencontainers.image.title="Template WebServer App" \
    org.opencontainers.image.description="Template WebServer App Description"

ARG source
WORKDIR /app
COPY ${source:-obj/Docker/publish} .
ENTRYPOINT ["dotnet", "TemplateServerApp.dll"]