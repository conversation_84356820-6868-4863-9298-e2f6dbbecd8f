{"Logging": {"Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning"}}}}, "Server": {"MessageService": {"Url": "nats://docker-sandbox.server.cluster:4222,nats://docker-sandbox.server.cluster:4223,nats://docker-sandbox.server.cluster:4224", "Username": "cp-admin"}, "Vault": {"Enabled": true, "Address": "http://docker-sandbox.server.cluster:8200/"}}}