{"DisplayName": "Server App Template", "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information", "System": "Warning", "Cassandra": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Json.JsonFormatter"}}]}}, "Server": {"DataBase": {"ConnectionString": "secret"}, "MessageService": {"ConsumerPrefix": "core-server", "Url": "host.docker.internal", "Username": "nats-user", "Password": "secret"}, "Outbox": {"Enabled": true, "Interval": "00:00:01"}, "Vault": {"Sections": [{"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}