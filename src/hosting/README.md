# Introduction

This repository main purpose is to provide unified hosting for all of our projects. It's aim is to deal with all
cross-cutting concerns of:

- Hosting
- Logging
- Secrets management
- Health checks, Authentication, CORS, HTTPS,
- Messaging with NATS
- Registration and using Cassandra C# driver.
- testing coming soon using [TestContainers.NET](https://dotnet.testcontainers.org/)

## Hosting

Two hosting models are supported at the moment:

- Server applications(background services that are doing some work in background). For example replication servers that
  consume messages and update the state in the database.
- Web applications that are serving HTTP requests. For example REST API.

### Server applications

<h5 a><strong><code>Program.cs</code></strong></h5>

```csharp
void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services.AddLocalServices(configuration);
}

void ConfigureServices(IServiceProvider serviceProvider, ServerConfiguration serverConfiguration)
{
    if (serverConfiguration.Environment.IsDevelopment())
    {
        var migrationService = serviceProvider.GetRequiredService<IMigrationService>();
        migrationService.Execute();
    }
}

await ServerHostRunner.RunApp(args, RegisterServices, ConfigureServices);
```

### Web applications

<h5 a><strong><code>Program.cs</code></strong></h5>

```csharp
await WebHostRunner.RunApp<WebServerConfigurator>(args);
```

<h5 a><strong><code>WebServerConfigurator.cs</code></strong></h5>

```csharp
public class WebServerConfigurator : WebConfiguratorBase
{
    protected override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services
            .AddApplicationDependencies();
    }

    public override void ConfigureWebApplicationBuilder(WebApplicationBuilder builder)
    {
    }

    public override void ConfigureServices(IServiceProvider serviceProvider, WebServerConfiguration configuration)
    {
    }

    protected override void ConfigureApplication(
        IApplicationBuilder app,
        IHostApplicationLifetime hostApplicationLifetime,
        WebServerConfiguration configuration
    )
    {
    }

    protected override void ConfigureApplicationDefinedHealthChecks(IHealthChecksBuilder healthChecksBuilder)
    {
        healthChecksBuilder
            .AddSqlServer("Server=.;Database=master;Trusted_Connection=True;");
    }

    protected override void ConfigureApplicationDefinedEndpoints(IEndpointRouteBuilder endpoints)
    {
    }
}
```

### Minimal Api

- Add code organization that support minimal api structure leveraging Endpoints and vertical slice architecture.
  and [Request-Endpoint-Response Pattern](https://deviq.com/design-patterns/repr-design-pattern).
- No controllers just requests -> endpoints -> handlers -> responses
- Handlers are implemented using MediatR
- Validation is automatically done using `FluentValidation` library and `IPipelineBehavior<,>` support in MediatR
- It's customizable and other pipelines can be added if needed.

```csharp
public class WebServerConfigurator : WebConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddMinimalApiServices();
    }

    protected override void RegisterApplicationEndpoints(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapLocationsEndpoints().MapClientEndpoints();
    }
}
```

- All endpoints configuration related to some Feature like `Locations`, `Users`, `Orders` etc. are placed in
  `Features` folder.'

```csharp
public static class Endpoints
{
    public static IEndpointRouteBuilder MapLocationsEndpoints(this IEndpointRouteBuilder endpoints)
    {
        var group = endpoints.MapGroup("/locations").WithOpenApi().WithTags("locations");

        group
            .MapGetWithHandler<GetLocationsHandler>("") // no parameters just handler regisration
            .WithName("Fetch all locations")
            .Produces<GetLocationsResponse>(contentType: "application/json");

        group.MapGet<GetLocationByNameRequest>("") // with parameters can ommit the return type(It will be of type `IResult`
             .WithName("Fetch location by location name")
             .Produces<GetLocationResponse>(contentType: "application/json");

       group.MapGet<GetLocationByIdRequest, Ok<GetLocationByIdResponse>>("/{id:int}") // explicitly specify the return type
            .WithName("Fetch location by location id")
            .Produces<GetLocationResponse>(contentType: "application/json");

        return endpoints;
    }
}
```

- Each request should be of either types:
    - `IWebRequest` and corresponding handler of type `IWebRequestHandler<TRequest> where TRequest : IWebRequest`.
    - `IWebRequest<TResponse>` and corresponding handler of
      type `IWebRequestHandler<TRequest, TResponse> where TRequest : IWebRequest<TResponse>`.

- When there is no request parameters and just want a handler to be executed then use `MapGetWithResponse<TResponse>`
  extension method .

 ```csharp
    app.MapGetWithResponse<Ok<GetCustomersResponse>>("/customers) where TResponse : IResult;
```

or using `Results<T1, T2, ...>` discriminated union types.

```csharp
    app.MapGetWithResponse<Results<Ok<GetCustomersResponse>, NotFound>>("/")
```

and handler should implement the following interface

```csharp
    public class GetCustomersRequestHandler
        : IWebHandler<Results<Ok<GetCustomersResponse>, NotFound>>
    {
        public async Task<Results<Ok<GetCustomersResponse>, NotFound>> Handle(
            WebRequest<Results<Ok<GetCustomersResponse>, NotFound>> request,
            CancellationToken cancellationToken)
        {
            await Task.CompletedTask;
            return TypedResults.Ok(new GetCustomersResponse());
        }
    }
```