using System.Runtime.Serialization;

namespace Kukui.Infrastructure.Hosting.Common.Vault;

[Serializable]
public sealed class VaultConfigurationException : Exception
{
    public string? VaultAddress { get; set; }

    public string? MountPath { get; set; }

    public string? Username { get; set; }

    public VaultConfigurationException(VaultOptions vaultOptions, Exception? innerException = null)
        : base("Failed to connect to Vault server", innerException)
    {
        VaultAddress = vaultOptions.Address;
        MountPath = vaultOptions.MountPath;
        Username = vaultOptions.Username;
    }

    public VaultConfigurationException(string message) : base(message)
    {
    }

    private VaultConfigurationException(SerializationInfo info, StreamingContext context) : base(info, context)
    {
    }

    public VaultConfigurationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}