// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Kukui.Infrastructure.Hosting.Common.Vault;

using Microsoft.Extensions.Configuration;
using VaultSharp;
using VaultSharp.V1.AuthMethods.UserPass;

public class VaultOptions
{
    public VaultOptions(IConfigurationSection vaultSection)
    {
        vaultSection.Bind(this);
    }

    public bool Enabled { get; set; }

    public TimeSpan ReloadInterval { get; set; }

    public VaultClientSettings? ClientSettings
    {
        get
        {
            if (string.IsNullOrWhiteSpace(Address))
            {
                throw new VaultConfigurationException(
                    "server:Vault:Address is missing or empty, but is required. Verify whether you are referencing the right \"ENVIRONMENT\" variable.");
            }

            if (string.IsNullOrWhiteSpace(PassPhrase))
            {
                throw new VaultConfigurationException(
                    "server:Vault:PassPhrase is missing or empty, but is required. Verify whether you have \"secrets.json\" file in the root of the project.");
            }

            return new VaultClientSettings(Address, new UserPassAuthMethodInfo(Username, PassPhrase));
        }
    }


    public string Address { get; set; }

    public string MountPath { get; set; } = "kukui";

    public string Username { get; set; } = "kukui-read-only";

    public string PassPhrase { get; set; }

    public VaultSection[] Sections { get; set; } = [];
}