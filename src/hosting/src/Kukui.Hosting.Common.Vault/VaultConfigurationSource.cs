using Microsoft.Extensions.Configuration;

namespace Kukui.Infrastructure.Hosting.Common.Vault;

public class VaultConfigurationSource : IConfigurationSource
{
    public IReadOnlyVaultClient VaultClient { get; }

    public IVaultWatcher? VaultReloadWatcher { get; }

    public VaultConfigurationSource(
        IReadOnlyVaultClient vaultClient,
        IVaultWatcher? vaultReloadWatcher = null)
    {
        VaultClient = vaultClient;
        VaultReloadWatcher = vaultReloadWatcher;
    }

    public IConfigurationProvider Build(IConfigurationBuilder builder)
    {
        return new VaultConfigurationProvider(this);
    }
}