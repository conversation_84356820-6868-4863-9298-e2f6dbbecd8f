namespace Kukui.Infrastructure.Hosting.Common.Vault;

using System.Collections.ObjectModel;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using VaultSharp;
using VaultSharp.V1.Commons;

public class VaultReadClient : VaultClient, IReadOnlyVaultClient
{
    private static readonly IReadOnlyDictionary<string, object> EmptyData = new Dictionary<string, object>();

    public VaultReadClient(VaultOptions vaultOptions) : base(vaultOptions.ClientSettings)
    {
        VaultOptions = vaultOptions;
    }

    public VaultOptions VaultOptions { get; }

    public async Task<IReadOnlyDictionary<string, object>> ReadSecret(
        string pathName,
        string? mountPoint,
        int? version = null)
    {
        Secret<SecretData>? response = await V1.Secrets.KeyValue.V2.ReadSecretAsync(pathName, version, mountPoint);

        return response.Data?.Data == null ? EmptyData : new ReadOnlyDictionary<string, object>(response.Data.Data);
    }

    public async Task<HealthCheckResult> GetHealthStatusAsync()
    {
        if (!VaultOptions.Enabled)
        {
            return HealthCheckResult.Healthy("Vault is disabled");
        }

        var health = await V1.System.GetHealthStatusAsync().ConfigureAwait(false);

        return health.Initialized switch
        {
            true when health.Sealed => HealthCheckResult.Unhealthy("Vault is sealed"),
            false => HealthCheckResult.Unhealthy("Vault is not initialized"),
            _ => HealthCheckResult.Healthy("Vault is healthy")
        };
    }
}