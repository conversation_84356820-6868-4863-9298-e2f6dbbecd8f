namespace Kukui.Infrastructure.Hosting.Common.Vault;

using Microsoft.Extensions.Diagnostics.HealthChecks;

public class VaultHealthCheck
{
    private readonly VaultReadOnlyClientFactory _vaultClientFactory;

    public VaultHealthCheck(VaultReadOnlyClientFactory vaultClientFactory)
    {
        _vaultClientFactory = vaultClientFactory;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        return await _vaultClientFactory.CreateClient().GetHealthStatusAsync();
    }
}