namespace Kukui.Infrastructure.Hosting.Common.Vault;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Serilog;

public class VaultConfigurationProvider : ConfigurationProvider
{
    private static readonly object LockToken = new();
    private readonly VaultConfigurationSource _configurationSource;

    public VaultConfigurationProvider(VaultConfigurationSource source)
    {
        _configurationSource = source;
        if (source.VaultClient.VaultOptions.ReloadInterval > TimeSpan.Zero)
        {
            ChangeToken.OnChange(
                () => _configurationSource.VaultReloadWatcher!.Watch(),
                () =>
                {
                    try
                    {
                        Load();
                    }
                    catch (Exception e)
                    {
                        Log.Warning(e, "Failed to reload configuration from Vault");
                    }
                });
        }

        GetReloadToken();
    }

    public override void Load()
    {
        lock (LockToken)
        {
            LoadAsync(_configurationSource).GetAwaiter().GetResult();
        }
    }

    private async Task LoadAsync(VaultConfigurationSource configurationSource)
    {
        if (!configurationSource.VaultClient.VaultOptions.Sections.Any())
        {
            return;
        }

        foreach (var section in configurationSource.VaultClient.VaultOptions.Sections)
        {
            await LoadSectionData(section);
        }
    }

    private async Task LoadSectionData(VaultSection section)
    {
        if (string.IsNullOrEmpty(section.Name))
        {
            throw new VaultConfigurationException("server:Vault:Sections:Name is missing or empty, but is required.");
        }

        if (!section.Keys.Any())
        {
            return;
        }

        try
        {
            var secrets = await _configurationSource.VaultClient.ReadSecret(
                section.Name,
                _configurationSource.VaultClient.VaultOptions.MountPath);

            foreach (var key in section.Keys)
            {
                if (key == null || string.IsNullOrEmpty(key.Source))
                {
                    throw new VaultConfigurationException(
                        "One or more vault sections are malformed. Please check the 'Source' property.");
                }

                var srcValue = secrets[key.Source!]?.ToString();

                if (!string.IsNullOrEmpty(key.ValueSuffix))
                {
                    srcValue += key.ValueSuffix;
                }

                var configurationKey = $"{section.GetConfigurationSectionName()}:{key.Target}";
                Data[configurationKey] = srcValue;
            }
        }
        catch (Exception ex)
        {
            throw new VaultConfigurationException(
                $"An error occurred while trying to read configuration from: {_configurationSource.VaultClient.VaultOptions.Address}. Section in question: {section}",
                ex);
        }
    }
}