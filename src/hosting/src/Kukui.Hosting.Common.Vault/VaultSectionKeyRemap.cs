// ReSharper disable UnusedMember.Global

namespace Kukui.Infrastructure.Hosting.Common.Vault;

public class VaultSectionKeyRemap
{
    private string? _target;
    public string? Source { get; set; }

    public string? Target
    {
        get => string.IsNullOrEmpty(_target)
            ? Source
            : _target;
        set => _target = value;
    }

    public string? ValueSuffix { get; set; }

    public override string ToString()
    {
        var suffix = string.IsNullOrEmpty(ValueSuffix)
            ? string.Empty
            : "->" + ValueSuffix;

        return $"{Source}->{Target}{suffix}";
    }
}