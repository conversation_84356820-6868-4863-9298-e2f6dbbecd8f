// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Kukui.Infrastructure.Hosting.Common.Vault;

public class VaultSection
{
    public string Name { get; set; }

    public string? ConfigurationSectionName { get; set; }

    public VaultSectionKeyRemap[] Keys { get; set; } = [];

    public string? GetConfigurationSectionName() =>
        string.IsNullOrEmpty(ConfigurationSectionName) ? Name : ConfigurationSectionName;

    public override string ToString()
    {
        if (!Keys.Any())
        {
            return base.ToString();
        }

        var keys = string.Join(Environment.NewLine, Keys.Select(x => x?.ToString()));

        return $"{Name}->{GetConfigurationSectionName()}{Environment.NewLine}{keys}";
    }
}