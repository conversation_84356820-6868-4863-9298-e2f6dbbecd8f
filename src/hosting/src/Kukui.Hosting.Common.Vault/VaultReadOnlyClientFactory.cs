namespace Kukui.Infrastructure.Hosting.Common.Vault;

public class VaultReadOnlyClientFactory
{
    private readonly VaultOptions _vaultOptions;
    private IReadOnlyVaultClient? _vaultClient;

    public VaultReadOnlyClientFactory(VaultOptions vaultOptions)
    {
        _vaultOptions = vaultOptions;
    }

    public IReadOnlyVaultClient CreateClient()
    {
        return _vaultClient ??= new VaultReadClient(_vaultOptions);
    }
}