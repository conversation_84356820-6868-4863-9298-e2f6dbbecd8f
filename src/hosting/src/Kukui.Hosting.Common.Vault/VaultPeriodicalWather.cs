using Microsoft.Extensions.Primitives;

namespace Kukui.Infrastructure.Hosting.Common.Vault;

public interface IVaultWatcher : IDisposable
{
    IChangeToken Watch();
}

public sealed class VaultPeriodicalWatcher : IVaultWatcher
{
    private readonly Timer _timer;
    private CancellationTokenSource? _cancellationTokenSource;

    public VaultPeriodicalWatcher(TimeSpan refreshInterval)
    {
        _timer = new Timer(Change, null, TimeSpan.Zero, refreshInterval);
    }

    private void Change(object state)
    {
        _cancellationTokenSource?.Cancel();
    }

    public IChangeToken Watch()
    {
        _cancellationTokenSource = new CancellationTokenSource();
        return new CancellationChangeToken(_cancellationTokenSource.Token);
    }

    public void Dispose()
    {
        _timer.Dispose();
        _cancellationTokenSource?.Dispose();
    }
}