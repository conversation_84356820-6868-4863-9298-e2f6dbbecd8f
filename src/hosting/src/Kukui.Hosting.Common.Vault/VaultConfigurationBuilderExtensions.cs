using Microsoft.Extensions.Configuration;

namespace Kukui.Infrastructure.Hosting.Common.Vault;

public static class VaultConfigurationBuilderExtensions
{
    public static IConfigurationBuilder AddVault(
        this IConfigurationBuilder configurationBuilder,
        IConfigurationSection vaultSection
    )
    {
        VaultOptions vaultOptions = new(vaultSection);

        if (!vaultOptions.Enabled)
        {
            return configurationBuilder;
        }

        configurationBuilder.Add(
            new VaultConfigurationSource(
                new VaultReadClient(vaultOptions),
                new VaultPeriodicalWatcher(vaultOptions.ReloadInterval)
            ));

        return configurationBuilder;
    }
}