namespace Kukui.Infrastructure.Hosting.Web;

using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;

public class GlobalWebExceptionHandler : IExceptionHandler
{
    private readonly ILogger<GlobalWebExceptionHandler> _logger;

    public GlobalWebExceptionHandler(ILogger<GlobalWebExceptionHandler> logger)
    {
        _logger = logger;
    }

    public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
    {
        var problemDetails = new ProblemDetails
        {
            Instance = httpContext.Request.Path,
            Title = "Server error",
            Status = StatusCodes.Status500InternalServerError,
            Detail = exception.Message,
            Extensions = new Dictionary<string, object?>
            {
                { "traceId", httpContext.TraceIdentifier },
                { "routeValues", httpContext.Request.RouteValues },
                { "queryString", httpContext.Request.Query },
                { "userEmail", httpContext.User.FindFirstValue(ClaimTypes.Email) }
            }
        };

        _logger.LogError(exception, "Uncaught error occurred for request {@ProblemDetails}", problemDetails);

        await httpContext.Response.WriteAsJsonAsync(
            problemDetails,
            JsonSerializerOptions.Default,
            "application/problem+json; charset=utf-8",
            cancellationToken);

        return true;
    }
}