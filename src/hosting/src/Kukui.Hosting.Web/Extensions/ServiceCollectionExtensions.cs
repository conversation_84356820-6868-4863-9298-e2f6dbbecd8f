using static Kukui.Infrastructure.Hosting.Web.Common.WebServerConstants;

namespace Kukui.Infrastructure.Hosting.Web.Extensions;

using Authentication;
using Common;
using Hosting.Common.Extensions;
using Hosting.Common.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Server.Kestrel.Core;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection ConfigureWebServiceSettings(
        this IServiceCollection services,
        IWebHostEnvironment environment)
    {
        var serverConfig = services.GetRequiredService<ServerConfiguration>();
        WebServerConfiguration webServerSettings = new(serverConfig, environment);

        return services.AddSingleton(webServerSettings)
            .Configure<KestrelServerOptions>(serverConfig.Configuration.GetSection(KestrelConfigurationSection))
            .AddCorsPolicy(webServerSettings);
    }

    private static IServiceCollection AddCorsPolicy(
        this IServiceCollection services,
        WebServerConfiguration configuration)
    {
        CorsPolicyBuilder policyBuilder = new();
        policyBuilder.AllowAnyHeader()
            .AllowAnyMethod()
            .WithExposedHeaders("Content-Disposition");

        var enabledOrigins = configuration.SecuritySettings.AllowedOrigins;

        if (enabledOrigins.Length == 0)
        {
            policyBuilder.AllowAnyOrigin();
        }
        else
        {
            policyBuilder.WithOrigins(enabledOrigins)
                .SetIsOriginAllowedToAllowWildcardSubdomains();
        }

        var policy = policyBuilder.Build();

        return services.AddCors(corsOptions => corsOptions.AddPolicy(DefaultAppCorsPolicyName, policy));
    }

    public static IServiceCollection AddJwtAuthentication(
        this IServiceCollection services,
        AuthenticationSettings authentication,
        Action<AuthenticationBuilder>? configure = null)
    {
        if (!authentication.Enabled)
        {
            return services.AddSingleton<IAuthorizationHandler, AllowAnonymous>();
        }

        if (authentication.IdentityServerHost == default)
        {
            throw new InvalidOperationException(
                $"Authentication is enabled, but {nameof(authentication.IdentityServerHost)} is not set.");
        }

        // ReSharper disable once ComplexConditionExpression
        var authBuilder = services.AddAuthentication(
                options =>
                {
                    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                })
            .AddJwtBearer(
                JwtBearerDefaults.AuthenticationScheme,
                x =>
                {
                    x.Authority = authentication.IdentityServerHost;
                    x.TokenValidationParameters.ValidateAudience = false;
                    x.TokenValidationParameters.ValidateIssuer = false;
                    x.RequireHttpsMetadata = false;
                });

        configure?.Invoke(authBuilder);
        return services;
    }
}