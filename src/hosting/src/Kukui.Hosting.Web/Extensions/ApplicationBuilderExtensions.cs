using Kukui.Infrastructure.Hosting.Web.Common;

namespace Kukui.Infrastructure.Hosting.Web.Extensions;

public static class ApplicationBuilderExtensions
{
    public static IApplicationBuilder UseAppConfiguration(
        this IApplicationBuilder app,
        WebServerConfiguration configuration,
        IHostApplicationLifetime hostApplicationLifetime,
        Action<IApplicationBuilder, IHostApplicationLifetime, WebServerConfiguration> configure)
    {
        configure(app, hostApplicationLifetime, configuration);

        return app;
    }
}