namespace Kukui.Infrastructure.Hosting.Web.Extensions;

using System.Text.Json;
using HealthChecks.UI.Core;
using Hosting.Common.Models;
using Microsoft.Extensions.Diagnostics.HealthChecks;

public static class HealthCheckResponseWriter
{
    public static async Task WriteHealthCheckUIResponse(HttpContext context, HealthReport report)
    {
        var uiReport = UIHealthReport.CreateFrom(report);
        uiReport.Entries.Add(
            "version",
            new UIHealthReportEntry
            {
                Data = new Dictionary<string, object>
                {
                    { "TAG", Environment.GetEnvironmentVariable("TAG") ?? "N/A" }
                }
            });

        await JsonSerializer.SerializeAsync(context.Response.Body, uiReport, SerializationOptions.GetDefaultOptions())
            .ConfigureAwait(false);
    }
}