namespace Kukui.Infrastructure.Hosting.Web.Extensions;

using Common;

internal static class HealthCheckBuilderExtensions
{
    internal static IHealthChecksBuilder AddApplicationHealthChecks(
        this IHealthChecksBuilder healthChecksBuilder,
        WebServerConfiguration configuration,
        Action<IHealthChecksBuilder, WebServerConfiguration> configure)
    {
        configure(healthChecksBuilder, configuration);

        return healthChecksBuilder;
    }
}