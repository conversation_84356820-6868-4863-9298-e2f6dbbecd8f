using static Kukui.Infrastructure.Hosting.Web.Common.WebServerConstants;
using static Kukui.Infrastructure.Hosting.Common.EndpointConstants;

namespace Kukui.Infrastructure.Hosting.Web;

using Common;
using Common.Extensions;
using Extensions;
using global::Serilog;
using Hosting.Common.BaseConfigurators;
using Hosting.Common.Models;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Scalar.AspNetCore;

public abstract class WebConfiguratorBase : IConfigureServer<WebServerConfiguration>
{
    public abstract void RegisterServices(IServiceCollection services, WebServerConfiguration configuration);

    public virtual void ConfigureServices(IServiceProvider serviceProvider, WebServerConfiguration configuration)
    {
    }

    public virtual Task ConfigureServicesAsync(
        IServiceProvider serviceProvider,
        WebServerConfiguration configuration) =>
        Task.CompletedTask;

    public void RegisterServicesInternal(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddControllers()
            .AddJsonOptions(options => SerializationOptions.SetDefaultOptions(options.JsonSerializerOptions));
        services.AddExceptionHandler<GlobalWebExceptionHandler>();
        services.AddProblemDetails();
        services.AddEndpointsApiExplorer();
        services.AddOpenApiDocument(configuration);

        services.AddJwtAuthentication(configuration.SecuritySettings.Authentication);

        services.AddHealthChecks().AddApplicationHealthChecks(configuration, ConfigureApplicationDefinedHealthChecks);

        RegisterServices(services, configuration);
    }

    public void ConfigureRequestPipeline(
        IApplicationBuilder app,
        IHostApplicationLifetime hostApplicationLifetime,
        WebServerConfiguration configuration)
    {
        app.UseForwardedHeaders();
        app.UseExceptionHandler();
        app.UseOpenApi(options => options.Path = OpenApiJsonPath);
        app.UseSwaggerUi(options =>
        {
            options.DocumentPath = OpenApiJsonPath;
            options.Path = string.Empty;
        });

        app.UseRouting();
        app.UseCors(DefaultAppCorsPolicyName);

        if (configuration.SecuritySettings.EnforceHttps)
        {
            app.UseHttpsRedirection();
        }

        app.UseAuthentication();
        app.UseAuthorization();
        app.UseAppConfiguration(configuration, hostApplicationLifetime, ConfigureApplication);
        app.UseEndpoints(options =>
        {
            options.MapScalarEndpoints(configuration, ConfigureScalar);
            ConfigureEndpoints(options);
        });
    }

    protected virtual void ConfigureApplication(
        IApplicationBuilder app,
        IHostApplicationLifetime hostApplicationLifetime,
        WebServerConfiguration configuration)
    {
    }

    public virtual void ConfigureWebApplicationBuilder(WebApplicationBuilder builder)
    {
    }

    private static void ConfigureHealthChecksEndpoint(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapHealthChecks(
                HealthEndpointPath,
                new HealthCheckOptions
                {
                    ResponseWriter = HealthCheckResponseWriter.WriteHealthCheckUIResponse
                })
            .AllowAnonymous();
    }

    private void ConfigureEndpoints(IEndpointRouteBuilder endpoints)
    {
        endpoints.MapControllers();
        ConfigureHealthChecksEndpoint(endpoints);
        RegisterApplicationEndpoints(endpoints);
    }

    protected virtual void RegisterApplicationEndpoints(IEndpointRouteBuilder endpoints)
    {
    }

    protected virtual void ConfigureApplicationDefinedHealthChecks(
        IHealthChecksBuilder healthChecksBuilder,
        WebServerConfiguration configuration)
    {
    }

    protected virtual void ConfigureScalar(ScalarOptions options)
    {
    }

    public virtual void ConfigureSerilog(LoggerConfiguration configuration)
    {
    }
}