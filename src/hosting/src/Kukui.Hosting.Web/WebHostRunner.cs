namespace Kukui.Infrastructure.Hosting.Web;

using Common;
using Extensions;
using Hosting.Common;
using Hosting.Common.Extensions;

public static class WebHostRunner
{
    /// <summary>
    ///     When you want to run a server application, and use synchronous method to configure your services, migration, etc.
    /// </summary>
    /// <param name="args"></param>
    /// <param name="configureMinimalRequests"></param>
    /// <typeparam name="TConfigurator"></typeparam>
    /// <returns></returns>
    public static async Task<int> RunApp<TConfigurator>(
        string[] args,
        Action<WebApplication, WebServerConfiguration>? configureMinimalRequests = null)
        where TConfigurator : WebConfiguratorBase, new()
    {
        return await HostRunner.Run(() =>
            {
                var configurator = new TConfigurator();
                var app = ConfigureWebHost(configurator, args, configureMinimalRequests);
                configurator.ConfigureServices(app.Services, app.Services.GetRequiredService<WebServerConfiguration>());

                return HostRunner.ConfigureWithoutRunning ? Task.CompletedTask : app.RunAsync();
            })
            .ConfigureAwait(false);
    }

    /// <summary>
    ///     When you want to run a server application, and use asynchronous method to configure your services, migration, etc.
    /// </summary>
    /// <param name="args"></param>
    /// <param name="configureMinimalRequests"></param>
    /// <typeparam name="TConfigurator"></typeparam>
    /// <returns></returns>
    public static async Task<int> RunAppAsync<TConfigurator>(
        string[] args,
        Action<WebApplication, WebServerConfiguration>? configureMinimalRequests = null)
        where TConfigurator : WebConfiguratorBase, new()
    {
        return await HostRunner.Run(async () =>
            {
                var configurator = new TConfigurator();
                var app = ConfigureWebHost(configurator, args, configureMinimalRequests);
                await configurator.ConfigureServicesAsync(
                    app.Services,
                    app.Services.GetRequiredService<WebServerConfiguration>());

                await (HostRunner.ConfigureWithoutRunning ? Task.CompletedTask : app.RunAsync());
            })
            .ConfigureAwait(false);
    }

    private static WebApplication ConfigureWebHost<TConfigurator>(
        TConfigurator configurator,
        string[] args,
        Action<WebApplication, WebServerConfiguration>? configureMinimalRequests = null)
        where TConfigurator : WebConfiguratorBase, new()
    {
        var builder = CreateWebApplicationBuilder(args);

        builder.Host.ConfigureHosting(loggerConfig: configurator.ConfigureSerilog);
        builder.Services.ConfigureWebServiceSettings(builder.Environment);

        configurator.ConfigureWebApplicationBuilder(builder);
        var configuration = builder.Services.GetRequiredService<WebServerConfiguration>();
        configurator.RegisterServicesInternal(builder.Services, configuration);

        var app = builder.Build();

        configurator.ConfigureRequestPipeline(app, app.Lifetime, configuration);
        configureMinimalRequests?.Invoke(app, configuration);

        return app;
    }

    private static WebApplicationBuilder CreateWebApplicationBuilder(string[] args)
    {
        WebApplicationOptions webOptions = new() { Args = args };
        return WebApplication.CreateBuilder(webOptions);
    }
}