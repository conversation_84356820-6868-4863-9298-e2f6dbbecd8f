namespace Kukui.Infrastructure.Hosting.Server;

using Common;
using Common.BaseConfigurators;
using Common.Models;
using global::Serilog;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

public static class ServerHostRunner
{
    /// <summary>
    ///     When you want to run a server application, and use synchronous method to configure your services, migration, etc.
    /// </summary>
    /// <param name="args"></param>
    /// <param name="registerServices"></param>
    /// <param name="configureServices"></param>
    /// <param name="serilogConfig"></param>
    /// <returns></returns>
    public static Task<int> RunApp(
        string[] args,
        Action<IServiceCollection, ServerConfiguration> registerServices,
        Action<IServiceProvider, ServerConfiguration>? configureServices = null,
        Action<LoggerConfiguration>? serilogConfig = null) =>
        HostRunner.Run(CreateAppHostBuilder(args), registerServices, configureServices, serilogConfig);

    /// <summary>
    ///     When you want to run a server application and use asynchronous method to configure your services, migration, etc.
    /// </summary>
    /// <param name="args"></param>
    /// <param name="registerServices"></param>
    /// <param name="configureServices"></param>
    /// <returns></returns>
    public static Task<int> RunAppAsync(
        string[] args,
        Action<IServiceCollection, ServerConfiguration> registerServices,
        Func<IServiceProvider, ServerConfiguration, Task>? configureServices = null,
        Action<LoggerConfiguration>? serilogConfig = null) =>
        HostRunner.Run(CreateAppHostBuilder(args), registerServices, configureServices, serilogConfig);

    /// <summary>
    ///     When you want to run a server application, and use synchronous method to configure your services, migration, etc.
    /// </summary>
    /// <param name="args"></param>
    /// <typeparam name="TConfigurator"></typeparam>
    /// <returns></returns>
    public static Task<int> RunApp<TConfigurator>(string[] args)
        where TConfigurator : ServerConfiguratorBase, IConfigureServer<ServerConfiguration>, new() =>
        HostRunner.Run<TConfigurator>(CreateAppHostBuilder(args));

    /// <summary>
    ///     When you want to run a server application, and use asynchronous method to configure your services, migration, etc.
    /// </summary>
    /// <param name="args"></param>
    /// <typeparam name="TConfigurator"></typeparam>
    /// <returns></returns>
    public static Task<int> RunAppAsync<TConfigurator>(string[] args)
        where TConfigurator : ServerConfiguratorBase, IConfigureServer<ServerConfiguration>, new() =>
        HostRunner.RunAsync<TConfigurator>(CreateAppHostBuilder(args));

    private static IHostBuilder CreateAppHostBuilder(string[] args)
    {
        var hostBuilder = new HostBuilder();
        hostBuilder.ConfigureHostConfiguration(opts => opts.AddEnvironmentVariables("DOTNET_").AddCommandLine(args));
        return hostBuilder;
    }
}