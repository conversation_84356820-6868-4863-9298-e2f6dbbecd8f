// ReSharper disable UnusedAutoPropertyAccessor.Global

namespace Kukui.Infrastructure.Hosting.Web.Common;

public class SecuritySettings
{
    public bool EnforceHttps { get; set; }

    public string[] AllowedOrigins { get; set; } = [];

    public AuthenticationSettings Authentication { get; set; } = new();
}

public class AuthenticationSettings
{
    public bool Enabled { get; set; }

    public string IdentityServerHost { get; set; } = default!;
}