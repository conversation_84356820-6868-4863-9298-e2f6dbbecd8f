namespace Kukui.Infrastructure.Hosting.Web.Common.Extensions;

using NSwag;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddOpenApiDocument(
        this IServiceCollection services,
        WebServerConfiguration webServerConfiguration)
    {
        return services.AddOpenApiDocument(
            settings =>
            {
                settings.Version = "v1";
                settings.Title = webServerConfiguration.ServerConfiguration.DisplayName;
                settings.Description = $"Description:{webServerConfiguration.ServerConfiguration.Description}";
                settings.AddSecurity(
                    "JWT Token",
                    [],
                    new OpenApiSecurityScheme
                    {
                        Type = OpenApiSecuritySchemeType.ApiKey,
                        In = OpenApiSecurityApiKeyLocation.Header,
                        Description = "Please enter a valid Bearer token(Prefix token with \"Bearer\").",
                        Name = "Authorization",
                        Scheme = "Bearer"
                    });
            });
    }
}