namespace Kukui.Infrastructure.Hosting.Web.Common.Extensions;

using Scalar.AspNetCore;

public static class ScalarExtensions
{
    public static void MapScalarEndpoints(
        this IEndpointRouteBuilder routeBuilder,
        WebServerConfiguration configuration,
        Action<ScalarOptions> configure)
    {
        routeBuilder.MapScalarApiReference(options =>
        {
            options.WithTitle(configuration.ServerConfiguration.DisplayName)
                .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient)
                .WithTheme(ScalarTheme.Moon)
                .WithPreferredScheme("Bearer");
            configure(options);
        });
    }
}