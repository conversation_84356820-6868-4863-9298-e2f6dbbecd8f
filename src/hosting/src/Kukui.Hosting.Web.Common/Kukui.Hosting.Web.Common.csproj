<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Web.Build.props', '$(MSBuildThisFileDirectory)../../'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Hosting.Web.Common</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Hosting.Web.Common</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" />
    <PackageReference Include="Microsoft.Identity.Client" />
    <PackageReference Include="NSwag.AspNetCore" />
    <PackageReference Include="Scalar.AspNetCore" />
    <PackageReference Include="AspNetCore.HealthChecks.Nats" />
    <PackageReference Include="AspNetCore.HealthChecks.Rabbitmq" />
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" />
    <PackageReference Include="AspNetCore.HealthChecks.System" />
    <PackageReference Include="AspNetCore.HealthChecks.Uris" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" />
    <PackageReference Include="Serilog.AspNetCore" />
  </ItemGroup>

</Project>