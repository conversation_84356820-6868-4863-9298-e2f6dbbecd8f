using static Kukui.Infrastructure.Hosting.Web.Common.WebServerConstants;

namespace Kukui.Infrastructure.Hosting.Web.Common;

using Hosting.Common.Models;

public class WebServerConfiguration
{
    public WebServerConfiguration(ServerConfiguration serverConfiguration, IWebHostEnvironment environment)
    {
        ServerConfiguration = serverConfiguration;
        Environment = environment;
        SecuritySettings = new SecuritySettings();
        ServerConfiguration.Configuration.Bind(SecurityConfigurationSection, SecuritySettings);
    }

    public ServerConfiguration ServerConfiguration { get; set; }

    public SecuritySettings SecuritySettings { get; set; }

    public IWebHostEnvironment Environment { get; }
}