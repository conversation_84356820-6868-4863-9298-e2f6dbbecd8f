namespace Kukui.Infrastructure.Hosting.Serilog.Formatters;

using global::Serilog.Events;
using global::Serilog.Formatting;
using global::Serilog.Formatting.Json;
using global::Serilog.Parsing;

public class DatadogJsonFormatter : ITextFormatter
{
    private readonly JsonValueFormatter _valueFormatter = new();

    public void Format(LogEvent logEvent, TextWriter output)
    {
        output.Write("{");
        WriteKeyValue("timestamp", logEvent.Timestamp.ToString("O"), output);
        WriteKeyValue("message", logEvent.MessageTemplate.Render(logEvent.Properties), output);
        WriteKeyValue("MessageTemplate", logEvent.MessageTemplate.ToString(), output);
        WriteKeyValue("level", logEvent.Level.ToString(), output);
        if (logEvent.Exception != null) WriteKeyValue("Exception", logEvent.Exception.ToString(), output);
        JsonValueFormatter.WriteQuotedJsonString("Properties", output);
        output.Write(":{");
        var num = 0;
        foreach (var property in logEvent.Properties)
        {
            ++num;
            WriteKeyValue(property.Key, property.Value, output, num == logEvent.Properties.Count);
        }

        output.Write("}");
        var source = logEvent.MessageTemplate.Tokens.OfType<PropertyToken>()
            .Where((Func<PropertyToken, bool>) (pt => pt.Format != null))
            .ToArray();

        if (source.Any())
        {
            output.Write(",\"Renderings\":[");
            var str = "";
            foreach (var propertyToken in source)
            {
                output.Write(str);
                str = ",";
                var stringWriter = new StringWriter();
                var properties = logEvent.Properties;
                var output1 = stringWriter;
                propertyToken.Render(properties, output1);
                JsonValueFormatter.WriteQuotedJsonString(stringWriter.ToString(), output);
            }

            output.Write(']');
        }

        output.Write("}");
    }

    private void WriteKeyValue(string key, LogEventPropertyValue val, TextWriter output, bool isLast = false)
    {
        JsonValueFormatter.WriteQuotedJsonString(key, output);
        output.Write(":");
        _valueFormatter.Format(val, output);
        if (isLast) return;

        output.Write(",");
    }

    private static void WriteKeyValue(string key, string val, TextWriter output, bool isLast = false)
    {
        JsonValueFormatter.WriteQuotedJsonString(key, output);
        output.Write(":");
        JsonValueFormatter.WriteQuotedJsonString(val, output);
        if (isLast) return;

        output.Write(",");
    }
}