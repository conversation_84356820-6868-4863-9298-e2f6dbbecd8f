<Project Sdk="Microsoft.NET.Sdk">
<Import Project="$([MSBuild]::GetPathOfFileAbove('Hosting.Build.props', '$(MSBuildThisFileDirectory)/'))" />
  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Hosting.Serilog</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Hosting.Serilog</RootNamespace>
  </PropertyGroup>

 <ItemGroup>
   <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.Extensions.Hosting" />
    <PackageReference Include="Destructurama.Attributed" />
    <PackageReference Include="Serilog.Settings.Configuration" />
    <PackageReference Include="Serilog.Expressions" />
    <PackageReference Include="Serilog.Exceptions" />
    <PackageReference Include="Serilog.Enrichers.Demystifier" />
    <PackageReference Include="Serilog.Enrichers.Environment" />
    <PackageReference Include="Serilog.Sinks.Async" />
    <PackageReference Include="Serilog.Sinks.Console" />
    <PackageReference Include="Serilog.Sinks.File" />
    <PackageReference Include="Serilog.Sinks.Seq" />
 </ItemGroup>

</Project>