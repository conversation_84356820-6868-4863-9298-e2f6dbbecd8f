namespace Kukui.Infrastructure.Hosting.Web.MinimalApi;

using System.Reflection;
using System.Text.Json;
using Hosting.Common.Models;

public class MinimalApiConfiguration
{
    internal Assembly[] AssemblyTypes { get; set; } = [Assembly.GetEntryAssembly()!];

    internal Action<MediatRServiceConfiguration>? MediatRConfigAction { get; set; }

    internal Action<ProblemDetailsContext>? ProblemDetailsConfigAction { get; set; }

    internal Action<JsonSerializerOptions> SerializerOptionsConfigAction { get; set; } =
        SerializationOptions.SetDefaultOptions;

    public MinimalApiConfiguration RegisterTypeFromAssembly<T>()
    {
        AssemblyTypes = [typeof(T).Assembly];

        return this;
    }

    public MinimalApiConfiguration RegisterTypeFromAssemblies(params Assembly[] assemblies)
    {
        AssemblyTypes = assemblies;
        return this;
    }

    public MinimalApiConfiguration ConfigureMediatR(Action<MediatRServiceConfiguration> configure)
    {
        MediatRConfigAction = configure;

        return this;
    }

    public MinimalApiConfiguration ConfigureProblemDetails(Action<ProblemDetailsContext> configure)
    {
        ProblemDetailsConfigAction = configure;

        return this;
    }

    public MinimalApiConfiguration ConfigureSerializerOptions(Action<JsonSerializerOptions> configure)
    {
        SerializerOptionsConfigAction = configure;

        return this;
    }
}