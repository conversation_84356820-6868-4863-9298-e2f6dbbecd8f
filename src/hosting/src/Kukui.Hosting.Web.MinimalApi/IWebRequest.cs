namespace Kukui.Infrastructure.Hosting.Web.MinimalApi;

using MediatR;
using Microsoft.AspNetCore.Http.HttpResults;

/// <summary>
/// Empty Request with response.
/// </summary>
public interface IWebRequest : IRequest<IResult>
{
}

/// <summary>
/// Default request with response.
/// </summary>
/// <typeparam name="TResponse"></typeparam>
public interface IWebRequest<out TResponse> : IRequest<TResponse>
    where TResponse : IResult
{
}

public interface IWebRequest<TResponse1, TResponse2> : IWebRequest<Results<TResponse1, TResponse2>>
    where TResponse1 : IResult
    where TResponse2 : IResult
{
}