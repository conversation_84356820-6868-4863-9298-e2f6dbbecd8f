namespace Kukui.Infrastructure.Hosting.Web.MinimalApi;

using FluentValidation;

public class ValidationErrorFilter : IEndpointFilter
{
    public const string ValidationErrorType = "validation-error";

    public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
    {
        try
        {
            return await next(context);
        }
        catch (ValidationException ex)
        {
            var errors = ex.Errors.Where(x => x != null)
                .GroupBy(
                    x => x.PropertyName,
                    x => x.ErrorMessage,
                    (propertyName, errorMessages) => new
                    {
                        Key = propertyName,
                        Values = errorMessages.Distinct().ToArray()
                    })
                .ToDictionary(x => x.Key, x => x.Values);

            return Results.ValidationProblem(type: ValidationErrorType, errors: errors);
        }
    }
}