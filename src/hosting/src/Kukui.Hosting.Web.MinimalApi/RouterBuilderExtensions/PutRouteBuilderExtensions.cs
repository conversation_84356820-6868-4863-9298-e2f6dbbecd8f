namespace Kukui.Infrastructure.Hosting.Web.MinimalApi.RouterBuilderExtensions;

using MediatR;
using Microsoft.AspNetCore.Mvc;

public static class PutRouteBuilderExtensions
{
    public static RouteHandlerBuilder MapPut<TRequest>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest
    {
        return endpoints.GlobalGroup()
            .MapPut(
                pattern,
                async ([FromBody] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }

    public static RouteHandlerBuilder MapPut<TRequest, TResponse>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest<TResponse>
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapPut(
                pattern,
                async ([FromBody] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }
}