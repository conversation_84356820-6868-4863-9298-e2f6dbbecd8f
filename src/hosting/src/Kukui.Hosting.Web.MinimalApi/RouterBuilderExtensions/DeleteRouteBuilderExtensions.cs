namespace Kukui.Infrastructure.Hosting.Web.MinimalApi.RouterBuilderExtensions;

using MediatR;

public static class DeleteRouteBuilderExtensions
{
    public static RouteHandlerBuilder MapDelete<TRequest>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest
    {
        return endpoints.GlobalGroup()
            .MapDelete(
                pattern,
                async ([AsParameters] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }

    public static RouteHandlerBuilder MapDelete<TRequest, TResponse>(
        this IEndpointRouteBuilder endpoints,
        string pattern)
        where TRequest : IWebRequest<TResponse>
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapDelete(
                pattern,
                async ([AsParameters] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }
}