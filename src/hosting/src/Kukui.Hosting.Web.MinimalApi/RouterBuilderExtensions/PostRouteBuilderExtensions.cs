namespace Kukui.Infrastructure.Hosting.Web.MinimalApi.RouterBuilderExtensions;

using MediatR;
using Microsoft.AspNetCore.Mvc;

public static class PostRouteBuilderExtensions
{
    /// <summary>
    /// Maps a POST endpoint for a specific request type.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request implementing <see cref="IWebRequest"/>.</typeparam>
    /// <param name="endpoints">The endpoint route builder.</param>
    /// <param name="pattern">The URL pattern of the endpoint.</param>
    /// <returns>A configured <see cref="RouteHandlerBuilder"/> for further customization.</returns>
    public static RouteHandlerBuilder MapPost<TRequest>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest
    {
        return endpoints.GlobalGroup()
            .MapPost(
                pattern,
                async ([FromBody] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }

    /// <summary>
    /// Maps a POST endpoint for a request type with parameters.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request implementing <see cref="IWebRequest"/>.</typeparam>
    /// <param name="endpoints">The endpoint route builder.</param>
    /// <param name="pattern">The URL pattern of the endpoint.</param>
    /// <returns>A configured <see cref="RouteHandlerBuilder"/> for further customization.</returns>
    public static RouteHandlerBuilder MapPostWithParameters<TRequest>(
        this IEndpointRouteBuilder endpoints,
        string pattern)
        where TRequest : IWebRequest
    {
        return endpoints.GlobalGroup()
            .MapPost(
                pattern,
                async ([AsParameters] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }

    /// <summary>
    /// Maps a POST endpoint with a specific response type.
    /// </summary>
    /// <typeparam name="TResponse">The type of the response implementing <see cref="IResult"/>.</typeparam>
    /// <param name="endpoints">The endpoint route builder.</param>
    /// <param name="pattern">The URL pattern of the endpoint.</param>
    /// <returns>A configured <see cref="RouteHandlerBuilder"/> for further customization.</returns>
    public static RouteHandlerBuilder MapPostWithResponse<TResponse>(
        this IEndpointRouteBuilder endpoints,
        string pattern)
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapPost(
                pattern,
                async (HttpContext httpContext, IMediator mediator, CancellationToken cancellationToken) =>
                    await mediator.Send(new WebRequest<TResponse>(httpContext), cancellationToken)
                        .ConfigureAwait(false));
    }

    /// <summary>
    /// Maps a POST endpoint for a specific request and response type.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request implementing <see cref="IWebRequest{TResponse}"/>.</typeparam>
    /// <typeparam name="TResponse">The type of the response implementing <see cref="IResult"/>.</typeparam>
    /// <param name="endpoints">The endpoint route builder.</param>
    /// <param name="pattern">The URL pattern of the endpoint.</param>
    /// <returns>A configured <see cref="RouteHandlerBuilder"/> for further customization.</returns>
    public static RouteHandlerBuilder MapPost<TRequest, TResponse>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest<TResponse>
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapPost(
                pattern,
                async ([FromBody] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }


    /// <summary>
    /// Maps a POST endpoint for a request and response type with parameters.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request implementing <see cref="IWebRequest{TResponse}"/>.</typeparam>
    /// <typeparam name="TResponse">The type of the response implementing <see cref="IResult"/>.</typeparam>
    /// <param name="endpoints">The endpoint route builder.</param>
    /// <param name="pattern">The URL pattern of the endpoint.</param>
    /// <returns>A configured <see cref="RouteHandlerBuilder"/> for further customization.</returns>
    public static RouteHandlerBuilder MapPostWithParameters<TRequest, TResponse>(
        this IEndpointRouteBuilder endpoints,
        string pattern)
        where TRequest : IWebRequest<TResponse>
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapPost(
                pattern,
                async ([AsParameters] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }
}