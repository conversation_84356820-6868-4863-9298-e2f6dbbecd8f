namespace Kukui.Infrastructure.Hosting.Web.MinimalApi.RouterBuilderExtensions;

using MediatR;
using Microsoft.AspNetCore.Mvc;

public static class PatchRouteBuilderExtensions
{
    public static RouteHandlerBuilder MapPatch<TRequest>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest
    {
        return endpoints.GlobalGroup()
            .MapPatch(
                pattern,
                async ([FromBody] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }

    public static RouteHandlerBuilder MapPatch<TRequest, TResponse>(
        this IEndpointRouteBuilder endpoints,
        string pattern)
        where TRequest : IWebRequest<TResponse>
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapPatch(
                pattern,
                async ([FromBody] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }
}