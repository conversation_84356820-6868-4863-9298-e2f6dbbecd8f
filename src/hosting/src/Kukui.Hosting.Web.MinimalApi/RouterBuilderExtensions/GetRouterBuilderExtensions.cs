namespace Kukui.Infrastructure.Hosting.Web.MinimalApi.RouterBuilderExtensions;

using MediatR;

public static class GetRouterBuilderExtensions
{
    public static RouteHandlerBuilder MapGet<TRequest>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest
    {
        return endpoints.GlobalGroup()
            .MapGet(
                pattern,
                async ([AsParameters] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }

    public static RouteHandlerBuilder MapGet<TRequest, TResponse>(this IEndpointRouteBuilder endpoints, string pattern)
        where TRequest : IWebRequest<TResponse>
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapGet(
                pattern,
                async ([AsParameters] TRequest request, IMediator mediator, CancellationToken cancellationToken) =>
                await mediator.Send(request, cancellationToken).ConfigureAwait(false));
    }

    public static RouteHandlerBuilder MapGetWithResponse<TResponse>(
        this IEndpointRouteBuilder endpoints,
        string pattern)
        where TResponse : IResult
    {
        return endpoints.GlobalGroup()
            .MapGet(
                pattern,
                async (HttpContext httpContext, IMediator mediator, CancellationToken cancellationToken) =>
                    await mediator.Send(new WebRequest<TResponse>(httpContext), cancellationToken)
                        .ConfigureAwait(false));
    }
}