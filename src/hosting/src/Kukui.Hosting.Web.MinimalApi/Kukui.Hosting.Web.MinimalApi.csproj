<Project Sdk="Microsoft.NET.Sdk.Web">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Web.Build.props', '$(MSBuildThisFileDirectory)../../'))" />
  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Hosting.Web.MinimalApi</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Hosting.Web.MinimalApi</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="MediatR" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Kukui.Hosting.Web\Kukui.Hosting.Web.csproj" />
  </ItemGroup>
</Project>