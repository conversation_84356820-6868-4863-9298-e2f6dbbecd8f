namespace Kukui.Infrastructure.Hosting.Web.MinimalApi;

using FluentValidation;
using Microsoft.AspNetCore.Http.Json;

public static class ServiceRegistrations
{
    public static IServiceCollection AddMinimalApiServices(
        this IServiceCollection services,
        Action<MinimalApiConfiguration>? configure = null)
    {
        var apiConfiguration = new MinimalApiConfiguration();
        configure?.Invoke(apiConfiguration);
        services.AddProblemDetails(
                options => { options.CustomizeProblemDetails = apiConfiguration.ProblemDetailsConfigAction; })
            .AddHttpContextAccessor()
            .AddMediatR(
                options =>
                {
                    options.RegisterServicesFromAssemblies(apiConfiguration.AssemblyTypes);
                    options.AddOpenBehavior(typeof(FluentValidationPipelineBehavior<,>));

                    apiConfiguration.MediatRConfigAction?.Invoke(options);
                })
            .AddValidatorsFromAssemblies(apiConfiguration.AssemblyTypes);

        services.Configure<JsonOptions>(
            options => apiConfiguration.SerializerOptionsConfigAction.Invoke(options.SerializerOptions));

        return services;
    }
}