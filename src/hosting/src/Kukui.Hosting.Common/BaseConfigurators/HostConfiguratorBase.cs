namespace Kukui.Infrastructure.Hosting.Common.BaseConfigurators;

using global::Serilog;
using Microsoft.Extensions.DependencyInjection;
using Models;

public abstract class HostConfiguratorBase : IConfigureServer<ServerConfiguration>
{
    public abstract void RegisterServices(IServiceCollection services, ServerConfiguration serverConfig);

    public virtual void ConfigureServices(IServiceProvider serviceProvider, ServerConfiguration serverConfiguration)
    {
    }

    internal void RegisterServicesInternal(IServiceCollection services, ServerConfiguration serverConfig)
    {
        RegisterServices(services, serverConfig);
    }

    public virtual Task ConfigureServicesAsync(
        IServiceProvider serviceProvider,
        ServerConfiguration serverConfiguration) =>
        Task.CompletedTask;

    public virtual void ConfigureSerilog(LoggerConfiguration configuration)
    {
    }
}