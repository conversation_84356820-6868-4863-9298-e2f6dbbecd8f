namespace Kukui.Infrastructure.Hosting.Common.BaseConfigurators;

using global::Serilog;
using Microsoft.Extensions.DependencyInjection;

public interface IConfigureServer<in TSettings>
    where TSettings : class
{
    void RegisterServices(IServiceCollection services, TSettings configuration);

    void ConfigureServices(IServiceProvider serviceProvider, TSettings configuration);

    Task ConfigureServicesAsync(IServiceProvider serviceProvider, TSettings configuration);

    void ConfigureSerilog(LoggerConfiguration configuration);
}