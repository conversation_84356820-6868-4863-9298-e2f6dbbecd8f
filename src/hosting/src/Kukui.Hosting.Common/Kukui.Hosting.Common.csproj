<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Hosting.Build.props', '$(MSBuildThisFileDirectory)/'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Hosting.Common</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Hosting.Common</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />


    <PackageReference Include="Figgle" />
    <PackageReference Include="Scrutor" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Kukui.Hosting.Common.Vault\Kukui.Hosting.Common.Vault.csproj" />
    <ProjectReference Include="..\Kukui.Hosting.Serilog\Kukui.Hosting.Serilog.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="Kukui.Infrastructure.Hosting.Web.Common" />
    <InternalsVisibleTo Include="Kukui.Infrastructure.Hosting.Web" />
  </ItemGroup>


</Project>