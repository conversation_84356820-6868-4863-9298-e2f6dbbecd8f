namespace Kukui.Infrastructure.Hosting.Common;

using BaseConfigurators;
using Extensions;
using global::Serilog;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Models;
using LoggerExtensions = Extensions.LoggerExtensions;

public static class HostRunner
{
    private const string ServerStartupErrorMessage = "An error has occurred while trying to start or run the server";

    internal static bool ConfigureWithoutRunning { get; set; } = false;

    public static async Task<int> Run(
        IHostBuilder hostBuilder,
        Action<IServiceCollection, ServerConfiguration> registerServices,
        Action<IServiceProvider, ServerConfiguration>? configureServices = null,
        Action<LoggerConfiguration>? serilogConfig = null)
    {
        return await Run(() =>
        {
            var host = hostBuilder.ConfigureHosting(registerServices, serilogConfig)
                .Build()
                .ConfigureServices(configureServices);

            return ConfigureWithoutRunning ? Task.CompletedTask : host.RunAsync();
        });
    }

    public static async Task<int> Run(
        IHostBuilder hostBuilder,
        Action<IServiceCollection, ServerConfiguration> registerServices,
        Func<IServiceProvider, ServerConfiguration, Task>? configureServices = null,
        Action<LoggerConfiguration>? serilogConfig = null)
    {
        return await Run(async () =>
        {
            var host = hostBuilder.ConfigureHosting(registerServices, serilogConfig).Build();
            await (configureServices?.Invoke(host.Services, host.Services.GetRequiredService<ServerConfiguration>()) ??
                   Task.CompletedTask);
            await (ConfigureWithoutRunning ? Task.CompletedTask : host.RunAsync());
        });
    }

    public static Task<int> Run<TConfigurator>(IHostBuilder hostBuilder)
        where TConfigurator : HostConfiguratorBase, IConfigureServer<ServerConfiguration>, new()
    {
        TConfigurator configurator = new();
        return Run(
            hostBuilder,
            configurator.RegisterServicesInternal,
            configurator.ConfigureServices,
            configurator.ConfigureSerilog);
    }

    public static Task<int> RunAsync<TConfigurator>(IHostBuilder hostBuilder)
        where TConfigurator : HostConfiguratorBase, IConfigureServer<ServerConfiguration>, new()
    {
        TConfigurator configurator = new();
        return Run(
            hostBuilder,
            configurator.RegisterServicesInternal,
            configurator.ConfigureServicesAsync,
            configurator.ConfigureSerilog);
    }

    internal static async Task<int> Run(Func<Task> execute)
    {
        LoggerExtensions.CreateInitialLogger();
        try
        {
            Log.Logger.Information("Setting up server ...");

            await execute();

            Log.Logger.Information("Server shutdown");
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, ServerStartupErrorMessage);
            Environment.ExitCode = 1;
        }
        finally
        {
            Log.CloseAndFlush();
        }

        return Environment.ExitCode;
    }
}