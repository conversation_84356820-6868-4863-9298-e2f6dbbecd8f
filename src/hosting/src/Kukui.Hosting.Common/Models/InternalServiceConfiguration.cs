namespace Kukui.Infrastructure.Hosting.Common.Models;

using Microsoft.Extensions.Configuration;

public class InternalServiceConfiguration : ServiceConfiguration
{
    public const string SectionName = "InternalServices";

    public InternalServiceConfiguration(ServerConfiguration serverConfiguration, string serviceName)
    {
        var sectionName = $"{SectionName}:{serviceName}";
        var serviceNameSection = serverConfiguration.ServerConfigSection.GetSection(sectionName);
        if (!serviceNameSection.Exists())
        {
            throw new SectionNotFoundException(sectionName);
        }

        Url = serviceNameSection.GetValue<string>("Url") ??
              throw new InvalidOperationException($"Url for {serviceName} not found");
        KeycloakConfiguration = KeycloakConfiguration.CreateInternalServicesKeycloakConfiguration(
            serverConfiguration.ServerConfigSection.GetSection(SectionName),
            $"{serviceName}:Security");
    }

    public KeycloakConfiguration KeycloakConfiguration { get; set; }
}