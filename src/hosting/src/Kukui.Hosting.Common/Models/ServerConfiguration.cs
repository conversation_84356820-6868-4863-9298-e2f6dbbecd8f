using static Kukui.Infrastructure.Hosting.Common.Models.ServerConfigurationConstants;

namespace Kukui.Infrastructure.Hosting.Common.Models;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

public class ServerConfiguration
{
    public ServerConfiguration(IConfiguration configuration)
    {
        Configuration = configuration;
        ServerConfigSection = configuration.GetSection(ServerConfigurationSection);
        DisplayName = configuration.GetValue<string>(DisplayNameSection) ?? string.Empty;
        Description = configuration.GetValue<string>(DescriptionSection);
        LoggingConfiguration = configuration.GetSection(LoggingConfigurationSection);
        TcpHealthCheckConfiguration =
            ServerConfigSection.GetSection("TcpHealthCheck").Get<TcpHealthCheckConfiguration>() ??
            new TcpHealthCheckConfiguration();
    }

    public string DisplayName { get; set; }

    public string? Description { get; set; }

    public VersionInfo VersionInfo { get; set; } = default!;

    public TcpHealthCheckConfiguration TcpHealthCheckConfiguration { get; set; }

    public IConfigurationSection LoggingConfiguration { get; set; }

    public IConfigurationSection ServerConfigSection { get; set; }

    public IConfiguration Configuration { get; set; }

    public IHostEnvironment Environment { get; set; } = default!;


    public void BindServerConfig<T>(string sectionName, T instance)
    {
        ServerConfigSection.Bind(sectionName, instance);
    }

    public string GetConnectionString(string key = "ConnectionString") =>
        ServerConfigSection.GetValue<string>($"Database:{key}") ??
        throw new InvalidOperationException($"Connection string {key} not found");

    public ExternalServiceConfiguration GetExternalServiceConfiguration(string serviceName) =>
        GetConfiguration<ExternalServiceConfiguration>($"ExternalServices:{serviceName}");

    public InternalServiceConfiguration GetInternalServiceConfiguration(string serviceName) => new(this, serviceName);

    public KeycloakConfiguration GetInternalServicesKeycloakConfiguration(
        string serviceName,
        string? sectionName = null) =>
        KeycloakConfiguration.CreateInternalServicesKeycloakConfiguration(
            ServerConfigSection.GetSection(sectionName ?? InternalServiceConfiguration.SectionName),
            $"{serviceName}:Security");

    public TConfiguration GetConfiguration<TConfiguration>(string fullPath)
        where TConfiguration : new()
    {
        TConfiguration config = new();
        BindServerConfig(fullPath, config);

        return config;
    }
}