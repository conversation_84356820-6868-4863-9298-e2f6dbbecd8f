namespace Kukui.Infrastructure.Hosting.Common.Models;

using System.Text.Json;
using System.Text.Json.Serialization;

public class SerializationOptions
{
    public static JsonSerializerOptions GetDefaultOptions(Action<JsonSerializerOptions>? configure = null)
    {
        var options = new JsonSerializerOptions();
        SetDefaultOptions(options);
        configure?.Invoke(options);
        return options;
    }

    public static void SetDefaultOptions(JsonSerializerOptions options)
    {
        options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.PropertyNameCaseInsensitive = true;
        options.WriteIndented = true;
        options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingDefault;
        options.Converters.Add(new JsonStringEnumConverter());
    }
}