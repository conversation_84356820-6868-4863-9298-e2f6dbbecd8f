namespace Kukui.Infrastructure.Hosting.Common.Extensions;

using global::Serilog;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Models;

public static class HostBuilderExtensions
{
    public static IHostBuilder ConfigureHosting(
        this IHostBuilder hostBuilder,
        Action<IServiceCollection, ServerConfiguration>? registerServices = null,
        Action<LoggerConfiguration>? loggerConfig = null)
    {
        return hostBuilder.ConfigureAppConfiguration((hostCtx, configBuilder) =>
            {
                configBuilder.ConfigureAppSettings(hostCtx.HostingEnvironment);
            })
            .ConfigureLogging((hostCtx, logBuilder) => logBuilder.ConfigureSerilog(hostCtx, loggerConfig))
            .ConfigureServices((hostCtx, services) =>
            {
                services.RegisterAppSettings(hostCtx);
                var serverConfig = services.GetRequiredService<ServerConfiguration>();
                registerServices?.Invoke(services, serverConfig);
            });
    }
}