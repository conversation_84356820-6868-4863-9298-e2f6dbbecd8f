using static Kukui.Infrastructure.Hosting.Common.Models.ServerConfigurationConstants;

namespace Kukui.Infrastructure.Hosting.Common.Extensions;

using System.Reflection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Vault;

public static class ConfigurationBuilderExtensions
{
    public static IConfigurationBuilder ConfigureAppSettings(
        this IConfigurationBuilder configBuilder,
        IHostEnvironment env)
    {
        configBuilder.SetBasePath(GetFileProvidersPath(env))
            .AddJsonFile($"{ConfigurationBaseFilename}.json", false, true)
            .AddJsonFile($"{ConfigurationBaseFilename}.{env.EnvironmentName}.json", true, true)
            .AddJsonFile($"{ConfigurationSecretsFilename}.json", true, true)
            .AddJsonFile($"{ConfigurationSecretsFilename}.{env.EnvironmentName}.json", true, true)
            .AddUserSecrets(
                Assembly.GetEntryAssembly() ?? throw new InvalidOperationException("Entry assembly not found."))
            .AddEnvironmentVariables()
            .AddVault(configBuilder.GetSection(VaultConfigurationSection))
            .AddEnvironmentVariables();
        return configBuilder;
    }

    private static string GetFileProvidersPath(IHostEnvironment hostBuilderContext)
    {
        var configDir = Path.Combine(hostBuilderContext.ContentRootPath, ConfigurationDirectoryName);
        if (!Directory.Exists(configDir))
        {
            throw new DirectoryNotFoundException(
                $"Directory {configDir} does not exist. This is the root directory for configuration files.");
        }

        return configDir;
    }

    private static IConfigurationSection GetSection(this IConfigurationBuilder configBuilder, string sectionName)
    {
        var config = configBuilder.Build();
        return config.GetSection(sectionName);
    }
}