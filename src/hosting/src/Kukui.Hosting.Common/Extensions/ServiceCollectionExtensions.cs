using static Kukui.Infrastructure.Hosting.Common.Models.ServerConfigurationConstants;

namespace Kukui.Infrastructure.Hosting.Common.Extensions;

using Figgle;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Models;
using Vault;

public static class ServiceCollectionExtensions
{
    internal static IServiceCollection RegisterAppSettings(
        this IServiceCollection services,
        HostBuilderContext hostBuilderCtx)
    {
        ServerConfiguration serverConfig = new(hostBuilderCtx.Configuration)
            { Environment = hostBuilderCtx.HostingEnvironment };
        return services.CollectAppVersionInformation(serverConfig).AddServerConfiguration(serverConfig);
    }

    private static IServiceCollection AddServerConfiguration(
        this IServiceCollection services,
        ServerConfiguration serverConfig)
    {
        return services.AddSingleton(serverConfig).AddVaultClient(serverConfig);
    }

    private static IServiceCollection AddVaultClient(this IServiceCollection services, ServerConfiguration serverConfig)
    {
        var vaultOptions = new VaultOptions(serverConfig.Configuration.GetSection(VaultConfigurationSection));
        return services.AddSingleton(new VaultReadOnlyClientFactory(vaultOptions));
    }

    private static IServiceCollection CollectAppVersionInformation(
        this IServiceCollection services,
        ServerConfiguration serverConfig)
    {
        serverConfig.VersionInfo = new VersionInfo
        {
            HostingVersion =
                typeof(ServiceCollectionExtensions).Assembly.GetAssemblyVersionString(VersionType.Assembly),
            AppVersion = VersionType.Assembly.GetEntryAssemblyVersion(),
            FileVersion = VersionType.File.GetEntryAssemblyVersion(),
            ProductVersion = VersionType.Product.GetEntryAssemblyVersion()
        };

        PrintImportantInfo(serverConfig);

        return services;
    }

    private static void PrintImportantInfo(ServerConfiguration serverConfig)
    {
        Console.Title = serverConfig.DisplayName;
        Console.WriteLine(FiggleFonts.Standard.Render(serverConfig.DisplayName));

        Console.WriteLine($"Hosting version: {serverConfig.VersionInfo.HostingVersion}");
        Console.WriteLine($"App version: {serverConfig.VersionInfo.AppVersion}");
        Console.WriteLine($"File version: {serverConfig.VersionInfo.FileVersion}");
        Console.WriteLine($"Product version: {serverConfig.VersionInfo.ProductVersion}");
    }

    public static TService GetRequiredService<TService>(this IServiceCollection services)
        where TService : notnull
    {
        using var provider = services.BuildServiceProvider();

        return provider.GetRequiredService<TService>();
    }
}