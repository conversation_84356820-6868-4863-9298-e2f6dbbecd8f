namespace Kukui.Infrastructure.Hosting.Common.Extensions;

using Destructurama;
using global::Serilog;
using global::Serilog.Events;
using global::Serilog.Exceptions;
using global::Serilog.Formatting.Json;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Models;

public static class LoggerExtensions
{
    public static void CreateInitialLogger()
    {
        Log.Logger = new LoggerConfiguration().MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .Enrich.WithExceptionDetails()
            .Enrich.FromLogContext()
            .WriteTo.Console(new JsonFormatter())
            .CreateLogger();
    }

    public static ILoggingBuilder ConfigureSerilog(
        this ILoggingBuilder loggingBuilder,
        HostBuilderContext hostCtx,
        Action<LoggerConfiguration>? configure = null)
    {
        loggingBuilder.ClearProviders();
        var configuration = new LoggerConfiguration().ReadFrom
            .Configuration(hostCtx.Configuration.GetSection(ServerConfigurationConstants.LoggingConfigurationSection))
            .Filter.ByExcluding(HealthCheckEndpoint)
            .Destructure.UsingAttributes()
            .Enrich.FromLogContext()
            .Enrich.WithExceptionDetails()
            .Enrich.WithEnvironmentName()
            .Enrich.WithProperty(
                nameof(hostCtx.HostingEnvironment.ApplicationName),
                hostCtx.HostingEnvironment.ApplicationName);
        configure?.Invoke(configuration);
        return loggingBuilder.AddSerilog(configuration.CreateLogger());
    }

    private static bool HealthCheckEndpoint(LogEvent logEvent)
    {
        return ((string[]) ["Path", "RequestPath"]).Any(path =>
            logEvent.Properties.ContainsKey(path) &&
            logEvent.Properties[path].ToString().Contains(EndpointConstants.HealthPath));
    }
}