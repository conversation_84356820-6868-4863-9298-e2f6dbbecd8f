using System.Reflection;
using Kukui.Infrastructure.Hosting.Common.Models;

namespace Kukui.Infrastructure.Hosting.Common.Extensions;

internal static class AssemblyVersionExtensions
{
    private const string UnknownVersion = "1.0.0";

    internal static string GetAssemblyVersionString(this Assembly? assembly, VersionType versionType)
    {
        if (assembly == null)
        {
            return UnknownVersion;
        }

        return versionType switch
        {
            VersionType.File => assembly.GetCustomAttribute<AssemblyFileVersionAttribute>()
                ?.Version,
            VersionType.Product => assembly.GetCustomAttribute<AssemblyProductAttribute>()
                ?.Product,
            _ => assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()
                .InformationalVersion
        } ?? UnknownVersion;
    }

    public static string GetEntryAssemblyVersion(this VersionType versionType)
    {
        var asm = Assembly.GetEntryAssembly();

        return asm.GetAssemblyVersionString(versionType);
    }
}