using Kukui.Infrastructure.Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Kukui.Infrastructure.Hosting.Common.Extensions;

public static class HostExtensions
{
    public static IHost ConfigureServices(
        this IHost host,
        Action<IServiceProvider, ServerConfiguration>? configureServices
    )
    {
        var serverConfig = host.Services.GetRequiredService<ServerConfiguration>();
        configureServices?.Invoke(host.Services, serverConfig);

        return host;
    }
}