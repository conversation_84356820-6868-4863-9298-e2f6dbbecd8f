<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="9.0.4" />
    <PackageVersion Include="Bogus" Version="35.6.3" />
    <PackageVersion Include="FluentAssertions" Version="[7.2.0]" />
    <PackageVersion Include="TestContainers" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.PostgreSql" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.Keycloak" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.Nats" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.RabbitMq" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.Redis" Version="4.4.0" />
    <PackageVersion Include="WireMock.Net.Testcontainers" Version="1.7.4" />
    <PackageVersion Include="xunit" Version="2.9.3" />
    <PackageVersion Include="xunit.extensibility.core" Version="2.9.3" />
    <PackageVersion Include="xunit.extensibility.execution" Version="2.9.3" />
    <PackageVersion Include="xunit.analyzers" Version="1.21.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="coverlet.collector" Version="6.0.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageVersion>
    <PackageVersion Include="coverlet.msbuild" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
  </ItemGroup>

  <ItemGroup Condition=" '$(TargetFramework)' == 'net9.0' ">
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.4" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net8.0' ">
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.7" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.36" />
  </ItemGroup>
</Project>