using System.Linq.Expressions;
using System.Reflection;
using Cassandra.Mapping;

namespace Kukui.Infrastructure.Database.ScyllaDb.Linq.Extensions;

public static class MapperExtensions
{
    /// <summary>
    ///     Provide scylla db functionality to search for values in collections column data types(map, list, set).
    ///     Should be invoked as follows:
    ///     - for list/set data types:  await mapper.Search
    ///     <T>
    ///         (x => x.Property.Contains(value));
    ///         - for map data type:  await mapper.Search<T>(x => x.Property.Values.Contains(value));
    /// </summary>
    /// <typeparam name="T">The type of entity to search</typeparam>
    /// <param name="mapper">The DataStax Mapper instance to use</param>
    /// <param name="searchExpression">The expression used to define the search</param>
    /// <returns>
    ///     An asynchronous task that represents the search operation. The task result contains the list of entities that
    ///     match the search criteria.
    /// </returns>
    public static async Task<IEnumerable<T>> Search<T>(this Mapper mapper, Expression<Func<T, bool>> searchExpression)
    {
        var result = BuildQuery(searchExpression);

        return await mapper.FetchAsync<T>(result.query, result.searchCriteria);
    }

    /// <summary>
    ///     Provide scylla db functionality to search for values in collections column data types(map, list, set).
    ///     Should be invoked as follows:
    ///     - for list/set data types:  await mapper.Search
    ///     <T>
    ///         (x => x.Property.Contains(value));
    ///         - for map data type:  await mapper.Search<T>(x => x.Property.Values.Contains(value));
    /// </summary>
    /// <typeparam name="T">The type of entity to search</typeparam>
    /// <param name="mapper">The DataStax Mapper instance to use</param>
    /// <param name="searchExpression">The expression used to define the search</param>
    /// <param name="pageSize">The number of entities to return in a single page</param>
    /// <param name="pagingState">Page state associated with the search</param>
    /// <returns>
    ///     An asynchronous task that represents the search operation. The task result contains the list of entities that
    ///     match the search criteria.
    /// </returns>
    public static Task<IPage<T>> Search<T>(this Mapper mapper, Expression<Func<T, bool>> searchExpression, int pageSize,
        byte[] pagingState)
    {
        var result = BuildQuery(searchExpression);
        return mapper.FetchPageAsync<T>(pageSize, pagingState, result.query,
            result.searchCriteria == null ? null : [result.searchCriteria]);
    }

    private static (string query, object? searchCriteria) BuildQuery<T>(Expression<Func<T, bool>> expression)
    {
        var typeDefinition = MappingConfiguration.Global.Get<T>();

        if (expression.Body is not MethodCallExpression methodCallExpression)
        {
            throw new ArgumentException();
        }

        PropertyInfo? propertyInfo;
        MemberExpression argumentExpression;

        if (methodCallExpression.Object is MemberExpression propertyMemberExpression)
        {
            propertyInfo = (PropertyInfo)propertyMemberExpression.Member;
            argumentExpression = (MemberExpression)methodCallExpression.Arguments[0];
        }
        else
        {
            propertyInfo =
                ((MemberExpression)(methodCallExpression.Arguments[0] as MemberExpression)!.Expression)
                .Member as PropertyInfo;
            argumentExpression = (MemberExpression)methodCallExpression.Arguments[1];
        }

        var argsFieldInfo = (FieldInfo)argumentExpression.Member;
        var argsConstantExpression = (ConstantExpression)argumentExpression.Expression;
        var argument = argsFieldInfo.GetValue(argsConstantExpression.Value);

        if (argument == null)
        {
            return (string.Empty, argument);
        }

        var columnName = typeDefinition.GetColumnDefinition(propertyInfo)
            .ColumnName;

        var query = $"WHERE {columnName} CONTAINS ?";
        return (query, argument);
    }
}