namespace Kukui.Infrastructure.Database.ScyllaDb.Repository;

using System.Linq.Expressions;
using Cassandra;
using Cassandra.Mapping;

public interface IRepository<T>
{
    ISession Session { get; }

    Task<T?> FindAsync(params object[] parameters);

    Task<TResult?> FindAsync<TResult>(object[] parameters, Expression<Func<T, TResult>> selector);

    Task<IEnumerable<T>> FindManyAsync(object[] parameters);

    Task<IEnumerable<TResult>> FindManyAsync<TResult>(object[] parameters, Expression<Func<T, TResult>> selector);

    Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> predicate);

    Task<TResult?> GetFirstOrDefaultAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector);

    Task<IEnumerable<T>> GetAllAsync(Expression<Func<T, bool>> predicate);

    Task<IEnumerable<TResult>> GetAllAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector);

    Task<IPage<TResult>> PageAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector,
        int pageSize,
        byte[] pageState);

    Task<IPage<TResult>> PageWithFilteringAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector,
        int pageSize,
        byte[] pageState);

    Task CreateAsync<TEntity>(TEntity entity);

    Task InsertBatchAsync<TEntity>(IEnumerable<TEntity> entities);

    Task UpdateAsync<TEntity>(TEntity entity);

    Task UpdateAsync<TEntity>(Expression<Func<TEntity, bool>> predicate, Expression<Func<TEntity, TEntity>> selector);

    Task DeleteAsync<TEntity>(TEntity entity);

    Task DeleteAsync<TEntity>(Expression<Func<TEntity, bool>> predicate);
}