namespace Kukui.Infrastructure.Database.ScyllaDb.Repository;

using System.Linq.Expressions;
using Cassandra;
using Cassandra.Data.Linq;
using Cassandra.Mapping;
using Mapping.Extensions;

public class Repository<T> : IRepository<T>
{
    private readonly TypeDefinitionCache<T> cache;
    private readonly Table<T> _entity;

    public Repository(ISession session, TypeDefinitionCache<T> cache)
    {
        this.cache = cache;
        Session = session;
        _entity = session.GetTable<T>();
    }

    public ISession Session { get; }

    public async Task<T?> FindAsync(params object[] parameters)
    {
        var mapper = new Mapper(Session);
        var columns = GetSearchColumns(parameters);
        return await mapper.FirstOrDefaultAsync<T>(
            $"WHERE {string.Join(" AND ", columns.Select(column => $"{column.Key} = ?"))}",
            columns.Select(x => x.Value)
                .ToArray());
    }

    public async Task<TResult?> FindAsync<TResult>(object[] parameters, Expression<Func<T, TResult>> selector)
    {
        var selectedColumns = new List<string>();
        switch (selector.Body)
        {
            case NewExpression newExpression:
                selectedColumns.AddRange(newExpression.Members.Select(x => x.Name));
                break;
            case MemberExpression memberExpression:
                selectedColumns.Add(memberExpression.Member.Name);
                break;
            default:
                throw new NotSupportedException("Only NewExpression and MemberExpression are supported");
        }

        var columns = GetSearchColumns(parameters);
        var columnMappings = cache.TypeDefinition!.GetDatabaseColumnMappings();
        var mapper = new Mapper(Session);
        return await mapper.FirstOrDefaultAsync<TResult>(
            $"""
             SELECT {string.Join(", ", selectedColumns.Select(x => columnMappings[x]))}
             FROM {cache.TypeDefinition!.KeyspaceName}.{cache.TypeDefinition.TableName}
             WHERE {string.Join(" AND ", columns.Select(column => $"{column} = ?"))}
             """,
            parameters);
    }

    public async Task<IEnumerable<T>> FindManyAsync(object[] parameters)
    {
        byte[] pageState = [];
        var mapper = new Mapper(Session);
        var columns = GetSearchColumns(parameters);
        var queryResult = new List<T>();
        while (pageState != null)
        {
            var pagedResult = await mapper.FetchPageAsync<T>(
                1000,
                pageState,
                $"WHERE {string.Join(" AND ", columns.Select(column => $"{column.Key} = ?"))}",
                columns.Select(x => x.Value)
                    .ToArray());
            queryResult.AddRange(pagedResult);

            pageState = pagedResult.PagingState;
        }

        return queryResult;
    }

    public async Task<IEnumerable<TResult>> FindManyAsync<TResult>(
        object[] parameters,
        Expression<Func<T, TResult>> selector)
    {
        var selectedColumns = new List<string>();
        var columnMappings = cache.TypeDefinition!.GetDatabaseColumnMappings();
        switch (selector.Body)
        {
            case NewExpression newExpression:
                selectedColumns.AddRange(newExpression.Members.Select(x => x.Name));
                break;
            case MemberExpression memberExpression:
                selectedColumns.Add(memberExpression.Member.Name);
                break;
            default:
                throw new NotSupportedException("Only NewExpression and MemberExpression are supported");
        }

        byte[] pageState = [];
        var mapper = new Mapper(Session);
        var columns = GetSearchColumns(parameters);
        var queryResult = new List<TResult>();
        while (pageState != null)
        {
            var pagedResult = await mapper.FetchPageAsync<TResult>(
                1000,
                pageState,
                $"""
                 SELECT {string.Join(", ", selectedColumns.Select(x => columnMappings[x]))}
                 FROM {cache.TypeDefinition!.KeyspaceName}.{cache.TypeDefinition.TableName}
                 WHERE {string.Join(" AND ", columns.Select(column => $"{column} = ?"))}
                 """,
                columns.Select(x => x.Value)
                    .ToArray());
            queryResult.AddRange(pagedResult);

            pageState = pagedResult.PagingState;
        }

        return queryResult;
    }

    public async Task<T?> GetFirstOrDefaultAsync(Expression<Func<T, bool>> predicate)
    {
        return await _entity.Where(predicate)
            .FirstOrDefault()
            .ExecuteAsync();
    }

    public async Task<TResult?> GetFirstOrDefaultAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector)
    {
        return await _entity.Where(predicate)
            .Select(selector)
            .FirstOrDefault()
            .ExecuteAsync();
    }


    public async Task<IEnumerable<T>> GetAllAsync(Expression<Func<T, bool>> predicate)
    {
        byte[] pageState = [];
        var query = _entity.Where(predicate)
            .SetPageSize(1000)
            .SetPagingState(pageState);
        var queryResult = new List<T>();
        while (pageState != null)
        {
            var pageResult = await query.ExecutePagedAsync();
            queryResult.AddRange(pageResult);
            pageState = pageResult.PagingState;
        }

        return queryResult;
    }

    public async Task<IEnumerable<TResult>> GetAllAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector)
    {
        byte[] pageState = [];
        var query = _entity.Where(predicate)
            .Select(selector)
            .SetPageSize(1000)
            .SetPagingState(pageState);
        var queryResult = new List<TResult>();
        while (pageState != null)
        {
            var pageResult = await query.ExecutePagedAsync();
            queryResult.AddRange(pageResult);
            pageState = pageResult.PagingState;
        }

        return queryResult;
    }

    public async Task<IPage<TResult>> PageAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector,
        int pageSize,
        byte[] pageState)
    {
        return await _entity.Where(predicate)
            .Select(selector)
            .SetPageSize(pageSize)
            .SetPagingState(pageState)
            .ExecutePagedAsync();
    }

    public async Task<IPage<TResult>> PageWithFilteringAsync<TResult>(
        Expression<Func<T, bool>> predicate,
        Expression<Func<T, TResult>> selector,
        int pageSize,
        byte[] pageState)
    {
        return await _entity.Where(predicate)
            .AllowFiltering()
            .Select(selector)
            .SetPageSize(pageSize)
            .SetPagingState(pageState)
            .ExecutePagedAsync();
    }

    public Task CreateAsync<TEntity>(TEntity entity)
    {
        return Session.GetTable<TEntity>()
            .Insert(entity, false)
            .ExecuteAsync();
    }

    public async Task InsertBatchAsync<TEntity>(IEnumerable<TEntity> entities)
    {
        var mapper = new Mapper(Session);
        var batch = mapper.CreateBatch(BatchType.Logged);
        foreach (var entity in entities)
        {
            batch.Insert(entity, false);
        }

        await mapper.ExecuteAsync(batch);
    }

    public async Task UpdateAsync<TEntity>(TEntity entity)
    {
        var mapper = new Mapper(Session);
        await mapper.UpdateAsync(entity);
    }

    public Task UpdateAsync<TEntity>(
        Expression<Func<TEntity, bool>> predicate,
        Expression<Func<TEntity, TEntity>> selector)
    {
        return Session.GetTable<TEntity>()
            .Where(predicate)
            .Select(selector)
            .Update()
            .ExecuteAsync();
    }

    public Task DeleteAsync<TEntity>(TEntity entity)
    {
        return new Mapper(Session).DeleteAsync(entity);
    }

    public Task DeleteAsync<TEntity>(Expression<Func<TEntity, bool>> predicate)
    {
        return Session.GetTable<TEntity>()
            .Where(predicate)
            .Delete()
            .ExecuteAsync();
    }

    private KeyValuePair<string, object>[] GetSearchColumns(object[] parameters)
    {
        var parameterTypes = parameters.Select(
                x => new
                {
                    Type = x.GetType(),
                    Value = x
                })
            .ToArray();

        var parametersCount = parameterTypes.GroupBy(x => x.Type)
            .Count();
        if (parameters.Length > 1 && parametersCount == 1)
        {
            return cache.GetPrimaryKeys()
                .Take(parameters.Length)
                .Zip(
                    parameterTypes.Select(x => x.Value),
                    (column, parameter) => new KeyValuePair<string, object>(column, parameter))
                .ToArray();
        }

        var dbColumns = cache.TypeDefinition !.GetColumnTypes()
            .Where(
                x => cache.GetPrimaryKeys()
                    .Contains(x.Name));

        return dbColumns.Take(parameters.Length)
            .Join(
                parameterTypes,
                x => x.ColumnType,
                x => x.Type,
                (column, parameter) => new KeyValuePair<string, object>(column.Name, parameter.Value))
            .ToArray();
    }
}