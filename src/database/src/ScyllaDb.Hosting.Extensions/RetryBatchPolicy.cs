namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

using Cassandra;

public class RetryBatchPolicy : IExtendedRetryPolicy
{
    private readonly IRetryPolicy _defaultPolicy = new DefaultRetryPolicy();

    public RetryDecision OnReadTimeout(
        IStatement query,
        ConsistencyLevel cl,
        int requiredResponses,
        int receivedResponses,
        bool dataRetrieved,
        int nbRetry)
    {
        return _defaultPolicy.OnReadTimeout(query, cl, requiredResponses, receivedResponses, dataRetrieved, nbRetry);
    }

    public RetryDecision OnWriteTimeout(
        IStatement query,
        ConsistencyLevel cl,
        string writeType,
        int requiredAcks,
        int receivedAcks,
        int nbRetry)
    {
        if (writeType == "BATCH_LOG" && nbRetry <= 3)
        {
            return RetryDecision.Retry(cl);
        }

        return _defaultPolicy.OnWriteTimeout(query, cl, writeType, requiredAcks, receivedAcks, nbRetry);
    }

    public RetryDecision OnUnavailable(
        IStatement query,
        ConsistencyLevel cl,
        int requiredReplica,
        int aliveReplica,
        int nbRetry)
    {
        return _defaultPolicy.OnUnavailable(query, cl, requiredReplica, aliveReplica, nbRetry);
    }

    public RetryDecision OnRequestError(IStatement statement, Configuration config, Exception ex, int nbRetry)
    {
        return RetryDecision.Rethrow();
    }
}