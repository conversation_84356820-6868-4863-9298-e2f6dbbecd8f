using System.Net;
using Cassandra;

namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

public class DockerAddressTranslator : IAddressTranslator
{
    private readonly AddressTranslationConfiguration[] _configuration;

    public DockerAddressTranslator(AddressTranslationConfiguration[] configuration)
    {
        _configuration = configuration;
    }

    public IPEndPoint Translate(IPEndPoint address)
    {
        var config =
            _configuration.FirstOrDefault(x => x.DockerIpAddress == address.Address.ToString());

        return config == null ? address : new ConnectionPoint(config.HostIpAddress, config.HostPort);
    }
}