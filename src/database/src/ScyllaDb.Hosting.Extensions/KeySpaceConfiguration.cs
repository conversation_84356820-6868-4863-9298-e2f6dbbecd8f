namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

public class KeySpaceConfiguration
{
    private const string SimpleStrategy = "SimpleStrategy";
    private const string NetworkTopologyStrategy = "NetworkTopologyStrategy";

    private readonly string _keySpaceStrategy;
    private readonly int _replicationFactor;

    private KeySpaceConfiguration(string keySpaceStrategy, int replicationFactor)
    {
        _keySpaceStrategy = keySpaceStrategy;
        _replicationFactor = replicationFactor;
    }

    internal static KeySpaceConfiguration CreateSimpleStrategyWithReplication(int replicationFactor)
    {
        return new KeySpaceConfiguration(SimpleStrategy, replicationFactor);
    }

    internal static KeySpaceConfiguration CreateNetworkTopologyStrategyWithReplication(int replicationFactor)
    {
        return new KeySpaceConfiguration(NetworkTopologyStrategy, replicationFactor);
    }

    public static implicit operator Dictionary<string, string>(KeySpaceConfiguration keySpaceConfiguration)
    {
        return new Dictionary<string, string>
        {
            { "class", keySpaceConfiguration._keySpaceStrategy },
            { "replication_factor", keySpaceConfiguration._replicationFactor.ToString() }
        };
    }
}