namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

using App.Metrics;
using Cassandra;
using Cassandra.Metrics;

internal static class ClusterBuilderExtensions
{
    internal static Builder WithContactPoints(this Builder builder, ScyllaDbConnectionSettings connectionSettings)
    {
        foreach (var connectionSetting in connectionSettings.ContactPoints)
        {
            var ipEndPoint = ScyllaDbConnectionSettings.GetConnectionEndpoint(connectionSetting);
            builder.AddContactPoint(ipEndPoint);
        }

        return builder;
    }

    internal static Builder WithAddressTranslator(this Builder builder, ScyllaDbConnectionSettings connectionSettings)
    {
        return connectionSettings.AddressTranslation.Any()
            ? builder.WithAddressTranslator(new DockerAddressTranslator(connectionSettings.AddressTranslation))
            : builder;
    }

    internal static Builder WithAuthentication(this Builder builder, ScyllaDbConnectionSettings connectionSettings)
    {
        return string.IsNullOrWhiteSpace(connectionSettings.Password)
            ? builder
            : builder.WithCredentials(connectionSettings.UserName, connectionSettings.Password);
    }

    internal static Builder AddDataDogMetrics(this Builder clusterBuilder, ScyllaDbConnectionSettings config)
    {
        if (!config.MetricsOptions.Enabled)
        {
            return clusterBuilder;
        }

        var metricsProvider = new MetricsBuilder().Report.ToDatadogHttp(config.MetricsOptions.DataDogReportingOptions)
            .Build();

        clusterBuilder.WithMetrics(
            metricsProvider.CreateDriverMetricsProvider(),
            new DriverMetricsOptions().SetEnabledNodeMetrics(NodeMetric.AllNodeMetrics)
                .SetEnabledSessionMetrics(SessionMetric.AllSessionMetrics));

        return clusterBuilder;
    }

    internal static Builder WithMetaDataSyncEnabled(this Builder clusterBuilder)
    {
        return clusterBuilder.WithMetadataSyncOptions(new MetadataSyncOptions().SetMetadataSyncEnabled(true));
    }

    internal static Builder WithQueryOptions(this Builder clusterBuilder, Action<QueryOptions> configure)
    {
        QueryOptions queryOptions = new();
        configure(queryOptions);
        return clusterBuilder.WithQueryOptions(queryOptions);
    }
}