// ReSharper disable UnusedAutoPropertyAccessor.Global

using App.Metrics.Reporting.Datadog;
using Microsoft.Extensions.Configuration;

namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

public class MetricsOptions
{
    private const string DataDogConfig = "datadogConfig";

    public MetricsOptions(IConfiguration configuration)
    {
        configuration.Bind(this);
        DataDogReportingOptions = new MetricsReportingDatadogOptions();
        configuration.Bind(DataDogConfig, DataDogReportingOptions.Datadog);
    }

    public bool Enabled { get; set; }

    public MetricsReportingDatadogOptions DataDogReportingOptions { get; set; }
}