namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

using System.Net;
using Cassandra;
using Infrastructure.Hosting.Common.Models;

public class ScyllaDbConnectionSettings
{
    private const string ScyllaSectionName = "database";
    private const string MetricsSectionName = "metrics";

    public ScyllaDbConnectionSettings(ServerConfiguration configuration, string sectionName = ScyllaSectionName)
    {
        configuration.BindServerConfig(sectionName, this);
        ApplicationName = configuration.DisplayName;
        MetricsOptions = new MetricsOptions(
            configuration.ServerConfigSection.GetSection($"{ScyllaSectionName}:{MetricsSectionName}"));
    }

    public string[] ContactPoints => TryGetConnectionStringBuilder()?.ContactPoints ?? [];

    public string? UserName => TryGetConnectionStringBuilder()?.Username;

    public string? Password => TryGetConnectionStringBuilder()?.Password;

    public bool IsClusterConnection => Nodes > 1;

    public int Nodes => ContactPoints.Length;

    public string ConnectionString { get; set; } = default!;

    public int DefaultPageSize { get; set; }

    public string DefaultKeySpace { get; set; } = default!;

    public MetricsOptions MetricsOptions { get; set; }

    public string ApplicationName { get; set; }

    public ReconnectionPolicy ReconnectionPolicy { get; set; } = default!;

    public AddressTranslationConfiguration[] AddressTranslation { get; set; } = [];

    internal static IPEndPoint GetConnectionEndpoint(string contactPoint)
    {
        return new ConnectionPoint(contactPoint);
    }

    private CassandraConnectionStringBuilder? TryGetConnectionStringBuilder()
    {
        try
        {
            return new CassandraConnectionStringBuilder(ConnectionString);
        }

        catch
        {
            return default;
        }
    }
}