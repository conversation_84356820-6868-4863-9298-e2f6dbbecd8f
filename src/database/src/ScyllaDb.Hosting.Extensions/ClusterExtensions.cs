using Cassandra;

namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

public static class ClusterExtensions
{
    public static void CreateKeySpaceIfNotExists(this ISession session,
        ScyllaDbConnectionSettings connectionSettings)
    {
        if (string.IsNullOrWhiteSpace(connectionSettings.DefaultKeySpace))
        {
            return;
        }

        var metadata = session.Cluster.Metadata.GetKeyspace(connectionSettings.DefaultKeySpace);

        if (metadata == null)
        {
            session.CreateKeyspaceIfNotExists(connectionSettings.DefaultKeySpace,
                GetKeySpaceReplicationOptions(connectionSettings));
        }

        session.ChangeKeyspace(connectionSettings.DefaultKeySpace);
    }

    private static Dictionary<string, string> GetKeySpaceReplicationOptions(
        ScyllaDbConnectionSettings connectionSettings)
    {
        return connectionSettings.IsClusterConnection
            ? KeySpaceConfiguration.CreateNetworkTopologyStrategyWithReplication(connectionSettings.Nodes)
            : KeySpaceConfiguration.CreateSimpleStrategyWithReplication(1);
    }
}