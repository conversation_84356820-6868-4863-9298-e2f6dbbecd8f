using System.Net;
using System.Net.Sockets;

namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

internal class ConnectionPoint
{
    public ConnectionPoint(string contactPoint)
    {
        if (string.IsNullOrWhiteSpace(contactPoint))
        {
            throw new ArgumentException("Contact point cannot be null or empty", nameof(contactPoint));
        }

        var parts = contactPoint.Split([":"], StringSplitOptions.RemoveEmptyEntries);

        Address = IPAddress.TryParse(parts[0], out var address)
            ? address
            : Dns.GetHostEntry(parts[0]).AddressList.First(x => x.AddressFamily == AddressFamily.InterNetwork);

        PortNumber = parts.Length == 1 ? 9042 : int.Parse(parts[1]);
    }

    public ConnectionPoint(string address, int portNumber)
        : this($"{address}:{portNumber}")
    {
    }

    private IPAddress Address { get; }

    private int PortNumber { get; }

    public static implicit operator IPEndPoint(ConnectionPoint connectionPoint)
    {
        return new IPEndPoint(connectionPoint.Address, connectionPoint.PortNumber);
    }
}