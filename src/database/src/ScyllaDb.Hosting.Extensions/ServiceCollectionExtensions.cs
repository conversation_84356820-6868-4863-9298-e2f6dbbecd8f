namespace Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;

using Abstractions.Hosting;
using Abstractions.Mappings;
using Cassandra;
using Cassandra.Mapping;
using Infrastructure.Hosting.Common.Extensions;
using Infrastructure.Hosting.Common.Models;
using Mapping.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddScyllaDb(
        this IServiceCollection services,
        ServerConfiguration serverConfiguration,
        Action<IScyllaDbConfigurationBuilder>? options = null)
    {
        ScyllaDbConfigurationBuilder configBuilder = new(services);
        options?.Invoke(configBuilder);
        Diagnostics.AddLoggerProvider(services.GetRequiredService<ILoggerProvider>());
        ScyllaDbConnectionSettings connectionSettings = new(serverConfiguration);

        services.AddSingleton(connectionSettings).AddClusterConfiguration();

        return services;
    }

    private static Builder ConfigureCluster(ScyllaDbConnectionSettings connectionSettings)
    {
        var clusterBuilder = Cluster.Builder()
            .WithContactPoints(connectionSettings)
            .WithAddressTranslator(connectionSettings)
            .WithAuthentication(connectionSettings)
            .WithApplicationName(connectionSettings.ApplicationName)
            .WithCompression(CompressionType.LZ4)
            .WithRetryPolicy(new RetryBatchPolicy())
            .WithReconnectionPolicy(
                new ExponentialReconnectionPolicy(
                    (long) connectionSettings.ReconnectionPolicy.MinDelay.TotalMilliseconds,
                    (long) connectionSettings.ReconnectionPolicy.MaxDelay.TotalMilliseconds))
            .WithRetryPolicy(new DefaultRetryPolicy());

        return clusterBuilder;
    }

    private static IServiceCollection AddClusterConfiguration(this IServiceCollection services)
    {
        return services.AddSingleton<ICluster>(
                provider =>
                {
                    var connectionSettings = provider.GetRequiredService<ScyllaDbConnectionSettings>();
                    return ConfigureCluster(connectionSettings)
                        .AddDataDogMetrics(connectionSettings)
                        .WithLoadBalancingPolicy(new TokenAwarePolicy(new RoundRobinPolicy()))
                        .WithMetaDataSyncEnabled()
                        .WithQueryOptions(
                            options => options.SetConsistencyLevel(
                                    connectionSettings.IsClusterConnection
                                        ? ConsistencyLevel.LocalQuorum
                                        : ConsistencyLevel.LocalOne)
                                .SetPageSize(connectionSettings.DefaultPageSize))
                        .Build();
                })
            .AddSingleton<ISession>(
                provider =>
                {
                    var connectionSettings = provider.GetRequiredService<ScyllaDbConnectionSettings>();
                    var session = provider.GetRequiredService<ICluster>().Connect();
                    session.CreateKeySpaceIfNotExists(connectionSettings);
                    ConfigureMappings(session, provider);
                    return session;
                });
    }

    private static void ConfigureMappings(ISession session, IServiceProvider serviceProvider)
    {
        var mappingConfigurationOptions = serviceProvider.GetService<IOptions<MappingConfigurationOptions>>();

        if (mappingConfigurationOptions == null)
        {
            return;
        }

        var mapConfigs = mappingConfigurationOptions.Value.GetEntityMapperConfigurations();
        var mappingProcessor = serviceProvider.GetRequiredService<IMappingProcessor>();

        foreach (var mapConfig in mapConfigs)
        {
            var mappingBuilder = mappingProcessor.ProcessMappings(mapConfig);

            session.UserDefinedTypes.Define(mappingBuilder.UdtMaps.ToArray());

            if (ScyllaMappingConfiguration.GetMapping(mapConfig.EntityType) != null) continue;

            MappingConfiguration.Global.Define(mappingBuilder.TableDefinition);
            MappingConfiguration.Global.Define(mappingBuilder.MaterializedViewDefinitions.ToArray());
        }
    }
}