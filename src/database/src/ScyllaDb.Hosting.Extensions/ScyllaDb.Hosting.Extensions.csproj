<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="App.Metrics.Abstractions" />
    <PackageReference Include="App.Metrics.Datadog" />
    <PackageReference Include="App.Metrics.Reporting.Datadog" />
    <PackageReference Include="CassandraCSharpDriver.AppMetrics" />
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Common\Kukui.Hosting.Common.csproj" />
    <ProjectReference Include="..\ScyllaDb.Abstractions\ScyllaDb.Abstractions.csproj" />
    <ProjectReference Include="..\ScyllaDb.Mapping.Extensions\ScyllaDb.Mapping.Extensions.csproj" />
  </ItemGroup>

</Project>