namespace Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

using System.Linq.Expressions;
using Cassandra.Mapping;

public interface ITypeDefinitionCache
{
    ITypeDefinition? TypeDefinition { get; }

    string[] GetPrimaryKeys();

    string[] GetPartitionKeys();

    string GetColumn(string propertyName);

    string GetColumn<TEntity, TProperty>(Expression<Func<TEntity, TProperty>> expression);
}