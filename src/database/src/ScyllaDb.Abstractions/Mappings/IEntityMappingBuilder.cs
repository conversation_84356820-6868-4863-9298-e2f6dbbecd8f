namespace Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

using System.Linq.Expressions;
using Cassandra;
using Cassandra.Mapping;

public interface IEntityMappingBuilder
{
    ITypeDefinition TableDefinition { get; }

    IEnumerable<ITypeDefinition> MaterializedViewDefinitions { get; }

    IEnumerable<UdtMap> UdtMaps { get; }

    IEnumerable<IndexMetadata> Indexes { get; }
}

public interface IEntityMappingBuilder<T> : IEntityMappingBuilder
{
    IEntityMappingBuilder<T> WithTable(string tableName, string keySpace, Action<Map<T>> primaryKeyConfiguration);

    IEntityMappingBuilder<T> WithMaterializedView<TMaterializedView>(
        string name,
        string keySpace,
        Action<Map<TMaterializedView>> primaryKeyConfiguration)
        where TMaterializedView : class;

    IEntityMappingBuilder<T> AutoMapColumns(Action<Map<T>>? columnMapConfig = null);

    IEntityMappingBuilder<T> WithUdtMap<TMap>(
        Expression<Func<T, TMap?>> property,
        Action<IUserDefinedMapping<TMap>> configure)
        where TMap : class, new();

    IEntityMappingBuilder<T> WithUdtMap<TMap>(
        Expression<Func<T, IEnumerable<TMap>>> property,
        Action<IUserDefinedMapping<TMap>> configure)
        where TMap : class, new();

    public IEntityMappingBuilder<T> WithGlobalIndex<TProperty>(Expression<Func<T, TProperty>> columnExpression);

    public IEntityMappingBuilder<T> WithLocalIndex<TProperty>(Expression<Func<T, TProperty>> columnExpression);

    public IEntityMappingBuilder<T> WithCollectionIndex<TProperty>(
        Expression<Func<T, TProperty>> columnExpression,
        CollectionIndexKind indexKind);
}