using System.Reflection;

namespace Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

public class MappingConfigurationOptions
{
    public HashSet<Assembly> AssembliesToScan { get; set; } = [];

    public IEnumerable<EntityMapperConfiguration> GetEntityMapperConfigurations()
    {
        return AssembliesToScan
            .SelectMany(x => x.GetTypes())
            .Where(x => !x.IsAbstract &&
                        !x.IsInterface &&
                        x
                            .GetInterfaces()
                            .Any(i =>
                                i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IEntityMapping<>)))
            .Select(entityMapper => new EntityMapperConfiguration(
                entityMapper,
                entityMapper
                    .GetInterfaces()
                    .First(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IEntityMapping<>))
                    .GetGenericArguments()[0]
            ))
            .ToList();
    }
}