namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

using System.Reflection;
using Cassandra.Mapping;

public static class ColumnMapExtensions
{
    public static string GetColumnName(this ColumnMap columnMap)
    {
        var fieldInfo = typeof(ColumnMap).GetField("_columnName", BindingFlags.NonPublic | BindingFlags.Instance);
        if (fieldInfo == null)
        {
            throw new InvalidOperationException("Could not find field for column definition");
        }

        return fieldInfo.GetValue(columnMap)
            ?.ToString() ?? throw new InvalidOperationException("Column definition is null");
    }

    public static Type GetColumnType(this ColumnMap columnMap)
    {
        var fieldInfo = typeof(ColumnMap).GetField("_memberInfoType", BindingFlags.NonPublic | BindingFlags.Instance);
        if (fieldInfo == null)
        {
            throw new InvalidOperationException("Could not find field for column definition");
        }

        return (Type) fieldInfo.GetValue(columnMap);
    }
}