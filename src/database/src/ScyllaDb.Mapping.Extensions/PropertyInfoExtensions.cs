using System.Linq.Expressions;
using System.Reflection;

namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

public static class PropertyInfoExtensions
{
    public static LambdaExpression CreateLambdaExpression<T>(this PropertyInfo property) where T : class
    {
        var parameterExpression = Expression.Parameter(typeof(T), "x");
        var propertyExpression = Expression.Property(parameterExpression, property);
        var funcType = typeof(Func<,>).MakeGenericType(typeof(T), property.PropertyType);
        return Expression.Lambda(funcType, propertyExpression, parameterExpression);
    }
}