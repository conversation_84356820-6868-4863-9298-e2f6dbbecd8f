using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using Microsoft.Extensions.DependencyInjection;

namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

public static class ServiceCollectionRegistrations
{
    public static IServiceCollection AddScyllaDbMappingsFromAssembly<T>(this IServiceCollection services)
    {
        return services.Configure<MappingConfigurationOptions>(options =>
            options.AssembliesToScan.Add(typeof(T).Assembly));
    }
}