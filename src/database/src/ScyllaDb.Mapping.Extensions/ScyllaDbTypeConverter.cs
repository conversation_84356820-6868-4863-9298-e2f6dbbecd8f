using Cassandra.Mapping.TypeConversion;

namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

/// <summary>
/// Converts a ScyllaDb type to a POCO type and vice versa. Useful for converting dictionary types to POCO types.
/// WIP. Needs to add support for searching and filtering.
/// </summary>
/// <typeparam name="TEntity">Application Type</typeparam>
/// <typeparam name="TDbType">Database Type</typeparam>
public abstract class ScyllaDbTypeConverter<TEntity, TDbType> : TypeConverter
{
    protected override Func<TDatabase, TPoco> GetUserDefinedFromDbConverter<TDatabase, TPoco>()
    {
        if (typeof(TDatabase).IsAssignableFrom(typeof(TDbType)) && typeof(TPoco) == typeof(TEntity))
        {
            Func<TDbType?, TEntity?> func = GetUserDefinedToDbConverter;
            return (Func<TDatabase, TPoco>)(object)func;
        }

        return default!;
    }

    protected override Func<TPoco, TDatabase> GetUserDefinedToDbConverter<TPoco, TDatabase>()
    {
        if (typeof(TDatabase).IsAssignableFrom(typeof(TDbType)) && typeof(TPoco) == typeof(TEntity))
        {
            Func<TEntity?, TDbType?> func = GetUserDefinedFromDbConverter;
            return (Func<TPoco, TDatabase>)(object)func;
        }

        return default!;
    }

    protected abstract TDbType? GetUserDefinedFromDbConverter(TEntity? entity);

    protected abstract TEntity? GetUserDefinedToDbConverter(TDbType? database);
}