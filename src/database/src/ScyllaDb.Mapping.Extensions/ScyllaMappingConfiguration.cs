using Cassandra.Mapping;

namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

public static class ScyllaMappingConfiguration
{
    public static ITypeDefinition? GetMapping(Type entityType)
    {
        var methodInfo = typeof(MappingConfiguration).GetMethod("Get");

        if (methodInfo == null)
        {
            throw new InvalidOperationException(
                $"Failed to find \"Get\" method on {nameof(MappingConfiguration)} class in Cassandra.Mapping assembly");
        }

        var generic = methodInfo.MakeGenericMethod(entityType);
        return (ITypeDefinition)generic.Invoke(MappingConfiguration.Global, null);
    }
}