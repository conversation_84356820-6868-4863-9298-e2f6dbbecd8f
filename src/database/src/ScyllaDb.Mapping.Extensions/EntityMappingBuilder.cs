namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

using System.Linq.Expressions;
using System.Reflection;
using Abstractions.Mappings;
using Cassandra;
using Cassandra.Mapping;

#pragma warning disable S6602
public class EntityMappingBuilder<T> : IEntityMappingBuilder<T>
    where T : class
{
    private readonly List<IndexMetadata> _indexes = [];
    private readonly IList<ITypeDefinition> _materializedViewMaps = [];
    private readonly List<UdtMap> _udtMaps = [];
    private Dictionary<string, ColumnMap> _entityColumnMappings = [];
    private Map<T> _tableMap;

    public ITypeDefinition TableDefinition => _tableMap;

    public IEnumerable<ITypeDefinition> MaterializedViewDefinitions => _materializedViewMaps;

    public IEnumerable<UdtMap> UdtMaps => _udtMaps;

    public IEnumerable<IndexMetadata> Indexes => _indexes;

    public IEntityMappingBuilder<T> WithTable(string tableName, string keySpace, Action<Map<T>> primaryKeyConfiguration)
    {
        _tableMap = new Map<T>().TableName(tableName).KeyspaceName(keySpace);
        primaryKeyConfiguration.Invoke(_tableMap);
        return this;
    }

    public IEntityMappingBuilder<T> WithMaterializedView<TMaterializedView>(
        string name,
        string keySpace,
        Action<Map<TMaterializedView>> primaryKeyConfiguration)
        where TMaterializedView : class
    {
        var materializedViewMap = new Map<TMaterializedView>().TableName(name).KeyspaceName(keySpace);
        primaryKeyConfiguration.Invoke(materializedViewMap);

        if (!_entityColumnMappings.Any())
            throw new InvalidOperationException(
                "Underlying table definitions is not found.Ensure you are defining the table before the materialized view.");

        foreach (var property in typeof(TMaterializedView).GetProperties())
        {
            AutoMapColumn(materializedViewMap, property, _entityColumnMappings[property.Name].GetColumnName());
        }

        _materializedViewMaps.Add(materializedViewMap);

        return this;
    }

    public IEntityMappingBuilder<T> AutoMapColumns(Action<Map<T>>? columnMapConfig = null)
    {
        columnMapConfig?.Invoke(_tableMap);

        _entityColumnMappings = TableDefinition.GetColumnMappings();

        foreach (var property in typeof(T).GetProperties())
        {
            if (_entityColumnMappings.ContainsKey(property.Name)) continue;

            AutoMapColumn(_tableMap, property, property.Name.ToSnakeCaseFormatting());
        }

        return this;
    }

    public IEntityMappingBuilder<T> WithUdtMap<TMap>(
        Expression<Func<T, TMap?>> property,
        Action<IUserDefinedMapping<TMap>> configure)
        where TMap : class, new()
    {
        UserDefinedMapping<TMap> mapping = new();
        configure.Invoke(mapping);
        _udtMaps.Add(mapping.UdtMap);

        return this;
    }

    public IEntityMappingBuilder<T> WithUdtMap<TMap>(
        Expression<Func<T, IEnumerable<TMap>>> property,
        Action<IUserDefinedMapping<TMap>> configure)
        where TMap : class, new()
    {
        UserDefinedMapping<TMap> mapping = new();
        configure.Invoke(mapping);
        _udtMaps.Add(mapping.UdtMap);

        return this;
    }

    public IEntityMappingBuilder<T> WithGlobalIndex<TProperty>(Expression<Func<T, TProperty>> columnExpression)
    {
        var propertyInfo = (columnExpression.Body as MemberExpression)?.Member as PropertyInfo;

        if (propertyInfo == null) throw new InvalidOperationException("Invalid property expression");

        var columnName = _entityColumnMappings[propertyInfo.Name].GetColumnName();
        var indexName = $"{TableDefinition.TableName}_by_{columnName}";
        var metaData = new IndexMetadata(
            indexName,
            "",
            IndexMetadata.IndexKind.Composites,
            new Dictionary<string, string>
            {
                { "target", $"({columnName})" }, { "type", IndexType.Global.ToString() }
            });

        _indexes.Add(metaData);
        return this;
    }

    public IEntityMappingBuilder<T> WithLocalIndex<TProperty>(Expression<Func<T, TProperty>> columnExpression)
    {
        var propertyInfo = (columnExpression.Body as MemberExpression)?.Member as PropertyInfo;

        if (propertyInfo == null) throw new InvalidOperationException("Invalid property expression");

        var columnName = _entityColumnMappings[propertyInfo.Name].GetColumnName();
        var indexName = $"{TableDefinition.TableName}_by_{columnName}";
        var metaData = new IndexMetadata(
            indexName,
            "",
            IndexMetadata.IndexKind.Composites,
            new Dictionary<string, string>
            {
                { "target", $"(({string.Join(",", TableDefinition.PartitionKeys)}), {columnName})" },
                { "type", IndexType.Local.ToString() }
            });

        _indexes.Add(metaData);

        return this;
    }

    public IEntityMappingBuilder<T> WithCollectionIndex<TProperty>(
        Expression<Func<T, TProperty>> columnExpression,
        CollectionIndexKind indexKind)
    {
        var propertyInfo = (columnExpression.Body as MemberExpression)?.Member as PropertyInfo;

        if (propertyInfo == null) throw new InvalidOperationException("Invalid property expression");

        var columnName = _entityColumnMappings[propertyInfo.Name].GetColumnName();
        var indexName = $"{TableDefinition.TableName}_by_{columnName}";
        var metaData = new IndexMetadata(
            indexName,
            "",
            IndexMetadata.IndexKind.Composites,
            new Dictionary<string, string>
            {
                { "target", $"{indexKind.ToString().ToUpper()}({columnName})" },
                { "type", IndexType.Collection.ToString() }
            });

        _indexes.Add(metaData);
        return this;
    }

    private static void AutoMapColumn<TEntity>(Map<TEntity> map, PropertyInfo property, string columnName)
        where TEntity : class
    {
        // ReSharper disable once ConvertToLocalFunction
        Action<ColumnMap> columnMap = x => x.WithName(columnName);
        var mapParameter = Expression.Constant(map, typeof(Map<TEntity>));
        var mapPropertyExpressionParameter = Expression.Constant(property.CreateLambdaExpression<TEntity>());
        var mapPropertyColumnName = Expression.Constant(columnMap, typeof(Action<ColumnMap>));

        var callExpression = Expression.Call(
            mapParameter,
            "Column",
            [property.PropertyType],
            mapPropertyExpressionParameter,
            mapPropertyColumnName);

        Expression.Lambda<Action>(callExpression).Compile().Invoke();
    }
}