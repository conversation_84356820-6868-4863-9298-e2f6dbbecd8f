namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

using System.Reflection;
using Cassandra.Mapping;

public static class TypeDefinitionExtensions
{
    public static Dictionary<string, ColumnMap> GetColumnMappings(this ITypeDefinition typeDefinition)
    {
        var fieldInfo = typeDefinition.GetType()
            .GetFields(BindingFlags.NonPublic | BindingFlags.Instance)
            .FirstOrDefault(x => x.FieldType == typeof(Dictionary<string, ColumnMap>));
        if (fieldInfo == null)
        {
            throw new InvalidOperationException("Could not find field for column mappings");
        }

        return (Dictionary<string, ColumnMap>) fieldInfo.GetValue(typeDefinition) ?? [];
    }

    public static DatabaseColumn[] GetColumnTypes(this ITypeDefinition typeDefinition)
    {
        return GetColumnMappings(typeDefinition)
            .Select(
                x => new DatabaseColumn
                {
                    Name = x.Value.GetColumnName(),
                    ApplicationColumnName = x.Key,
                    ColumnType = x.Value.GetColumnType()
                })
            .ToArray();
    }

    public static Dictionary<string, string> GetDatabaseColumnMappings(this ITypeDefinition typeDefinition)
    {
        return GetColumnMappings(typeDefinition)
            .Select(
                x => new
                {
                    ColumnName = x.Value.GetColumnName(),
                    PropertyName = x.Key
                })
            .ToDictionary(k => k.PropertyName, v => v.ColumnName);
    }
}