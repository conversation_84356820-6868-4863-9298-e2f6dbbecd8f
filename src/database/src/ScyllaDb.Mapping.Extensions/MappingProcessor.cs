using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

public class MappingProcessor : IMappingProcessor
{
    public IEntityMappingBuilder ProcessMappings(EntityMapperConfiguration mapperConfiguration)
    {
        var mapping = Activator.CreateInstance(mapperConfiguration.DefinedMappingType);
        var mapBuilder = typeof(EntityMappingBuilder<>).MakeGenericType(mapperConfiguration.EntityType);
        var builderInstance = (IEntityMappingBuilder)Activator.CreateInstance(mapBuilder);

        var configure = mapperConfiguration.DefinedMappingType.GetMethod("Configure")!;
        configure.Invoke(mapping, [builderInstance]);

        return builderInstance;
    }
}