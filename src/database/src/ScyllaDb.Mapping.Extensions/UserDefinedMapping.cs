namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

using Abstractions.Mappings;
using Cassandra;

public class UserDefinedMapping<T> : UserDefinedMapping, IUserDefinedMapping<T>
    where T : class, new()
{
    public IUserDefinedMapping<T> WithName(string udtName, string keyspace)
    {
        UdtMap = UdtMap.For<T>(udtName, keyspace);
        return this;
    }

    public IUserDefinedMapping<T> AutoMap(Action<UdtMap<T?>>? configure = null)
    {
        foreach (var property in typeof(T).GetProperties())
        {
            UdtMap.AddPropertyMapping(property, property.Name.ToSnakeCaseFormatting());
        }

        configure?.Invoke((UdtMap<T?>) UdtMap);
        return this;
    }
}

public abstract class UserDefinedMapping
{
    public UdtMap UdtMap { get; protected set; }
}