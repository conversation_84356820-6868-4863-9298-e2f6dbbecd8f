namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

using System.Collections.Concurrent;
using System.Linq.Expressions;
using System.Reflection;
using Abstractions.Mappings;
using Cassandra.Mapping;

public class TypeDefinitionCache : ITypeDefinitionCache
{
    protected readonly ConcurrentDictionary<Type, ITypeDefinition?> TypeDefinitions = new();
    protected readonly ConcurrentDictionary<Type, Dictionary<string, ColumnMap>?> ColumnMappings = new();

    public ITypeDefinition? TypeDefinition { get; }

    protected TypeDefinitionCache(Type entityType)
    {
        EntityType = entityType;
        TypeDefinition = TypeDefinitions.GetOrAdd(entityType, ScyllaMappingConfiguration.GetMapping);
        ColumnMappings.GetOrAdd(entityType, _ => TypeDefinition?.GetColumnMappings());
    }

    protected Type EntityType { get; }

    public string[] GetPrimaryKeys()
    {
        return TypeDefinition?.PartitionKeys
            .Union(TypeDefinition.ClusteringKeys.Select(x => x.Item1))
            .ToArray() ?? [];
    }

    public string[] GetPartitionKeys()
    {
        return TypeDefinition?.PartitionKeys ?? [];
    }

    public string GetColumn(string propertyName)
    {
        if (!ColumnMappings.TryGetValue(EntityType, out var columnMappings))
            throw new InvalidOperationException($"Column mappings not found for {EntityType.Name}");

        if (columnMappings == null || !columnMappings.TryGetValue(propertyName, out var columnMap))
            throw new InvalidOperationException($"Column mapping not found for {EntityType.Name}.{propertyName}");

        return columnMap.GetColumnName();
    }

    public string GetColumn<TEntity, TProperty>(Expression<Func<TEntity, TProperty>> expression)
    {
        var propertyName = expression.Body as MemberExpression ??
                           throw new ArgumentException("Expression must be a member expression");

        return GetColumn(propertyName.Member.Name);
    }
}

public class TypeDefinitionCache<T> : TypeDefinitionCache
{
    public TypeDefinitionCache() : base(typeof(T))
    {
    }

    public string GetColumn<TProperty>(Expression<Func<T, TProperty>> columnExpression)
    {
        if (columnExpression.Body is not MemberExpression memberExpression)
            throw new ArgumentException("Expression must be a member expression");

        return TypeDefinition?.GetColumnDefinition((PropertyInfo) memberExpression.Member)
                   .ColumnName ??
               throw new InvalidOperationException($"Column mapping not found for {memberExpression.Member.Name}");
    }
}