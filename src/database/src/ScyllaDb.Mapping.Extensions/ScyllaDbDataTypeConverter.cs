using Cassandra;

namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

public class ScyllaDbDataTypeConverter
{
    private static Dictionary<Type, string> _typeMap = new()
    {
        { typeof(string), "text" },
        { typeof(int), "int" },
        { typeof(long), "bigint" },
        { typeof(Guid), "uuid" },
        { typeof(TimeUuid), "timeuuid" },
        { typeof(DateTime), "timestamp" },
        { typeof(DateTimeOffset), "timestamp" },
        { typeof(bool), "boolean" },
        { typeof(float), "float" },
        { typeof(double), "double" },
        { typeof(decimal), "decimal" },
        { typeof(short), "smallint" },
        { typeof(sbyte), "tinyint" },
        { typeof(byte[]), "blob" },
        { typeof(IDictionary<,>), "map<K,V>" },
        { typeof(ISet<>), "set<T>" },
        { typeof(IEnumerable<>), "list<T>" },
        { typeof(LocalDate), "date" },
        { typeof(LocalTime), "time" },
        { typeof(Duration), "duration" }
    };

    public static string GetDbType(Type columnType)
    {
        if (_typeMap.TryGetValue(columnType, out var dbType))
        {
            return dbType;
        }

        if (columnType.IsGenericType)
        {
            var genericType = columnType.GetGenericTypeDefinition();
            if (_typeMap.TryGetValue(genericType, out var dbTypeGeneric))
            {
                return dbTypeGeneric;
            }
        }

        return string.Empty;
    }
}