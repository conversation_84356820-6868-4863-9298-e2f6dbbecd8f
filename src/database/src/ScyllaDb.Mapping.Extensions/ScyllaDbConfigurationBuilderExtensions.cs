using System.Reflection;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Hosting;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

public static class ScyllaDbConfigurationBuilderExtensions
{
    public static IScyllaDbConfigurationBuilder AddMappings(this IScyllaDbConfigurationBuilder builder,
        params Assembly[] assemblies)
    {
        builder.Services.Configure<MappingConfigurationOptions>(
            options =>
            {
                foreach (var assembly in assemblies)
                {
                    options.AssembliesToScan.Add(assembly);
                }
            });
        builder.Services.TryAddSingleton(typeof(IEntityMappingBuilder<>), typeof(EntityMappingBuilder<>));
        builder.Services.TryAddSingleton<IMappingProcessor, MappingProcessor>();

        return builder;
    }

    public static IScyllaDbConfigurationBuilder AddMappingsFromAssembly<T>(this IScyllaDbConfigurationBuilder builder)
    {
        return builder.AddMappings(typeof(T).Assembly);
    }
}