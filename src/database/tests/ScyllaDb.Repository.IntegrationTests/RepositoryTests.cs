namespace ScyllaDb.Repository.IntegrationTests;

using Database;
using FluentAssertions;
using Kukui.Infrastructure.Database.ScyllaDb.Repository;
using Kukui.Infrastructure.Testing.IntegrationTests;
using Microsoft.Extensions.DependencyInjection;

public class RepositoryTests : IntegrationTestBase<IRepository<Order>>
{
    private readonly TestEnvironment _testEnvironment;

    public RepositoryTests(TestEnvironment testEnvironment)
    {
        _testEnvironment = testEnvironment;
    }

    [Fact]
    public async Task FindAsync_ReturnsRecords()
    {
        var firstOrder = _testEnvironment.TestData.First();

        await SystemUnderTest.InsertBatchAsync(_testEnvironment.TestData);
        var result = await SystemUnderTest.FindAsync(
        [
            _testEnvironment.EntityId, firstOrder.OrderId
        ]);

        result.Should()
            .BeEquivalentTo(firstOrder);
    }


    protected override IRepository<Order> CreateSystemUnderTest()
    {
        return _testEnvironment.Services.GetRequiredService<IRepository<Order>>();
    }
}