namespace ScyllaDb.Repository.IntegrationTests.Database;

using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

public class LineItemMapping : IEntityMapping<LineItem>
{
    public void Configure(IEntityMappingBuilder<LineItem> builder)
    {
        builder.WithTable(
                "line_items",
                "core",
                map => map.PartitionKey(x => x.OrderId)
                    .ClusteringKey(x => x.ProductId))
            .AutoMapColumns();
    }
}