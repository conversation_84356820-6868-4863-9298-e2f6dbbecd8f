namespace ScyllaDb.Repository.IntegrationTests.Database;

using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

public class OrderMapping : IEntityMapping<Order>
{
    public void Configure(IEntityMappingBuilder<Order> builder)
    {
        builder.WithTable(
                "orders",
                "core",
                map => map.PartitionKey(x => x.EntityId)
                    .ClusteringKey(x => x.OrderId))
            .AutoMapColumns();
    }
}