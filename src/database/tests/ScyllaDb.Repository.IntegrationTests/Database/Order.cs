namespace ScyllaDb.Repository.IntegrationTests.Database;

public class Order
{
    public Guid EntityId { get; set; }

    public Guid OrderId { get; set; }

    public Guid CustomerId { get; set; }

    public DateTimeOffset OpenDate { get; set; }

    public DateTimeOffset? CloseDate { get; set; }

    public decimal Total { get; set; }

    public DateTimeOffset CreatedDate { get; set; } = DateTimeOffset.UtcNow;
}