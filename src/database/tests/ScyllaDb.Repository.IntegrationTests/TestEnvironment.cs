namespace ScyllaDb.Repository.IntegrationTests;

using Database;
using Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;
using Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;
using Kukui.Infrastructure.Database.ScyllaDb.Repository;
using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.Testing.Containers.Database;
using Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;
using Kukui.Infrastructure.Testing.IntegrationTests;
using Kukui.Infrastructure.Testing.xUnit.AssemblyFixture;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

public class TestEnvironment : TestEnvironmentBase, IAssemblyFixture<TestEnvironment>
{
    private readonly IServiceCollection _services = new ServiceCollection();

    private readonly IConfigurationBuilder _configurationBuilder;

    public IServiceProvider Services { get; private set; }

    public Guid EntityId { get; private set; }

    public IEnumerable<Order> TestData { get; private set; }

    public TestEnvironment()
    {
        Configure(x => x.AddScyllaDb());
        _configurationBuilder = new ConfigurationBuilder().AddJsonFile("configs/config.json");
    }

    protected override async Task AfterInitializationAsync()
    {
        var scyllaDbContainer = GetContainer<ScyllaDbContainer>();
        _configurationBuilder.AddInMemoryCollection(
            new Dictionary<string, string?>
            {
                { "Server:Database:ConnectionString", scyllaDbContainer.GetConnectionString() }
            });

        await foreach (var script in GetScriptFilesContent(searchPattern: "*.cql"))
            await scyllaDbContainer.ExecScriptAsync(script);

        _services.AddLogging(builder => builder.AddConsole());
        _services.AddScyllaDb(
            new ServerConfiguration(_configurationBuilder.Build()),
            options => options.AddMappingsFromAssembly<TestEnvironment>());

        Services = _services.AddSingleton(typeof(TypeDefinitionCache<>))
            .AddSingleton(typeof(IRepository<>), typeof(Repository<>))
            .BuildServiceProvider();

        EntityId = Guid.NewGuid();
        TestData = FakeService.GetOrders(EntityId)
            .Generate(1000);
    }
}