CREATE KEYSPACE IF NOT EXISTS core WITH REPLICATION = {'class': 'SimpleStrategy', 'replication_factor': 1};

CREATE TABLE IF NOT EXISTS core.orders
(
    entity_id    UUID,
    order_id     UUID,
    customer_id  UUID,
    open_date    TIMESTAMP,
    close_date   TIMESTAMP,
    total        DECIMAL,
    created_date TIMESTAMP,

    PRIMARY KEY (entity_id, order_id)
);

CREATE TABLE IF NOT EXISTS core.line_items
(
    entity_id   UUID,
    order_id    UUID,
    product_id  UUID,
    description TEXT,
    quantity    INT,
    category    TEXT,
    price       DECIMAL,
    cost        DECIMAL,
    discount    DECIMAL,

    PRIMARY KEY (order_id, product_id)
);