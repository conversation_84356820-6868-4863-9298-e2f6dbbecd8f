namespace ScyllaDb.Repository.IntegrationTests;

using Bogus;
using Database;

public static class FakeService
{
    public static Faker<Order> GetOrders(Guid entityId)
    {
        return new Faker<Order>().RuleFor(o => o.EntityId, entityId)
            .RuleFor(o => o.OrderId, f => f.Random.Guid())
            .RuleFor(o => o.CustomerId, f => f.Random.Guid())
            .RuleFor(o => o.OpenDate, f => f.Date.Past())
            .RuleFor(o => o.CloseDate, f => f.Date.Future())
            .RuleFor(o => o.Total, f => f.Random.Decimal(10m, 1000m))
            .RuleFor(o => o.CreatedDate, f => f.Date.Past());
    }

    public static Faker<LineItem> CreateOrderLineItem(Order order)
    {
        return new Faker<LineItem>().RuleFor(x => x.EntityId, order.EntityId)
            .RuleFor(l => l.OrderId, order.OrderId)
            .RuleFor(l => l.ProductId, f => f.Random.Guid())
            .RuleFor(l => l.Description, f => f.Commerce.ProductName())
            .RuleFor(
                l => l.Category,
                f => f.Commerce
                    .Categories(1)[0])
            .RuleFor(l => l.Quantity, f => f.Random.Int(1, 10))
            .RuleFor(l => l.Price, f => f.Random.Decimal(1m, 100m))
            .RuleFor(x => x.Cost, (f, i) => i.Price * f.Random.Decimal(0.5m, 0.9m))
            .RuleFor(x => x.Discount, (f, i) => i.Price * f.Random.Decimal(0.1m, 0.5m));
    }
}