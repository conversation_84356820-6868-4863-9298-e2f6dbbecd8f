## This project will include common database packages that simplify database related operations.

## ScyllaDb cluster connection

Contains common approach for registering and connecting to scyllaDb clustering
using [C# Cassandra driver](https://docs.datastax.com/en/developer/csharp-driver/3.20/)
This extension method configures:

- Driver logging provider
- Sent metrics data to Datadog
- Configures reconnection policy
- Configures retry policy
- Setup token aware policy
- Setups some default query options like : default page size and default consistency level

### Usage

<h5 a><strong><code>Program.cs</code></strong></h5>

```csharp
void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services.AddScyllaDb(configuration);
}

void ConfigureServices(IServiceProvider serviceProvider, ServerConfiguration configuration)
{
    ISession session = serviceProvider.GetRequiredService<ISession>();
    ILogger<Program> logger = serviceProvider.GetRequiredService<ILogger<Program>>();
    logger.LogInformation("Connected to ScyllaDb {ClusterName}", session.Cluster.Metadata.ClusterName);
}

await ServerHostRunner.RunApp(args, RegisterServices, ConfigureServices);
```

### Mapping

Need to map application entities to database schema.
In order to configure mapping you need to implement `IEntityMappingBuilder<T>` interface.

<h5 a><strong><code>LocationEntityMapping.cs</code></strong></h5>

```csharp
public class LocationEntityMapping : IEntityMapping<LocationEntity>
{
    public void Configure(IEntityMappingBuilder<LocationEntity> builder)
    {
        builder
            .WithTable("location", "core", map =>
                map
                  .PartitionKey(x => x.Id)
                  .ClusteringKey(x => x.Name)
            )
            .WithUdtMap(x => x.Address, map => map.WithName("location_address", "core").AutoMap())
            .WithUdtMap(x => x.Address.Coordinates, map => map.WithName("geo_coordinate", "core").AutoMap())
            .AutoMapColumns(options =>
                options
                    .Column(x => x.CreatedDate, map => map.WithName("created_date_time"))
                    .Column(x => x.ModifiedDate, map => map.WithName("modified_date_time"))
            )
            .WithMaterializedView<LocationsByEntityView>(
                "locations_by_entity",
                "core",
                map => map
                    .PartitionKey(x => x.EntityId)
                    .ClusteringKey(x => x.Name, SortOrder.Ascending)
                    .ClusteringKey(x => x.Id)
            )
            .WithCollectionIndex(x => x.ExternalIdentification, CollectionIndexKind.Values)
            .WithCollectionIndex(x => x.NameSearch, CollectionIndexKind.Values)
            .WithCollectionIndex(x => x.CpNameSearch, CollectionIndexKind.Values);
    }
}
```

In order to provide this mapping configuration you need to register mapping as follows:

<h5 a><strong><code>Program.cs</code></strong></h5>

```csharp
    services.AddScyllaDb(
        configuration.ServerConfiguration,
        options => options.AddMappingsFromAssembly<LocationEntitiy>()
    )
```