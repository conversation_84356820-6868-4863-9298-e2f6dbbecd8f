// See https://aka.ms/new-console-template for more information

using Cassandra;
using Cassandra.Data.Linq;
using Cassandra.Mapping;
using Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;
using Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;
using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.Hosting.Server;
using Microsoft.Extensions.DependencyInjection;
using Sample.Console.ScyllaDb;

void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services.AddScyllaDb(configuration, options => options.AddMappingsFromAssembly<Program>());
}

void ConfigureServices(IServiceProvider serviceProvider, ServerConfiguration configuration)
{
    var session = serviceProvider.GetRequiredService<ISession>();
    var globalConfig = MappingConfiguration.Global;
    var view = session.GetTable<LocationsByEntityView>();
    var record = view.FirstOrDefault().Execute();
}

await ServerHostRunner.RunApp(args, RegisterServices, ConfigureServices);