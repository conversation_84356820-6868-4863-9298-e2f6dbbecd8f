namespace Sample.Console.ScyllaDb;

using Cassandra.Mapping;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

public record BusinessEntity
{
    public Guid Id { get; set; }

    public string Name { get; set; }

    public string AdminEmail { get; set; }

    public bool Enabled { get; set; }

    public string? State { get; set; }

    public DateTimeOffset CreatedDate { get; set; }

    public DateTimeOffset? ModifiedDate { get; set; }

    public List<string> NameSearch { get; set; }
}

public enum State
{
    Active,
    Inactive,
    Deleted
}

public class BusinessEntityMapping
    //: IEntityMapping<BusinessEntity>
{
    public void Configure(IEntityMappingBuilder<BusinessEntity> builder)
    {
        builder.WithTable(
                "business_entity",
                "core",
                map => map.PartitionKey(x => x.Id).ClusteringKey(x => x.Name, SortOrder.Ascending))
            .AutoMapColumns(
                options => options.Column(x => x.CreatedDate, map => map.WithName("created_date_time"))
                    .Column(x => x.ModifiedDate, map => map.WithName("modified_date_time"))
                // .Column(x => x.State, map => map.WithDbType<State>())
            );
    }
}