{"DisplayName": "Sample ScyllaDb Application", "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}}, "Server": {"DataBase": {"ConnectionString": "Contact Points=host.docker.internal:9042", "DefaultPageSize": 100, "DefaultKeyspace": "core", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}, "Metrics": {"Enabled": false}}, "MessageService": {"Url": "nats://localhost:4222", "Username": "admin", "Password": "secret"}, "Vault": {"Enabled": false, "Address": "http://docker-sandbox.server.cluster:8200/", "Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}