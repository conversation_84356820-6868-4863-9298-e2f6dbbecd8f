namespace MSO.Retention.Infrastructure.Database.EntityModels;

using Cassandra;

public class EmailBlastRecipientByEntityCampaignView
{
    public TimeUuid EntityCampaignId { get; set; }

    public TimeUuid LocationCampaignId { get; set; }

    public TimeUuid Id { get; set; }

    public string EmailAddress { get; set; }

    public string CustomerId { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public bool FleetAccount { get; set; }

    public DateTimeOffset? LastVisit { get; set; }

    public decimal? TotalAmountSpent { get; set; }

    public string? State { get; set; }
}