namespace MSO.Retention.Infrastructure.Database.EntityMappings;

using EntityModels;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

public class EmailBlastRecipientMapping : IEntityMapping<EmailBlastRecipient>
{
    public void Configure(IEntityMappingBuilder<EmailBlastRecipient> builder)
    {
        builder.WithTable(
                "email_blast_recipients",
                "retention",
                map => map.PartitionKey(x => x.LocationCampaignId)
                    .ClusteringKey(x => x.EmailAddress)
                    .ClusteringKey(x => x.Id))
            .AutoMapColumns()
            .WithUdtMap(x => x.Vehicles, map => map.WithName("customer_vehicle", "retention").AutoMap())
            .WithMaterializedView<EmailBlastRecipientByEntityCampaignView>(
                "recipients_by_entity_campaign_id",
                "retention",
                map => map.PartitionKey(x => x.EntityCampaignId, x => x.EmailAddress)
                    .ClusteringKey(x => x.LocationCampaignId)
                    .ClusteringKey(x => x.Id));
    }
}