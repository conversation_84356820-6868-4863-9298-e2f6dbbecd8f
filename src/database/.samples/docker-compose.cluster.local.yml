version: "3.8"

services:
  node1:
    image: scylladb/scylla:5.4.0
    command: --seeds=node1 --smp 1 --memory 1G --listen-address *********1 --api-address 0.0.0.0 --developer-mode 1 --authenticator Pass<PERSON><PERSON>uthenticator --authorizer <PERSON><PERSON><PERSON><PERSON><PERSON> --reactor-backend=epoll
    ports:
      - "9142:9042"
    volumes:
      - "./data/node1:/var/lib/scylla"
      - "./upload:/var/lib/scylla/upload"
    healthcheck:
      test: cqlsh *********1 -u cassandra -p cassandra -e 'SELECT now() FROM system.local' || exit -1
      start_period: 15s
      interval: 5s
      timeout: 10s
      retries: 10
    networks:
      scylla_cluster:
        ipv4_address: *********1

  node2:
    image: scylladb/scylla:5.4.0
    command: --seeds=node1 --smp 1 --memory 1G --listen-address *********2 --api-address 0.0.0.0 --developer-mode 1 --authenticator <PERSON><PERSON><PERSON><PERSON>enticator --authorizer <PERSON><PERSON><PERSON><PERSON><PERSON> --reactor-backend=epoll
    ports:
      - "9242:9042"
    volumes:
      - "./data/node2:/var/lib/scylla"
    healthcheck:
      test: cqlsh *********2 -u cassandra -p cassandra -e 'SELECT now() FROM system.local' || exit -1
      start_period: 15s
      interval: 5s
      timeout: 10s
      retries: 10
    networks:
      scylla_cluster:
        ipv4_address: *********2

  node3:
    image: scylladb/scylla:5.4.0
    command: --seeds=node1 --smp 1 --memory 1G --listen-address *********3 --api-address 0.0.0.0 --developer-mode 1 --authenticator PasswordAuthenticator --authorizer CassandraAuthorizer --reactor-backend=epoll
    ports:
      - "9342:9042"
    volumes:
      - "./data/node3:/var/lib/scylla"
    healthcheck:
      test: cqlsh *********3 -u cassandra -p cassandra -e 'SELECT now() FROM system.local' || exit -1
      start_period: 15s
      interval: 5s
      timeout: 10s
      retries: 10
    networks:
      scylla_cluster:
        ipv4_address: *********3

networks:
  scylla_cluster:
    driver: bridge
    ipam:
      config:
        - subnet: *********/24
          gateway: *********