## This project setup back infrastructure for messaging leveraging [NATS](https://nats.io/) messaging system.

Packages provides abstractions built around [NATS C# driver v2](https://github.com/nats-io/nats.net.v2) for implmenting
request-response(rqrs)
and publishing message to stream by setting up consumers.

### NATS Core - request-response(rqrs) pattern [Docs](https://docs.nats.io/nats-concepts/core-nats/reqreply)

### Usage

#### Publisher side

- Adding nats services which wires up all necessary dependencies

```csharp
public class AppConfigurator : WebConfiguratorBase
{
    protected override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddNatsSupport();
    }
}
````

We are using `INatsRequestClient<TRquest, TResponse>` to send message to subscriber and get back the response.

```csharp
app.MapPost("api/rqrs/locations",
            async ([FromServices] INatsRequestClient<GetLocationRequest, GetLocationResponse> handler,
                [FromBody] GetLocationRequest request) =>
            {
                GetLocationResponse? response = await handler.SendRequestAsync(request);
                return response?.Data;
            });
```

What happens behind the scenies is that `IRequestClient<,>` send message to subject using `TRquest` message fullname(
with namespace);
Subscriber is listening to the same subject and consumes the message and provide response within a given time window
which can be configured(`RequestTimeout=10s`).

#### Subscriber side

- Adding nats services which wires up all necessary dependencies by registering
  all `IRequestHandler<TRequest, TResponse>`

```csharp
public class WebApiConfigurator : WebConfiguratorBase
{
    protected override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddNatsSupport(options =>
        {
            options.AddRequestHandlers(h => h.RegisterRequestHandlersFromAssemblyContaining<WebApiConfigurator>());
        });
    }
}
```

- Next we provide implementation of `IRequestHandler<TRequest, TResponse>` interface and use it to handle incoming
  request

```csharp
public class GetLocationRequestHandler
    : INatsRequestHandler<GetLocationRequest, GetLocationResponse>
{
    private readonly ILogger _logger;

    public GetLocationRequestHandler(ILogger<GetLocationRequestHandler> logger)
    {
        _logger = logger;
    }

    public async ValueTask<GetLocationResponse> HandleRequestAsync(GetLocationRequest request,
        CancellationToken cancellationToken = default)
    {
       // business logic
    }
}
```

### More advanced usage using `OneOf` package

- `OneOf` package is used to provide support for discriminated unions in C#. They provide a way to model a c# Type that
  can be one of many types. [GitHub](https://github.com/mcintyre321/OneOf)

Lets defined our request and response models

```csharp
public record GetLocationRequest : NatsRequest<GetLocationResponse>
{
    public GetLocationRequest(string locationId)
    {
        LocationId = locationId;
    }

    public string LocationId { get; }
}
```

- Next we define our response model using `OneOf` package and return more than one type in response to request

```csharp

[GenerateOneOf]
public partial class GetLocationResponse : OneOfBase<LocationData, LocationNotFound, OperationNotAllowed>;

public class LocationNotFound
{
    public string Description { get; set; }
}

public class OperationNotAllowed
{
    public string Description { get; set; }
}

public class LocationData
{
    public string LocationId { get; set; }
    
    public string Name { get; set; }
    
    public bool Enabled { get; set; } = true;
}
```

Here is some naive sample implementation of returning more than one result from request handler

```csharp
public class GetLocationRequestHandler
    : INatsRequestHandler<GetLocationRequest, GetLocationResponse>
{
    private readonly ILogger _logger;

    public GetLocationRequestHandler(ILogger<GetLocationRequestHandler> logger)
    {
        _logger = logger;
    }

    public async ValueTask<GetLocationResponse> HandleRequestAsync(GetLocationRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing  {RequestType} request with {@Payload}", nameof(GetLocationRequest),
            request);
        
        await Task.Delay(TimeSpan.Zero, cancellationToken);

        return request.LocationId switch
        {
            "0" => new LocationNotFound { Description = "Location not found in a given range. Try expand date range" },
            "66" => new OperationNotAllowed { Description = "Operation is not allowed for this location" },
            _ => new LocationData { LocationId = request.LocationId, Name = $"Location {request.LocationId}" }
        };
    }
}
```

Publisher can consume the response and based on the result to take different actions

```csharp
app.MapPost("api/rqrs/locations",
            async ([FromServices] INatsRequestClient<GetLocationRequest, GetLocationResponse> requestClient,
                [FromBody] GetLocationRequest request) =>
            {
                GetLocationResponse response = await requestClient.SendRequestAsync(request);
                
                return response.Match(
                    data => Results.Ok(data),
                    notFound => Results.NotFound(notFound.Description),
                    notAllowed => Results.BadRequest(notAllowed.Description)
                );
            });
```

### JetStream - publishing messages to stream and consuming them

In order to do that we need to model three things: `Events`, `Streams` and `Consumers`

#### Events

Those are basically C# POCO objects that transfers data between publisher and consumer by persisting them on the file
system to provide temporal decoupling between two parties.
Each event should inherit from `EventBase<TStream>` where `TStream` is a class that represents a stream of events.

```csharp
public class EmailCampaignCreated : EventBase<RetentionCampaignsStream>
{
    public int LocationId { get; set; }
    
    public int CampaignId { get; set; }
    
    public DateTimeOffSet CreateDateTime { get; } = DateTimeOffSet.UtcNow;
}
```

#### Streams

Streams are a way to organize(group) events in way that share similar characteristics.
For example we might want to group all events related to retention into one stream.

Each stream implements `NatsStream` abstract class and can configure/overwrite some default configuration for the
stream.

```csharp
public class RetentionCampaignsStream : NatsStream
{
    protected override int MaxMessageSize { get; } = 256 * 1024;

    protected override TimeSpan MessageTtl { get; set; } = TimeSpan.FromDays(1);

    protected override int Replicas { get; } = 1;
}
```

#### Consumers

Consumers are a way to consume events from the stream. Each consumer should implement `NatsConsumer` abstract class
which provides some default configuration for the consumer with option to overwrite some configuration settings.
`NatsConsumer` is a durable pull consumer which can provide options to consume messages one by one or in batch.
Next version will provide support for ad-hoc (epehemeral) consumers .

```csharp
public class EmailCampaignCreatedConsumer : NatsConsumer, IEventConsumer<EmailCampaignCreated>
{
    private readonly ILogger _logger;

    public EmailCampaignCreatedConsumer(ILogger<EmailCampaignCreatedConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(NatsJSMsg<EmailCampaignCreated> context, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Consumed {EventType} event with {@Payload}", nameof(EmailCampaignCreated), context);
        return Task.CompletedTask;
    }
}
```

#### Publisher

Next we need to inject `IPublisher` interface and use it to publish message to stream

```csharp
await WebHostRunner.RunApp<AppConfigurator>(args,
    (app, _) =>
    {
        app.MapPost("api/js/emails/created/{numbers:int}",
            async ([FromServices] IPublisher<EmailCampaignCreated> publisher, [FromRoute] int numbers) =>
            {
                foreach (int num in Enumerable.Range(1, numbers))
                {
                    await publisher.PublishAsync(
                        new EmailCampaignCreated { LocationId = num * 100, CampaignId = num });
                }
            });
    });
```

#### Configuration

The last thing to is to wire up all dependencies and create stream/consumers if they do not exists.

Automatically scan assembly(assemblies) and register all consumers

```csharp
await ServerHostRunner.RunApp(args, (services, config) =>
{
    services.AddNatsSupport(options =>
    {
        options.ConfigureConsumers(consumer =>
        {
            consumer.RegisterConsumersFromAssemblyContaining<EmailCampaignCreatedConsumer>();
        });
    });
});
```

Or manually register consumers

```csharp
await ServerHostRunner.RunApp(args, (services, config) =>
{
    services.AddNatsSupport(options =>
    {
        options.ConfigureConsumers(consumer =>
        {
            consumer.AddConsumer<EmailCampaignCreatedConsumer>();
            consumer.AddConsumer<OrdersConsumer>();
        });
    });
});
```

This configuration will:

1. Create all streams(if they don't exists) based on the events consumed by registered consumers.
2. Create all the consumers(if they don't exists) and wire them to redirect consumed messages from the stream
   to `IEventConsumer<TEvent>`.