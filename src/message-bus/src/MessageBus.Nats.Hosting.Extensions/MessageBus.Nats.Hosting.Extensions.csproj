<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.MessageBus.Hosting.Extensions</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.MessageBus.Hosting.Extensions</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="NATS.Client.Hosting" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Common\Kukui.Hosting.Common.csproj" />
    <ProjectReference Include="..\MessageBus.Abstractions\MessageBus.Abstractions.csproj" />
    <ProjectReference Include="..\..\..\outbox\src\Outbox.Server.Serialization\Outbox.Server.Serialization.csproj" />
  </ItemGroup>

</Project>