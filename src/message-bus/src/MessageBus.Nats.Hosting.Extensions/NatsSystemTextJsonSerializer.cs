namespace Kukui.Infrastructure.MessageBus.Hosting.Extensions;

using System.Buffers;
using System.Text.Json;
using NATS.Client.Core;
using NATS.Client.Serializers.Json;

public sealed class NatsSystemTextJsonSerializer<T> : NatsSerializer, INatsSerializer<T>
{
    private readonly JsonSerializerOptions _serializerOptions = GetOptions();

    public static INatsSerializer<T> Create() => new NatsSystemTextJsonSerializer<T>();

    public void Serialize(IBufferWriter<byte> bufferWriter, T value)
    {
        Utf8JsonWriter writer;
        if (JsonWriter == null)
        {
            JsonWriter = new Utf8JsonWriter(bufferWriter, JsonWriterOpts);
            writer = JsonWriter;
        }
        else
        {
            writer = JsonWriter;
            writer.Reset(bufferWriter);
        }

        JsonSerializer.Serialize(writer, value, _serializerOptions);
        writer.Reset(NullBufferWriter.Instance);
    }

    public T? Deserialize(in ReadOnlySequence<byte> buffer)
    {
        if (buffer.IsEmpty) return default;

        Utf8JsonReader reader = new(buffer); // Utf8JsonReader is ref struct, no allocate.

        return JsonSerializer.Deserialize<T>(ref reader, _serializerOptions);
    }

    private sealed class NullBufferWriter : IBufferWriter<byte>
    {
        internal static readonly IBufferWriter<byte> Instance = new NullBufferWriter();

        public void Advance(int count)
        {
        }

        public Memory<byte> GetMemory(int sizeHint = 0) => Array.Empty<byte>();

        public Span<byte> GetSpan(int sizeHint = 0) => Array.Empty<byte>();
    }

    public INatsSerializer<T> CombineWith(INatsSerializer<T> next) => new NatsJsonSerializer<T>();
}