namespace Kukui.Infrastructure.MessageBus.Hosting.Extensions;

using Microsoft.Extensions.Configuration;

public class NatsConnectionOptions
{
    public const string MessageServiceSectionName = "MessageService";

    public NatsConnectionOptions(IConfigurationSection serverSection)
    {
        serverSection.Bind(MessageServiceSectionName, this);
    }

    /// <summary>
    ///     Sets either single or multiple urls separated by semicolon(cluster) scenario./>
    /// </summary>
    public string Url { get; set; }

    public string ConsumerPrefix { get; set; }

    public string UserName { get; set; }

    public string Password { get; set; }

    public int BatchSize { get; set; } = 100;

    public int NumberOfNodes => Url.Split([","], StringSplitOptions.RemoveEmptyEntries).Length;

    public bool IsCluster => NumberOfNodes > 1;
}