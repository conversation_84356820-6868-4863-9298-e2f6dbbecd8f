namespace Kukui.Infrastructure.MessageBus.Hosting.Extensions;

using Microsoft.Extensions.DependencyInjection;
using NATS.Client.Core;

public interface INatsSerializerFactory
{
    INatsSerializer<T> GetSerializer<T>();
}

public class NatsSerializerFactory : INatsSerializerFactory
{
    private readonly IServiceProvider _serviceProvider;

    public NatsSerializerFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public INatsSerializer<T> GetSerializer<T>() => _serviceProvider.GetRequiredService<INatsSerializer<T>>();
}