namespace Kukui.Infrastructure.MessageBus.Hosting.Extensions;

using System.Text.Json;
using System.Text.Json.Serialization;
using OneOf.Serialization.SystemTextJson;
using Outbox.Server.Serialization;

public abstract class NatsSerializer
{
    [ThreadStatic] protected static Utf8JsonWriter? JsonWriter;

    protected static readonly JsonWriterOptions JsonWriterOpts = new() { Indented = false, SkipValidation = true };

    public static JsonSerializerOptions GetOptions()
    {
        return new JsonSerializerOptions
        {
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters =
            {
                new OneOfJsonConverter(),
                new OneOfBaseJsonConverter(),
                new OutboxJsonConverter(),
                new OutboxJsonResultConverter()
            }
        };
    }
}