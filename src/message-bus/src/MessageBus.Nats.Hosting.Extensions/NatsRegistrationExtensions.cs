namespace Kukui.Infrastructure.MessageBus.Hosting.Extensions;

using Abstractions.Extensions;
using Infrastructure.Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NATS.Client.Core;
using NATS.Client.Hosting;

public static class NatsRegistrationExtensions
{
    public static IServiceCollection TryAddNatsConnection(
        this IServiceCollection services,
        ServerConfiguration serverConfig)
    {
        NatsConnectionOptions connectionOptions = new(serverConfig.ServerConfigSection);

        //TODO: Research another authentication options: creds file, jwt, nkey, seed, nkey file
        services.TryAddSingleton(connectionOptions);
        return services.AddNats(
                connectionOptions.NumberOfNodes,
                options => options with
                {
                    Url = connectionOptions.Url,
                    Name = serverConfig.DisplayName.UseKebabCaseFormatting(),
                    AuthOpts = NatsAuthOpts.Default with
                    {
                        Username = connectionOptions.UserName, Password = connectionOptions.Password
                    },
                    RequestTimeout = TimeSpan.FromSeconds(30)
                })
            .AddSingleton<INatsSerializerFactory, NatsSerializerFactory>()
            .Add<PERSON><PERSON>leton(typeof(INatsSerializer<>), typeof(NatsSystemTextJsonSerializer<>));
    }
}