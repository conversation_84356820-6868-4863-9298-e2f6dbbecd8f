namespace Kukui.Infrastructure.MessageBus.Nats.KeyValue;

using NATS.Client.KeyValueStore;

public interface IKeyValueStore
{
    IAsyncEnumerable<string> GetKeysAsync(string filter, CancellationToken cancellationToken);

    ValueTask<NatsKVEntry<T?>> GetEntryIfExistsAsync<T>(string key, CancellationToken cancellationToken);

    ValueTask<ulong> CreateAsync<T>(string key, T value, CancellationToken cancellationToken);

    ValueTask<ulong> PutAsync<T>(string key, T value, CancellationToken cancellationToken);

    ValueTask PurgeAsync(string key, CancellationToken cancellationToken = default);
}