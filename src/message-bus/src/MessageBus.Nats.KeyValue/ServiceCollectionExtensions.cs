namespace Kukui.Infrastructure.MessageBus.Nats.KeyValue;

using Infrastructure.Hosting.Common.Models;
using JetStream;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NATS.Client.JetStream;
using NATS.Client.KeyValueStore;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNatsKeyValueStorage(this IServiceCollection services, ServerConfiguration serverConfiguration, Action<KeyValueStorageOptions> options)
    {
        var keyValueStorageOptions = new KeyValueStorageOptions();
        options(keyValueStorageOptions);
        services.AddNatsJetStream(serverConfiguration).TryAddSingleton<INatsKVContext>(provider => new NatsKVContext(provider.GetRequiredService<INatsJSContext>()));

        foreach (var storeConfig in keyValueStorageOptions.StoresConfiguration)
        {
            services.TryAddKeyedSingleton(
                typeof(IKeyValueStore),
                storeConfig.Bucket,
                (provider, _) =>
                {
                    var ctx = provider.GetRequiredService<INatsKVContext>();
                    var store = ctx.CreateStoreAsync(storeConfig).AsTask().ConfigureAwait(false).GetAwaiter().GetResult();
                    return new NatsKeyValueStore(store);
                });
        }

        return services;
    }
}