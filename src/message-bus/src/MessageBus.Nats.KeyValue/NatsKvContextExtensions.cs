#pragma warning disable CS8073 // The result of the expression is always the same since a value of this type is never equal to 'null'

namespace Kukui.Infrastructure.MessageBus.Nats.KeyValue;

using NATS.Client.KeyValueStore;

public static class NatsKvContextExtensions
{
    public static async ValueTask<ulong> CreateEntryIfNotExistsAsync<T>(this INatsKVStore bucket, string key, T? value)
    {
        var entry = await bucket.GetEntryAsync<T>(key);

        if (entry == null)
        {
            return await bucket.CreateAsync(key, value);
        }

        return default;
    }
}