namespace Kukui.Infrastructure.MessageBus.Nats.KeyValue;

using System.Collections.Concurrent;
using Hosting.Extensions;
using NATS.Client.Core;
using NATS.Client.KeyValueStore;

public class NatsKeyValueStore : IKeyValueStore
{
    private readonly INatsKVStore _store;

    private readonly ConcurrentDictionary<Type, object> _serializers = [];


    public NatsKeyValueStore(INatsKVStore store)
    {
        _store = store;
    }

    public ValueTask<ulong> CreateAsync<T>(string key, T value, CancellationToken cancellationToken) =>
        _store.CreateAsync(key, value, GetSerializer<T>(), cancellationToken);

    public ValueTask<ulong> PutAsync<T>(string key, T value, CancellationToken cancellationToken) =>
        _store.PutAsync(key, value, GetSerializer<T>(), cancellationToken);

    public ValueTask PurgeAsync(string key, CancellationToken cancellationToken = default) =>
        _store.PurgeAsync(key, cancellationToken: cancellationToken);

    public IAsyncEnumerable<string> GetKeysAsync(string filter, CancellationToken cancellationToken) =>
        _store.GetKeysAsync([filter], cancellationToken: cancellationToken);

    public async ValueTask<NatsKVEntry<T?>> GetEntryIfExistsAsync<T>(string key, CancellationToken cancellationToken)
    {
        var natsSerializer = GetSerializer<T>();
        try
        {
            return await _store.GetEntryAsync(key, serializer: natsSerializer, cancellationToken: cancellationToken);
        }
        catch (NatsKVKeyNotFoundException)
        {
            await _store.CreateAsync(key, default, natsSerializer, cancellationToken);

            return new NatsKVEntry<T?> { Value = default! };
        }
        catch (NatsKVKeyDeletedException)
        {
            await _store.CreateAsync(key, default, natsSerializer, cancellationToken);

            return new NatsKVEntry<T?> { Value = default! };
        }
    }

    private INatsSerializer<T?> GetSerializer<T>()
    {
        return (INatsSerializer<T?>) _serializers.GetOrAdd(typeof(T), _ => new NatsSystemTextJsonSerializer<T>());
    }
}