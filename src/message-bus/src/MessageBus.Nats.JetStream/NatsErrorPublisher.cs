namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

using Abstractions;
using Abstractions.Publisher;
using NATS.Client.Core;
using NATS.Client.JetStream;

public class NatsErrorPublisher : IErrorPublisher
{
    private readonly INatsJSContext _jsContext;
    private readonly INatsSerializer<ErrorMessage> _serializer;

    public NatsErrorPublisher(INatsJSContext jsContext, INatsSerializer<ErrorMessage> serializer)
    {
        _jsContext = jsContext;
        _serializer = serializer;
    }

    public async Task PublishErrorAsync(ErrorMessage message, CancellationToken cancellationToken = default)
    {
        var response = await _jsContext.PublishAsync(
            message.Metadata.Subject,
            message,
            _serializer,
            cancellationToken: cancellationToken);
        response.EnsureSuccess();
    }
}