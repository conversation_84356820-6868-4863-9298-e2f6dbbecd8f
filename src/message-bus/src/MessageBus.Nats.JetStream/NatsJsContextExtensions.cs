// ReSharper disable All

namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

using Consumers;
using Microsoft.Extensions.Logging;
using NATS.Client.JetStream;
using NATS.Client.JetStream.Models;

public static class NatsJsContextExtensions
{
    public static async ValueTask<INatsJSStream?> GetStreamIfExistsAsync(
        this INatsJSContext natsJsContext,
        string streamName,
        StreamInfoRequest? infoRequest = default,
        CancellationToken cancellationToken = default)
    {
        try
        {
            return await natsJsContext.GetStreamAsync(streamName, infoRequest, cancellationToken);
        }
        catch (NatsJSApiException e) when (e.Error.Code == 404)
        {
            return default;
        }
    }

    public static async ValueTask<INatsJSConsumer> CreateConsumerIfNotExistsAsync(
        this INatsJSContext natsJsContext,
        string streamName,
        ConsumerOptionsBase consumerOptions,
        ILogger logger,
        CancellationToken cancellationToken = default)
    {
        INatsJSConsumer consumer;
        try
        {
            consumer = await natsJsContext.GetConsumerAsync(streamName, consumerOptions.DurableName, cancellationToken);
            logger.LogInformation("Consumer {ConsumerName} already exists", consumer.Info.Name);
        }
        catch (NatsJSApiException e) when (e.Error.Code == 404)
        {
            consumer = await natsJsContext.CreateConsumerAsync(streamName, consumerOptions, cancellationToken);

            logger.LogInformation(
                "Consumer {ConsumerName} has been added to {StreamName}",
                consumer.Info.Name,
                streamName);
        }

        return consumer;
    }

    public static async ValueTask<INatsJSStream> CreateStreamIfNotExistsAsync(
        this INatsJSContext jsContext,
        StreamConfig streamConfig,
        ILogger logger,
        CancellationToken cancellationToken = default)
    {
        var stream = await jsContext.GetStreamIfExistsAsync(streamConfig.Name!, cancellationToken: cancellationToken);
        if (stream == null)
        {
            stream = await jsContext.CreateStreamAsync(streamConfig, cancellationToken);
            logger.LogInformation("Stream {StreamName} was created", stream.Info.Config.Name);
            return stream;
        }

        logger.LogInformation("Stream {StreamName} already exists", stream.Info.Config.Name);

        return stream;
    }
}