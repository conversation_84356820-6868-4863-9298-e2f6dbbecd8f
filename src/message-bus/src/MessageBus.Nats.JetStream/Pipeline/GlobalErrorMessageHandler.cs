namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Pipeline;

using Abstractions;
using Abstractions.Consumers;
using Abstractions.Events;
using Abstractions.Pipeline;
using Abstractions.Publisher;
using Microsoft.Extensions.Logging;
using NATS.Client.JetStream.Models;

internal class GlobalErrorMessageHandler<TContext, TEvent> : IMessageExceptionProcessor<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class
{
    private readonly IErrorPublisher _errorPublisher;
    private readonly ILogger _logger;

    public GlobalErrorMessageHandler(
        IErrorPublisher errorPublisher,
        ILogger<GlobalErrorMessageHandler<TContext, TEvent>> logger)
    {
        _errorPublisher = errorPublisher;
        _logger = logger;
    }

    public async Task ExecuteAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug(
                "Start executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
                nameof(GlobalErrorMessageHandler<TContext, TEvent>),
                context.EventType.Name,
                context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id);

            await next(cancellationToken);

            _logger.LogDebug(
                "Finish executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
                nameof(GlobalErrorMessageHandler<TContext, TEvent>),
                context.EventType.Name,
                context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id);
        }
        catch (Exception e)
        {
            if (context is ConsumeContext<Batch<TEvent>> batchContext)
            {
                _logger.LogError(
                    e,
                    "{ConsumerName} failed to process {BatchSize} {EventType} with batch Id {BatchId}",
                    context.Consumer.Name,
                    batchContext.Message.Count,
                    typeof(TEvent).Name,
                    batchContext.Message.Id);

                await PublishBatchErrorMessageAsync(
                    batchContext.Consumer,
                    batchContext.Message.Id,
                    (IEnumerable<IEvent>) batchContext.Message,
                    e,
                    cancellationToken);
            }
            else
            {
                _logger.LogError(
                    e,
                    "{ConsumerName} failed to process {EventType} message with Id {MessageId}",
                    context.Consumer.Name,
                    typeof(TEvent).Name,
                    ((IEvent) context.Message).Id);

                await PublishErrorMessageAsync(context.Consumer, e, cancellationToken, (IEvent) context.Message);
            }
        }
    }

    private async Task PublishErrorMessageAsync(
        ConsumerInfo consumer,
        Exception e,
        CancellationToken cancellationToken,
        IEvent message)
    {
        await _errorPublisher.PublishErrorAsync(ErrorMessage.Create(consumer, message, e), cancellationToken);
    }

    private async Task PublishBatchErrorMessageAsync(
        ConsumerInfo consumer,
        Guid batchId,
        IEnumerable<IEvent> messages,
        Exception? e,
        CancellationToken cancellationToken = default)
    {
        foreach (var message in messages)
        {
            await _errorPublisher.PublishErrorAsync(
                ErrorMessage.CreateWithBath(consumer, message, batchId, e),
                cancellationToken);
        }
    }
}