namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Pipeline;

using Abstractions.Consumers;
using Abstractions.Events;
using Abstractions.Pipeline;
using Microsoft.Extensions.Logging;
using NATS.Client.JetStream;
using static Abstractions.MessageConstants;

public class MessageScheduler<TEvent> : IMessageScheduler<TEvent>
    where TEvent : class, IEvent
{
    private readonly ILogger<MessageScheduler<TEvent>> _logger;

    public MessageScheduler(ILogger<MessageScheduler<TEvent>> logger)
    {
        _logger = logger;
    }

    public async Task<bool> ScheduleAsync(ConsumeContext<TEvent> context, CancellationToken cancellationToken)
    {
        if (context.Headers == null || !context.Headers.TryGetValue(ScheduleTime, out var schedule)) return false;

        var scheduleTime = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(schedule.ToString()));

        if (scheduleTime <= DateTimeOffset.UtcNow)
        {
            return false;
        }

        var ackOpts = new AckOpts { DoubleAck = true };

        await context.MessagePayload.NakAsync(ackOpts, scheduleTime - DateTimeOffset.UtcNow, cancellationToken);

        _logger.LogInformation(
            "Message {EventType} with Id {MessageId} scheduled for {ScheduleTime}",
            typeof(TEvent).Name,
            context.Message.Id,
            scheduleTime);

        return true;
    }
}