namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Pipeline;

using System.Diagnostics.CodeAnalysis;
using Abstractions.Consumers;
using Abstractions.Events;
using Abstractions.Pipeline;
using Microsoft.Extensions.Logging;

[SuppressMessage("ReSharper", "StaticMemberInGenericType")]
internal class SimpleRetryMessagePolicy<TContext, TEvent> : IMessageRetryPolicy<TContext, TEvent>
    where TContext : ConsumeContext
{
    private const int RetryCount = 3;
    private static readonly TimeSpan BackOff = TimeSpan.FromSeconds(5);
    private static readonly TimeSpan BatchBackOff = TimeSpan.FromSeconds(10);
    private readonly ILogger<SimpleRetryMessagePolicy<TContext, TEvent>> _logger;

    public SimpleRetryMessagePolicy(ILogger<SimpleRetryMessagePolicy<TContext, TEvent>> logger)
    {
        _logger = logger;
    }

    public async Task ExecuteAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        var backOff = context.IsBatchContext ? BatchBackOff : BackOff;
        var retriedCount = 0;
        do
        {
            try
            {
                _logger.LogDebug(
                    "Start executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
                    nameof(SimpleRetryMessagePolicy<TContext, TEvent>),
                    context.EventType.Name,
                    context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id);

                await next(cancellationToken);

                _logger.LogDebug(
                    "Finish executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
                    nameof(SimpleRetryMessagePolicy<TContext, TEvent>),
                    context.EventType.Name,
                    context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id);

                return;
            }
            catch (Exception e)
            {
                _logger.LogDebug(
                    e,
                    "Retry {RetryCount} with error during {MessagePipelineStep} for {EventType} with Id {MessageId}",
                    nameof(SimpleRetryMessagePolicy<TContext, TEvent>),
                    retriedCount + 1,
                    context.EventType.Name,
                    context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id);

                await context.MessagePayload.AckProgressAsync(cancellationToken: cancellationToken);

                if (retriedCount++ == RetryCount)
                {
                    throw;
                }

                var errorMessage = $"{e.Message}.{e.InnerException?.Message}";
                _logger.LogWarning(
                    "Retrying {RetryCount} after {BackOff} seconds processing {EventType} message with Id {MessageId}.{ErrorMessage}",
                    retriedCount,
                    retriedCount * backOff.TotalSeconds,
                    context.EventType.Name,
                    context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id,
                    errorMessage);
                await Task.Delay((int) (retriedCount * backOff.TotalMilliseconds), cancellationToken);
            }
        } while (retriedCount <= RetryCount);
    }
}