namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Pipeline;

using Abstractions.Consumers;
using Abstractions.Events;
using Abstractions.Pipeline;
using Microsoft.Extensions.Logging;
using NATS.Client.JetStream;

internal class MessageAcknowledgeHandler<TContext, TEvent> : IMessageAcknowledgeHandler<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class, IEvent
{
    private readonly ILogger _logger;
    private readonly AckOpts _ackOpts = new() { DoubleAck = true };

    public MessageAcknowledgeHandler(ILogger<MessageAcknowledgeHandler<TContext, TEvent>> logger)
    {
        _logger = logger;
    }

    public async Task ExecuteAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        await next(cancellationToken);
        await context.MessagePayload.AckAsync(_ackOpts, cancellationToken);

        if (context.IsBatchContext)
        {
            _logger.LogDebug(
                "Batch with Id {BatchId} processed {BatchSize} {EventType} messages successfully",
                ((Batch<TEvent>) context.Message).Id,
                ((Batch<TEvent>) context.Message).Count,
                context.EventType.Name);
        }
        else
        {
            _logger.LogDebug(
                "Message {EventType} with Id {MessageId} processed successfully",
                context.EventType.Name,
                ((IEvent) context.Message).Id);
        }
    }
}