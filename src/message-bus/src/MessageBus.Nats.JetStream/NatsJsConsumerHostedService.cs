namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NATS.Client.Core;

public class NatsJsConsumerHostedService : IHostedService
{
    private readonly NatsJetStreamRegistrator _natsJetStreamRegistrator;
    private readonly INatsConnection _natsConnection;
    private readonly ILogger<NatsJsConsumerHostedService> _logger;

    public NatsJsConsumerHostedService(
        NatsJetStreamRegistrator natsJetStreamRegistrator,
        INatsConnection natsConnection,
        ILogger<NatsJsConsumerHostedService> logger)
    {
        _natsJetStreamRegistrator = natsJetStreamRegistrator;
        _natsConnection = natsConnection;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting NATS JetStream consumer registration service");
        try
        {
            await _natsConnection.ConnectAsync();
            await _natsJetStreamRegistrator.RegisterAsync(cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to start NATS JetStream consumer registration service");
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}