namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

using System.Text.Json;
using Abstractions;
using Abstractions.Consumers;
using Abstractions.Events;
using Abstractions.Streams;
using Consumers;
using Handlers;
using Hosting.Extensions;
using Infrastructure.Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NATS.Client.Core;
using NATS.Client.JetStream;
using Outbox.Server.Serialization;

public class NatsJetStreamRegistrator
{
    private readonly ILogger<NatsJetStreamRegistrator> _logger;
    private readonly IServiceProvider _serviceProvider;


    public NatsJetStreamRegistrator(IServiceProvider serviceProvider, ILogger<NatsJetStreamRegistrator> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task RegisterAsync(CancellationToken cancellationToken = default)
    {
        var consumerRegistrationOptions = _serviceProvider.GetService<ConsumersRegistrationOptions>();
        var serverConfiguration = _serviceProvider.GetRequiredService<ServerConfiguration>();
        var natsConnectionOptions = _serviceProvider.GetRequiredService<NatsConnectionOptions>();
        if (consumerRegistrationOptions == null) return;

        var jsContext = _serviceProvider.GetRequiredService<INatsJSContext>();
        var streamReplicaCount = !serverConfiguration.Environment.IsProduction()
            ? (int?) natsConnectionOptions.NumberOfNodes
            : null;

        await CreateStreamsAsync(jsContext, streamReplicaCount, consumerRegistrationOptions, cancellationToken);
        await CreateConsumersAsync(jsContext, consumerRegistrationOptions, cancellationToken);
        await CreateConsumerGroupsAsync(jsContext, consumerRegistrationOptions, cancellationToken);
    }

    private async Task CreateStreamsAsync(
        INatsJSContext jsContext,
        int? replicaCount,
        ConsumersRegistrationOptions registrationOptions,
        CancellationToken cancellationToken)
    {
        var streams = registrationOptions.AllConsumerRegistrations
            .SelectMany(x => x.EventTypes.Select(e => e.Value.Stream))
            .Distinct();
        foreach (var stream in streams)
        {
            if (stream == null) throw new InvalidOperationException("Stream cannot be null");

            stream.Replicas = replicaCount ?? stream.Replicas;

            await jsContext.CreateStreamIfNotExistsAsync(stream, _logger, cancellationToken);
            var errorQueue = (NatsStreamBase) Activator.CreateInstance(
                typeof(DeadLetterQueue<>).MakeGenericType(stream.GetType()))!;
            errorQueue.Replicas = stream.Replicas;
            await jsContext.CreateStreamIfNotExistsAsync(errorQueue, _logger, cancellationToken);
        }
    }

    private async Task CreateConsumersAsync(
        INatsJSContext jsContext,
        ConsumersRegistrationOptions registrationOptions,
        CancellationToken cancellationToken)
    {
        foreach (var registration in registrationOptions.GetSingleConsumerRegistrations())
        {
            foreach (var streamEvents in registration.EventTypes.Values.GroupBy(x => x.Stream.Name))
            {
                var consumerOptions = registration.GetConsumerOptions(streamEvents);
                try
                {
                    var consumer = await jsContext.CreateConsumerIfNotExistsAsync(
                        streamEvents.Key,
                        consumerOptions,
                        _logger,
                        cancellationToken);
                    RegisterConsumerEventsHandler(consumer, streamEvents.ToDictionary(x => x.Value), cancellationToken);

                    _logger.LogInformation(
                        "{ConsumerName} is registered for {EventTypes} event with and start requesting messages from server",
                        consumer.Info.Name,
                        streamEvents.Select(x => x.Value.Name));
                }
                catch (Exception)
                {
                    _logger.LogError("Failed to create {ConsumerName}", consumerOptions.DurableName);
                    throw;
                }
            }
        }
    }

    private async Task CreateConsumerGroupsAsync(
        INatsJSContext jsContext,
        ConsumersRegistrationOptions consumerRegistrationOptions,
        CancellationToken cancellationToken)
    {
        foreach (var groupRegistration in consumerRegistrationOptions.GroupRegistrations)
        {
            try
            {
                var targetEvents = groupRegistration.Value.ConsumerRegistrations.SelectMany(x => x.EventTypes.Values)
                    .ToList();
                var streamName = targetEvents.Select(x => x.Stream.Name).Distinct().Single();
                var consumer = await jsContext.CreateConsumerIfNotExistsAsync(
                    streamName,
                    groupRegistration.Value.GetConsumerConfiguration(targetEvents),
                    _logger,
                    cancellationToken);

                RegisterConsumerEventsHandler(consumer, targetEvents.ToDictionary(x => x.Value), cancellationToken);

                _logger.LogInformation(
                    "{ConsumerName} is registered for {EventTypes} event with and start requesting messages from server",
                    consumer.Info.Name,
                    targetEvents.Select(x => x.Value.Name));
            }
            catch (Exception)
            {
                _logger.LogError("Failed to create {ConsumerName}", groupRegistration.Value.ConsumerGroupName);
                throw;
            }
        }
    }


    private void RegisterConsumerEventsHandler(
        INatsJSConsumer consumer,
        Dictionary<Type, EventType> eventTypes,
        CancellationToken cancellationToken)
    {
#pragma warning disable VSTHRD110
        Task.Run(
            async () =>
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        await consumer.RefreshAsync(cancellationToken);
                        await HandleProcessingAsync(consumer, eventTypes, cancellationToken);
                    }
                    catch
                    {
                        await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
                    }
                }
            },
            cancellationToken);
#pragma warning restore VSTHRD110
    }

    // ReSharper disable once TooManyArguments
    private async Task HandleProcessingAsync(
        INatsJSConsumer consumer,
        Dictionary<Type, EventType> eventTypes,
        CancellationToken cancellationToken)
    {
        if (consumer.Info.Config.MaxBatch > 1)
        {
            await HandleMessageBatchAsync(consumer, eventTypes, cancellationToken);
        }
        else
        {
            await HandleMessageAsync(consumer, cancellationToken);
        }
    }

    private async Task HandleMessageAsync(INatsJSConsumer consumer, CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            var natsMsg = await consumer.NextAsync<NatsMemoryOwner<byte>>(cancellationToken: cancellationToken);
            if (natsMsg?.Error != null)
            {
                _logger.LogError(
                    natsMsg.Value.Error,
                    "Failed to read message for consumer {ConsumerName} with {MessageSubject} and headers {MessageHeaders}",
                    consumer.Info.Name,
                    natsMsg.Value.Subject,
                    natsMsg.Value.Headers);
                continue;
            }

            if (natsMsg?.Data == null || natsMsg.Value.Data.Length == 0)
            {
                continue;
            }

            try
            {
                var eventMessage = DeserializeEvent(consumer.Info.Name, natsMsg.Value.Data.Memory.Span);

                if (eventMessage == null) continue;

                var internalHandlerType = typeof(ConsumerHandlerInternal<>).MakeGenericType(eventMessage.GetType());

                var handler = (ConsumerHandlerInternalBase) Activator.CreateInstance(
                    internalHandlerType,
                    null,
                    _serviceProvider)!;
                var consumeContext = new ConsumeContext(
                    eventMessage,
                    natsMsg.Value.Subject,
                    consumer.Info,
                    natsMsg.Value.Headers,
                    natsMsg.Value.Metadata)
                {
                    MessagePayload = natsMsg.Value
                };
                await handler.HandleInternalAsync(consumeContext, cancellationToken);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "{ConsumerName} failed when requesting message from server", consumer.Info.Name);
            }
        }
    }

    private async Task HandleMessageBatchAsync(
        INatsJSConsumer consumer,
        Dictionary<Type, EventType> eventTypes,
        CancellationToken cancellationToken)
    {
        var options = new NatsJSFetchOpts
        {
            MaxMsgs = consumer.Info.Config.MaxBatch,
            Expires = TimeSpan.FromSeconds(10)
        };

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var consumerBatch = new Batch<ConsumeContext>(options.MaxMsgs.Value);

                var consumeEnumerator = consumer
                    .FetchAsync<NatsMemoryOwner<byte>>(options, cancellationToken: cancellationToken)
                    .GetAsyncEnumerator(cancellationToken);

                while (await consumeEnumerator.MoveNextAsync())
                {
                    if (consumeEnumerator.Current.Data.Length == 0) continue;

                    var eventMessage = DeserializeEvent(consumer.Info.Name, consumeEnumerator.Current.Data.Memory.Span);
                    if (eventMessage == null) continue;

                    var consumeContext = new ConsumeContext(
                        eventMessage,
                        consumeEnumerator.Current.Subject,
                        consumer.Info,
                        consumeEnumerator.Current.Headers,
                        consumeEnumerator.Current.Metadata)
                    {
                        MessagePayload = consumeEnumerator.Current
                    };

                    consumerBatch.Messages.Add(consumeContext);
                }

                await consumeEnumerator.DisposeAsync();

                foreach (var eventBatch in consumerBatch.Messages.GroupBy(x => x.Message.GetType()))
                {
                    if (!eventTypes.TryGetValue(eventBatch.Key, out var eventType))
                    {
                        throw new InvalidOperationException(
                            $"Event type {eventBatch.Key.Name} is not registered for consumer {consumer.Info.Name}");
                    }

                    var internalHandlerType = typeof(ConsumerHandlerInternal<>).MakeGenericType(eventBatch.Key);
                    var handler = (ConsumerHandlerInternalBase) Activator.CreateInstance(
                        internalHandlerType,
                        eventType,
                        _serviceProvider)!;
                    await handler.HandleBatchInternalAsync(eventBatch.ToList(), cancellationToken);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "{ConsumerName} failed when requesting batch from server", consumer.Info.Name);
            }
        }
    }

    private IEvent? DeserializeEvent(string consumerName, Span<byte> data)
    {
        var serializationResult =
            JsonSerializer.Deserialize<SerializationResult<IEvent>>(data, SystemTextOutboxSerializer.Options) ??
            throw new InvalidOperationException("Failed to deserialize event");

        if (serializationResult.Value == null)
        {
            _logger.LogError(
                "Failed to deserialize event for {ConsumerName} with payload {MessagePayload}",
                consumerName,
                serializationResult.JsonValue);
        }

        return serializationResult.Value;
    }
}