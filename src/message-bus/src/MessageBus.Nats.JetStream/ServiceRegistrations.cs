namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

using Abstractions;
using Abstractions.Consumers;
using Abstractions.Pipeline;
using Abstractions.Publisher;
using Consumers;
using Hosting.Extensions;
using Infrastructure.Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using NATS.Client.Core;
using NATS.Client.JetStream;
using Pipeline;

public static class ServiceRegistrations
{
    public static IServiceCollection AddNatsJetStream(this IServiceCollection services, ServerConfiguration serverConfig, Action<ConsumersRegistrationOptions>? configure = null)
    {
        var connectionOptions = new NatsConnectionOptions(serverConfig.ServerConfigSection);
        ConsumersRegistrationOptions consumersRegistrationOptions = new(connectionOptions);
        configure?.Invoke(consumersRegistrationOptions);

        services.TryAddNatsConnection(serverConfig).TryAddSingleton<INatsJSContext>(sp => new NatsJSContext(sp.GetRequiredService<INatsConnection>()));
        services.TryAddSingleton<IEventTopologyProvider, EventTopologyProvider>();
        services.TryAddSingleton<NatsJetStreamRegistrator>();
        services.TryAddSingleton(typeof(IPublisher<>), typeof(NatsJsPublisher<>));
        services.TryAddSingleton<IErrorPublisher, NatsErrorPublisher>();
        services.TryAddSingleton<IPublisherFactory, NatsJsPublisherFactory>();
        services.TryAddConsumerRegistrations(consumersRegistrationOptions);

        return services.RegisterPipelineServices(consumersRegistrationOptions);
    }

    private static void TryAddConsumerRegistrations(this IServiceCollection services, ConsumersRegistrationOptions consumerRegistrationOptions)
    {
        var consumerRegistrations = consumerRegistrationOptions.AllConsumerRegistrations.ToArray();
        if (!consumerRegistrations.Any()) return;

        services.AddSingleton(consumerRegistrationOptions);
        services.AddConsumerHandlers(consumerRegistrations);
        services.AddHostedService<NatsJsConsumerHostedService>();
    }

    private static IServiceCollection RegisterPipelineServices(this IServiceCollection services, ConsumersRegistrationOptions consumersRegistration)
    {
        services.AddScoped(typeof(IPipeline<,>), typeof(MessageSchedulerPipeline<,>))
            .AddScoped(typeof(IPipeline<,>), typeof(MessageAcknowledgePipeline<,>))
            .AddScoped(typeof(IPipeline<,>), typeof(MessageExceptionPipeline<,>))
            .AddScoped(typeof(IPipeline<,>), typeof(MessageRetryPipeline<,>))
            .AddScoped(typeof(IPipeline<,>), typeof(MessagePreProcessorPipeline<,>))
            .AddScoped(typeof(IPipeline<,>), typeof(MessageFilterPipeline<,>))
            .AddScoped(typeof(IPipeline<,>), typeof(MessagePostProcessorPipeline<,>));

        services.AddMessageProcessingPipeline(
            consumersRegistration,
            options => options.AddMessageAcknowledgeHandler(typeof(MessageAcknowledgeHandler<,>))
                .AddMessageExceptionHandler(typeof(GlobalErrorMessageHandler<,>))
                .AddMessageScheduler(typeof(MessageScheduler<>))
                .AddRetryProcessor(typeof(SimpleRetryMessagePolicy<,>)));

        return services;
    }

    private static void AddMessageProcessingPipeline(this IServiceCollection services, ConsumersRegistrationOptions consumersRegistration, Action<PipelineConfigurationOptions>? configure = null)

    {
        var pipelineOptions = new PipelineConfigurationOptions(services);
        configure?.Invoke(pipelineOptions);

        foreach (var messageFilter in consumersRegistration.MessageFilters)
        {
            services.TryAdd(messageFilter);
        }
    }

    private static IServiceCollection AddConsumerHandlers(this IServiceCollection services, params ConsumerRegistration[] registrations)
    {
        foreach (var registration in registrations)
        {
            foreach (var consumerEvent in registration.EventTypes.Values)
            {
                var serviceType = consumerEvent.IsBatch ? typeof(IEventConsumer<>).MakeGenericType(typeof(Batch<>).MakeGenericType(consumerEvent.Value)) : typeof(IEventConsumer<>).MakeGenericType(consumerEvent.Value);

                services.AddKeyedScoped(serviceType, registration.ConsumerName, registration.ConsumerType);
            }
        }

        return services;
    }
}