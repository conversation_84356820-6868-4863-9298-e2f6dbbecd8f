namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

using System.Collections.Concurrent;
using Abstractions;
using Abstractions.Events;
using Abstractions.Extensions;
using Abstractions.Streams;

public class EventTopologyProvider : IEventTopologyProvider
{
    private readonly ConcurrentDictionary<Type, EventTopology> _eventTopologyCache = new();

    public EventTopology GetTopology<TEvent>()
        where TEvent : IEvent
    {
        return GetTopology(typeof(TEvent));
    }

    public EventTopology GetTopology(Type eventType)
    {
        return _eventTopologyCache.GetOrAdd(eventType, CreateTopology);
    }

    private EventTopology CreateTopology(Type eventType)
    {
        var natsStream =
            (NatsStreamBase) Activator.CreateInstance(
                eventType.GetGenericInterfacesTypeArguments(typeof(IEvent<>))[0])!;
        var eventName = eventType.Name
            .Replace("Event", string.Empty)
            .UseKebabCaseFormatting();

        var eventTopology = new EventTopology
        {
            StreamName = natsStream.Name,
            SubjectRoot = eventName[..eventName.LastIndexOf('-')],
            Operation = eventName[(eventName.LastIndexOf('-') + 1)..]
        };
        eventTopology.SubjectTemplate =
            $"{natsStream.Name}.{eventTopology.SubjectRoot}.{{0}}.{eventTopology.Operation}";

        return eventTopology;
    }
}