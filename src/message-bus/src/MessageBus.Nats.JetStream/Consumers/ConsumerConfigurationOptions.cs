namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using Abstractions.Policies;

public class ConsumerConfigurationOptions
{
    internal DeliveryPolicy DeliveryPolicy { get; private set; } = new DeliverAllMessagesPolicy();

    public ConsumerConfigurationOptions WithDeliveryByStartTime(DateTimeOffset startTme)
    {
        DeliveryPolicy = new DeliveryByStartTime(startTme);

        return this;
    }

    public ConsumerConfigurationOptions WithDeliveryByStartSequenceNumber(ulong streamSequenceNumber)
    {
        DeliveryPolicy = new DeliveryByStartSequence(streamSequenceNumber);

        return this;
    }

    public ConsumerConfigurationOptions WithDeliveryByLastMessagePerSubject()
    {
        DeliveryPolicy = new DeliveryByLastMsgPerSubject();

        return this;
    }

    public ConsumerConfigurationOptions WithDeliveryByNewMessages()
    {
        DeliveryPolicy = new DeliverNewMessagesPolicy();

        return this;
    }
}