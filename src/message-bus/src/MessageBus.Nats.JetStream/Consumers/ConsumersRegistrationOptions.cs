namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using Abstractions.Consumers;
using Abstractions.Extensions;
using Abstractions.Pipeline;
using Hosting.Extensions;
using Microsoft.Extensions.DependencyInjection;

[SuppressMessage("ReSharper", "UnusedMethodReturnValue.Global")]
[SuppressMessage("ReSharper", "UnusedMember.Global")]
public class ConsumersRegistrationOptions
{
    private readonly NatsConnectionOptions _connectionOptions;
    private readonly Dictionary<Type, ConsumerRegistration> _registrations = new();

    public ConsumersRegistrationOptions(NatsConnectionOptions connectionOptions)
    {
        _connectionOptions = connectionOptions;
    }

    internal IReadOnlyCollection<ConsumerRegistration> Registrations => _registrations.Values.ToList();

    internal Dictionary<string, ConsumerGroupRegistration> GroupRegistrations { get; } = [];

    internal IReadOnlyCollection<ConsumerRegistration> AllConsumerRegistrations =>
        GroupRegistrations.Values.SelectMany(x => x.ConsumerRegistrations).Union(Registrations).ToList();

    internal List<ServiceDescriptor> MessageFilters { get; } = [];

    public ConsumersRegistrationOptions RegisterConsumersFromAssembly(
        Assembly assembly,
        Action<ConsumerConfigurationOptions>? configure = null)
    {
        RegisterConsumersFromAssemblies(configure, assembly);
        return this;
    }

    public ConsumersRegistrationOptions RegisterConsumersFromAssemblies(
        Action<ConsumerConfigurationOptions>? configure = null,
        params Assembly[] assemblies)
    {
        foreach (var consumerType in assemblies.SelectMany(x => x.GetTypes())
                     .Where(
                         type => type is { IsClass: true, IsAbstract: false } &&
                                 typeof(IEventConsumer).IsAssignableFrom(type)))
        {
            AddConsumer(consumerType, configure);
        }

        return this;
    }

    public ConsumersRegistrationOptions RegisterConsumersFromAssemblyContaining<T>(
        Action<ConsumerConfigurationOptions>? configure = null)
        where T : class
    {
        RegisterConsumersFromAssemblyContaining(typeof(T), configure);
        return this;
    }

    public ConsumersRegistrationOptions RegisterConsumersFromNamespaceContaining<T>(
        Action<ConsumerConfigurationOptions>? configure = null)
        where T : class
    {
        RegisterConsumersFromNamespaceContaining(typeof(T), configure);
        return this;
    }

    public ConsumersRegistrationOptions RegisterConsumersFromNamespaceContaining(
        Type type,
        Action<ConsumerConfigurationOptions>? configure = null)
    {
        type.Assembly.GetTypes()
            .Where(
                t => t.Namespace == type.Namespace && t is { IsClass: true, IsAbstract: false } &&
                     typeof(IEventConsumer).IsAssignableFrom(t))
            .ToList()
            .ForEach(consumerType => AddConsumer(consumerType, configure));

        return this;
    }

    public ConsumersRegistrationOptions RegisterConsumersFromAssemblyContaining(
        Type type,
        Action<ConsumerConfigurationOptions>? configure = null)
    {
        RegisterConsumersFromAssembly(type.Assembly, configure);
        return this;
    }

    public ConsumersRegistrationOptions AddConsumer<TConsumer>(
        Action<ConsumerConfigurationOptions>? configureConsumer = null)
        where TConsumer : IEventConsumer
    {
        AddConsumer(typeof(TConsumer), configureConsumer);

        return this;
    }

    public ConsumersRegistrationOptions AddConsumer(
        Type type,
        Action<ConsumerConfigurationOptions>? configureConsumer = null)
    {
        AddConsumerInternal(type, configureConsumer);

        return this;
    }

    public ConsumersRegistrationOptions AddBatchConsumer<TConsumer>(Action<BulkFetchOptions> bulkOptions)
        where TConsumer : IEventConsumer
    {
        AddBatchConsumerInternal(typeof(TConsumer), bulkOptions);

        return this;
    }

    public ConsumersRegistrationOptions AddConsumerGroup(
        string name,
        Action<ConsumerGroupRegistrationOptions> configure)
    {
        var options = new ConsumerGroupRegistrationOptions();
        configure(options);

        GroupRegistrations[name] = new ConsumerGroupRegistration(name, _connectionOptions, options.Consumers)
        {
            ConsumerConfiguration = options.ConsumerConfiguration
        };

        return this;
    }

    public IEnumerable<ConsumerRegistration> GetSingleConsumerRegistrations() =>
        Registrations.Except(GroupRegistrations.Values.SelectMany(x => x.ConsumerRegistrations));

    public ConsumersRegistrationOptions AddMessageFilter<TFilter>()
        where TFilter : class
    {
        AddMessageFilter(typeof(TFilter));

        return this;
    }

    public ConsumersRegistrationOptions AddMessageFilter(Type filterType)
    {
        var messageFilterTypes = filterType.GetGenericInterfacesTypeArguments(typeof(IMessageFilter<>));

        if (messageFilterTypes.Length == 0)
        {
            throw new InvalidOperationException(
                $"Message filter {filterType.Name} must implement IMessageFilter<T> interface.");
        }

        if (filterType.IsGenericType)
        {
            MessageFilters.Add(new ServiceDescriptor(typeof(IMessageFilter<>), filterType, ServiceLifetime.Transient));

            return this;
        }

        foreach (var messageFilterType in messageFilterTypes)
        {
            MessageFilters.Add(
                new ServiceDescriptor(
                    typeof(IMessageFilter<>).MakeGenericType(messageFilterType),
                    filterType,
                    ServiceLifetime.Transient));
        }

        return this;
    }

    private void AddConsumerInternal(Type type, Action<ConsumerConfigurationOptions>? consumerOptions = null)
    {
        if (_connectionOptions.ConsumerPrefix == null)
        {
            throw new InvalidOperationException(
                $"{nameof(_connectionOptions.ConsumerPrefix)} must be set in \"{NatsConnectionOptions.MessageServiceSectionName}\" section in your json configuration file.");
        }

        _registrations[type] = ConsumerRegistration.Create(type, _connectionOptions, consumerOptions);
    }

    private void AddBatchConsumerInternal(Type type, Action<BulkFetchOptions>? consumerOptions = null)
    {
        if (_connectionOptions.ConsumerPrefix == null)
        {
            throw new InvalidOperationException(
                $"{nameof(_connectionOptions.ConsumerPrefix)} must be set in {NatsConnectionOptions.MessageServiceSectionName} section in your json configuration file.");
        }

        _registrations[type] = ConsumerRegistration.CreateWithBathOptions(type, _connectionOptions, consumerOptions);
    }
}