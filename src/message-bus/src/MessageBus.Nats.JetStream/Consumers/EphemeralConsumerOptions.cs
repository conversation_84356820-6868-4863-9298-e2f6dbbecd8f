namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using Abstractions.Policies;
using NATS.Client.JetStream.Models;

public class EphemeralConsumerOptions
{
    public string Subject { get; set; }

    public string Description { get; set; }

    public int RequestSize { get; set; } = 10;

    public TimeSpan InactiveThreshold { get; set; } = TimeSpan.FromSeconds(30);

    public DeliveryPolicy DeliveryPolicy { get; set; } = new DeliverAllMessagesPolicy();

    public ConsumerConfigAckPolicy AckPolicy { get; set; } = ConsumerConfigAckPolicy.None;

    public static implicit operator ConsumerConfig(EphemeralConsumerOptions options)
    {
        return new ConsumerConfig
        {
            Description = options.Description,
            FilterSubjects = [options.Subject],
            InactiveThreshold = options.InactiveThreshold,
            MaxAckPending = options.RequestSize,
            MemStorage = true,
            MaxBatch = options.RequestSize,
            DeliverPolicy = options.DeliveryPolicy.Policy,
            OptStartSeq = options.DeliveryPolicy.StartSequence,
            OptStartTime = options.DeliveryPolicy.StartTime,
            AckPolicy = options.AckPolicy
        };
    }
}