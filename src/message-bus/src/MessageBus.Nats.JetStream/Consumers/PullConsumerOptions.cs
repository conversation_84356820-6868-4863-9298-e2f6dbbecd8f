namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using NATS.Client.JetStream.Models;

public sealed class PullConsumerOptions : ConsumerOptionsBase
{
    public PullConsumerOptions(
        string consumerName,
        IEnumerable<EventType> eventTypes,
        ConsumerConfigurationOptions consumerConfiguration) : base(
        consumerName,
        eventTypes,
        consumerConfiguration.DeliveryPolicy)
    {
    }

    protected override long MaxAckPendingMessages { get; } = 1;

    protected override int MaxMessages { get; set; } = 1;

    protected override ConsumerConfigAckPolicy AcknowledgmentPolicy { get; } = ConsumerConfigAckPolicy.Explicit;
}