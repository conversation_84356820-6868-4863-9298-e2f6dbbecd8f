namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using Abstractions;
using Abstractions.Extensions;
using Hosting.Extensions;

public class ConsumerGroupRegistration
{
    private readonly NatsConnectionOptions _connectionOptions;

    public ConsumerGroupRegistration(
        string groupName,
        NatsConnectionOptions connectionOptions,
        IReadOnlyCollection<Type> consumers)
    {
        if (string.IsNullOrWhiteSpace(groupName))
            throw new ArgumentException("Value cannot be null or whitespace.", nameof(groupName));

        _connectionOptions = connectionOptions;
        var normalizedConsumerPrefix = string.Join(
            "-",
            _connectionOptions.ConsumerPrefix.Split([' '], StringSplitOptions.RemoveEmptyEntries));
        ConsumerGroupName = $"{normalizedConsumerPrefix}_{groupName}".UseKebabCaseFormatting();
        ConsumerRegistrations = consumers
            .Select(type => new ConsumerRegistration(type, _connectionOptions, ConsumerGroupName))
            .ToList();

        var subscribedEvents = ConsumerRegistrations.SelectMany(x => x.EventTypes.Values);

        if (subscribedEvents.Select(x => x.Stream.Name).Distinct().Count() > 1)
            throw new ConsumerRegistrationException(
                ConsumerGroupName,
                "All consumers in a group must subscribe to the same stream");
    }

    public string ConsumerGroupName { get; }

    public IReadOnlyCollection<ConsumerRegistration> ConsumerRegistrations { get; }

    public ConsumerConfigurationOptions? ConsumerConfiguration { get; set; }


    public ConsumerOptionsBase GetConsumerConfiguration(IReadOnlyCollection<EventType> eventTypes)
    {
        if (!eventTypes.Any(x => x.IsBatch))
            return new PullConsumerOptions(
                ConsumerGroupName,
                eventTypes,
                ConsumerConfiguration ?? new ConsumerConfigurationOptions());

        var batchSize = (ConsumerConfiguration as BulkFetchOptions)?.MaxMessages ?? _connectionOptions.BatchSize;

        if (batchSize == 0)
        {
            throw new ConsumerRegistrationException(ConsumerGroupName, "Batch size must be greater than 0");
        }

        return new BulkConsumerOptions(ConsumerGroupName, eventTypes, new BulkFetchOptions { MaxMessages = batchSize });
    }
}