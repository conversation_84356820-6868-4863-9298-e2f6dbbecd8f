namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using Abstractions;
using Abstractions.Policies;
using NATS.Client.JetStream.Models;

public abstract class ConsumerOptionsBase
{
    protected ConsumerOptionsBase(string consumerName, IEnumerable<EventType> eventTypes, DeliveryPolicy deliveryPolicy)
        : this(consumerName, eventTypes, deliveryPolicy, new EventTopologyProvider())
    {
    }

    protected ConsumerOptionsBase(
        string consumerName,
        IEnumerable<EventType> eventTypes,
        DeliveryPolicy deliveryPolicy,
        IEventTopologyProvider topologyProvider)
    {
        DeliveryPolicy = deliveryPolicy;
        DurableName = consumerName;

        FilterSubjects = eventTypes
            .Select(x => string.Format(topologyProvider.GetTopology(x.Value).SubjectTemplate, "*"))
            .ToArray();
    }

    /// <summary>
    ///     Event consumer name. Used to create durable consumer.
    /// </summary>
    internal string DurableName { get; }

    /// <summary>
    ///     This can be particularly useful for ephemeral consumers to indicate their purpose since the durable name cannot be
    ///     provided.
    /// </summary>
    protected string? Description { get; set; }

    protected internal string[] FilterSubjects { get; set; }

    /// <summary>
    ///     Maximum number of attempts to deliver a message before it is considered a failure
    ///     and moving it to error queue
    /// </summary>
    protected virtual int MaxDeliveryAttempts { get; set; } = 3;

    /// <summary>
    ///     Sets the percentage of acknowledgements that should be sampled for observability, 0-100
    ///     This value is a string and for example allows both 30 and 30% as valid values.
    /// </summary>
    protected virtual string SampleFrequency { get; set; } = "25%";

    /// <summary>
    ///     Delivers only the headers of messages in the natsStream and not the bodies.
    ///     Additionally adds Nats-Msg-Size header to indicate the size of the removed payload.
    /// </summary>
    protected virtual bool HeadersOnly { get; set; } = false;

    /// <summary>
    ///     Force the consumer state to be kept in memory rather than inherit the setting from the natsStream
    /// </summary>
    protected virtual bool MemStorage { get; set; } = false;

    protected virtual ConsumerConfigReplayPolicy ReplayPolicy { get; set; } = ConsumerConfigReplayPolicy.Instant;

    /// <summary>
    ///     The maximum number of messages without acknowledgement that can be outstanding,
    ///     once this limit is reached message delivery will be suspended
    /// </summary>
    protected abstract long MaxAckPendingMessages { get; }

    /// <summary>
    ///     Configures consumer ack policy>
    /// </summary>
    protected abstract ConsumerConfigAckPolicy AcknowledgmentPolicy { get; }

    protected virtual TimeSpan AckWait { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    ///     Configures consumer deliver policy>
    /// </summary>
    protected DeliveryPolicy DeliveryPolicy { get; }

    /// <summary>
    ///     Max message requested in one pull request from server
    /// </summary>

    protected virtual int MaxMessages { get; set; }

    public static implicit operator ConsumerConfig(ConsumerOptionsBase options) =>
        new(options.DurableName!)
        {
            Description = options.Description,
            FilterSubjects = options.FilterSubjects,
            MaxAckPending = options.MaxAckPendingMessages,
            MaxDeliver = options.MaxDeliveryAttempts,
            SampleFreq = options.SampleFrequency,
            HeadersOnly = options.HeadersOnly,
            MemStorage = options.MemStorage,
            MaxBatch = options.MaxMessages,
            DeliverPolicy = options.DeliveryPolicy.Policy,
            OptStartSeq = options.DeliveryPolicy.StartSequence,
            OptStartTime = options.DeliveryPolicy.StartTime,
            AckPolicy = options.AcknowledgmentPolicy,
            AckWait = options.AckWait
        };
}