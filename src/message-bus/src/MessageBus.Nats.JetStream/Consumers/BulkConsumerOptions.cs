namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using Abstractions.Policies;
using NATS.Client.JetStream.Models;

public sealed class BulkConsumerOptions : ConsumerOptionsBase
{
    public BulkConsumerOptions(string consumerName, IEnumerable<EventType> eventTypes, BulkFetchOptions fetchOptions) :
        base(consumerName, eventTypes, fetchOptions.DeliveryPolicy)
    {
        MaxMessages = fetchOptions.MaxMessages;
    }

    public BulkConsumerOptions(
        string consumerName,
        IEnumerable<EventType> eventTypes,
        DeliveryPolicy deliveryPolicy,
        int batchSize) : base(consumerName, eventTypes, deliveryPolicy)
    {
        MaxMessages = batchSize;
    }

    protected override long MaxAckPendingMessages => MaxMessages;

    protected override ConsumerConfigAckPolicy AcknowledgmentPolicy => ConsumerConfigAckPolicy.All;

    protected override TimeSpan AckWait { get; set; } = TimeSpan.FromSeconds(60);
}