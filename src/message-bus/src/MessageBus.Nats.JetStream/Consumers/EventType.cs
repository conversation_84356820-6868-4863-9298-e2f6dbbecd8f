namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using Abstractions.Consumers;
using Abstractions.Events;
using Abstractions.Extensions;
using Abstractions.Streams;

public class EventType : IEquatable<EventType>
{
    public EventType(Type eventType)
    {
        if (eventType.IsGenericType && eventType.GetGenericTypeDefinition() == typeof(Batch<>))
        {
            Value = eventType.GetGenericArguments()[0];
            IsBatch = true;
        }
        else
        {
            Value = eventType;
        }

        Stream = Value.GetGenericInterfacesTypeArguments(typeof(IEvent<>))
            .Select(x => (NatsStreamBase) Activator.CreateInstance(x)!)
            .Distinct()
            .Single();
    }

    public Type Value { get; }

    public NatsStreamBase Stream { get; }

    public bool IsBatch { get; }

    public bool Equals(EventType? other)
    {
        if (other is null) return false;

        return ReferenceEquals(this, other) || Value.Name == other.Value.Name;
    }

    public override int GetHashCode() => Value.Name.GetHashCode();

    public override bool Equals(object? obj) => Equals(obj as EventType);
}