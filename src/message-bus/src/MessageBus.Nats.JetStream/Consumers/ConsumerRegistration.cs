namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using System.Reflection;
using Abstractions;
using Abstractions.Consumers;
using Abstractions.Extensions;
using Hosting.Extensions;

public sealed class ConsumerRegistration : IEquatable<ConsumerRegistration>
{
    private readonly NatsConnectionOptions _connectionOptions;
    private readonly ConsumerConfigurationOptions _options;

    internal ConsumerRegistration(
        Type consumerType,
        NatsConnectionOptions connectionOptions,
        string? consumerName = null,
        ConsumerConfigurationOptions? options = null)
    {
        _connectionOptions = connectionOptions;
        _options = options ?? new ConsumerConfigurationOptions();
        ConsumerType = consumerType;
        ConsumerPrefix = connectionOptions.ConsumerPrefix;
        EventTypes = GetConsumerSubscribedEvents(consumerType);
        ConsumerName = consumerName ?? GetConsumerName();
    }


    public Type ConsumerType { get; }

    public string ConsumerName { get; }

    public string ConsumerPrefix { get; }

    public Dictionary<Type, EventType> EventTypes { get; }

    public bool BatchConsumer => EventTypes.Values.Any(x => x.IsBatch);

    public bool Equals(ConsumerRegistration? other)
    {
        if (ReferenceEquals(null, other))
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return ConsumerType == other.ConsumerType;
    }

    public static ConsumerRegistration Create(
        Type consumerType,
        NatsConnectionOptions connectionOptions,
        Action<ConsumerConfigurationOptions>? consumerOptions = null)
    {
        ConsumerConfigurationOptions fetchOptions = new();
        consumerOptions?.Invoke(fetchOptions);
        return new ConsumerRegistration(consumerType, connectionOptions, options: fetchOptions);
    }

    public static ConsumerRegistration CreateWithBathOptions(
        Type consumerType,
        NatsConnectionOptions connectionOptions,
        Action<BulkFetchOptions>? bulkConfigOptions = null)
    {
        var options = new BulkFetchOptions();
        bulkConfigOptions?.Invoke(options);
        var registration = new ConsumerRegistration(consumerType, connectionOptions, options: options);

        return registration;
    }

    public ConsumerOptionsBase GetConsumerOptions(IEnumerable<EventType> eventTypes)
    {
        if (!BatchConsumer) return new PullConsumerOptions(ConsumerName, eventTypes, _options);

        var batchSize = (_options as BulkFetchOptions)?.MaxMessages ?? _connectionOptions.BatchSize;

        if (batchSize == 0)
        {
            throw new ConsumerRegistrationException(ConsumerType.Name, "Batch size must be greater than 0");
        }

        return new BulkConsumerOptions(ConsumerName, eventTypes, _options.DeliveryPolicy, batchSize);
    }

    private static Dictionary<Type, EventType> GetConsumerSubscribedEvents(Type consumerType) =>
        consumerType.GetGenericInterfacesTypeArguments(typeof(IEventConsumer<>))
            .Select(x => new EventType(x))
            .ToDictionary(x => x.Value);

    private string GetConsumerName()
    {
        var normalizedConsumerPrefix = string.Join(
            "-",
            ConsumerPrefix.Split([' '], StringSplitOptions.RemoveEmptyEntries));
        var consumerNameAttribute = ConsumerType.GetCustomAttribute<ConsumerNameAttribute>();
        return $"{normalizedConsumerPrefix}_{consumerNameAttribute?.Name ?? ResolveConsumerName()}"
            .UseKebabCaseFormatting();

        string ResolveConsumerName() =>
            ConsumerType.IsGenericType
                ? $"{ConsumerType.Name[..ConsumerType.Name.IndexOf('`')]}{EventTypes.Values.First().Value.Name}"
                : ConsumerType.Name;
    }

    public override bool Equals(object? obj) => Equals((ConsumerRegistration?) obj);

    public override int GetHashCode() => ConsumerType.Name.GetHashCode();
}