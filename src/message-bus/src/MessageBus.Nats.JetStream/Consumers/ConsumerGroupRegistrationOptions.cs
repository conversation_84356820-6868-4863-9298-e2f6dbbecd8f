namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Consumers;

using Abstractions.Consumers;

public class ConsumerGroupRegistrationOptions
{
    internal List<Type> Consumers { get; } = [];

    internal ConsumerConfigurationOptions? ConsumerConfiguration { get; private set; }

    public ConsumerGroupRegistrationOptions AddConsumer<TConsumer>()
        where TConsumer : IEventConsumer
    {
        Consumers.Add(typeof(TConsumer));

        return this;
    }


    public ConsumerGroupRegistrationOptions WithConsumerConfiguration(
        Action<ConsumerConfigurationOptions>? configure = null)
    {
        if (configure == null) return this;

        var options = new ConsumerConfigurationOptions();
        configure(options);
        ConsumerConfiguration = options;

        return this;
    }

    public ConsumerGroupRegistrationOptions WithBatchConsumerConfiguration(Action<BulkFetchOptions>? configure = null)
    {
        if (configure == null) return this;

        var options = new BulkFetchOptions();
        configure(options);
        ConsumerConfiguration = options;

        return this;
    }
}