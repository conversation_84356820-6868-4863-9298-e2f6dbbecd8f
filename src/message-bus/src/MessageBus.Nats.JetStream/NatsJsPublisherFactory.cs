using Kukui.Infrastructure.MessageBus.Abstractions.Publisher;
using Microsoft.Extensions.DependencyInjection;

namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

public class NatsJsPublisherFactory : IPublisherFactory
{
    private readonly IServiceProvider _serviceProvider;

    public NatsJsPublisherFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public IPublisher Create(Type messageType)
    {
        var publisherType = typeof(IPublisher<>).MakeGenericType(messageType);

        return (IPublisher)_serviceProvider.GetRequiredService(publisherType);
    }
}