namespace Kukui.Infrastructure.MessageBus.Nats.JetStream;

using Abstractions;
using Abstractions.Events;
using Abstractions.Publisher;
using Microsoft.Extensions.Logging;
using NATS.Client.Core;
using NATS.Client.JetStream;

public class NatsJsPublisher<TEvent> : IPublisher<TEvent>
    where TEvent : IEvent
{
    private readonly INatsJSContext _jsContext;
    private readonly ILogger _logger;
    private readonly IEventTopologyProvider _topologyProvider;
    private readonly INatsSerializer<TEvent> _serializer;

    public NatsJsPublisher(
        INatsJSContext jsContext,
        ILogger<NatsJsPublisher<TEvent>> logger,
        IEventTopologyProvider topologyProvider,
        INatsSerializer<TEvent> serializer)
    {
        _jsContext = jsContext;
        _logger = logger;
        _topologyProvider = topologyProvider;
        _serializer = serializer;
    }

    public Task PublishAsync(TEvent message, CancellationToken cancellationToken) =>
        PublishAsync(message, null, cancellationToken);

    public async Task PublishAsync(
        TEvent message,
        NatsHeaders? headers = null,
        CancellationToken cancellationToken = default)
    {
        var publishOptions = NatsJSPubOpts.Default with { MsgId = message.Metadata.IdempotenceKey, RetryAttempts = 3 };
        message.Metadata.Topology = _topologyProvider.GetTopology<TEvent>();
#pragma warning disable CA1305
        message.Metadata.Subject = string.Format(message.Metadata.Topology.SubjectTemplate, message.Id);
#pragma warning restore CA1305

        try
        {
            var response = await _jsContext.PublishAsync(
                message.Metadata.Subject,
                message,
                _serializer,
                publishOptions,
                headers,
                cancellationToken);

            response.EnsureSuccess();
            _logger.LogDebug(
                "Published {EventType} message with Id {MessageId} to subject {Subject}",
                typeof(TEvent),
                message.Id,
                message.Metadata.Subject);
        }
        catch (NatsJSDuplicateMessageException e)
        {
            _logger.LogWarning(
                e,
                "Duplicate {EventType} message with  sequence number {SeqNumber} and payload {@Message}",
                typeof(TEvent).Name,
                e.Sequence,
                message);
        }
    }

    public Task ScheduleAsync(
        TEvent message,
        DateTimeOffset scheduleTime,
        CancellationToken cancellationToken = default)
    {
        if (scheduleTime < DateTimeOffset.UtcNow)
        {
            throw new ArgumentException("Schedule time must be in the future", nameof(scheduleTime));
        }

        return PublishAsync(
            message,
            new NatsHeaders { { MessageConstants.ScheduleTime, scheduleTime.ToUnixTimeMilliseconds().ToString() } },
            cancellationToken);
    }

    public Task RePublishAsync(TEvent message, CancellationToken cancellationToken) =>
        PublishAsync(message, cancellationToken);

    public Task PublishAsync(IEvent message, CancellationToken cancellationToken) =>
        PublishAsync(message, null, cancellationToken);

    public Task PublishAsync(
        IEvent message,
        NatsHeaders? headers = null,
        CancellationToken cancellationToken = default) =>
        PublishAsync((TEvent) message, headers, cancellationToken);

    public Task ScheduleAsync(
        IEvent message,
        DateTimeOffset scheduleTime,
        CancellationToken cancellationToken = default) =>
        ScheduleAsync((TEvent) message, scheduleTime, cancellationToken);

    public Task RePublishAsync(IEvent message, CancellationToken cancellationToken) =>
        RePublishAsync(message, null, cancellationToken);

    public async Task RePublishAsync(
        TEvent message,
        NatsHeaders? headers = null,
        CancellationToken cancellationToken = default)
    {
        var publishOptions = NatsJSPubOpts.Default with { RetryAttempts = 3 };
        message.Metadata.Topology = _topologyProvider.GetTopology<TEvent>();
#pragma warning disable CA1305
        message.Metadata.Subject = string.Format(message.Metadata.Topology.SubjectTemplate, message.Id);
#pragma warning restore CA1305

        try
        {
            var response = await _jsContext.PublishAsync(
                message.Metadata.Subject,
                message,
                _serializer,
                publishOptions,
                headers,
                cancellationToken);

            response.EnsureSuccess();
            _logger.LogDebug(
                "Published {EventType} message with Id {MessageId} to subject {Subject}",
                typeof(TEvent),
                message.Id,
                message.Metadata.Subject);
        }
        catch (NatsJSException e)
        {
            _logger.LogWarning(e, "Failed to republish message");
        }
    }

    public Task RePublishAsync(
        IEvent message,
        NatsHeaders? headers = null,
        CancellationToken cancellationToken = default) =>
        RePublishAsync((TEvent) message, headers, cancellationToken);
}