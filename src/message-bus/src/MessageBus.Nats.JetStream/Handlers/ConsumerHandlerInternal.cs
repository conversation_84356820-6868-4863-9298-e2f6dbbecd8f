namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Handlers;

using Abstractions.Consumers;
using Abstractions.Events;
using Abstractions.Pipeline;
using Consumers;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

public class ConsumerHandlerInternal<TEvent> : ConsumerHandlerInternalBase
    where TEvent : class, IEvent
{
    private readonly EventType _eventType;

    public ConsumerHandlerInternal(EventType eventType, IServiceProvider serviceProvider) : base(
        serviceProvider,
        serviceProvider.GetRequiredService<ILogger<ConsumerHandlerInternal<TEvent>>>())
    {
        _eventType = eventType;
    }

    protected override async Task ProcessEventsAsync(ConsumeContext consumeContext, CancellationToken cancellationToken)
    {
        Logger.LogDebug(
            "[{StreamName}]: Received {EventType} event with {MessageId}",
            consumeContext.Consumer.StreamName,
            consumeContext.Message.GetType().Name,
            ((IEvent) consumeContext.Message).Id);

        using var providerScope = ServiceProvider.CreateScope();
        var handler =
            providerScope.ServiceProvider.GetRequiredKeyedService<IEventConsumer<TEvent>>(consumeContext.Consumer.Name);

        var context = new ConsumeContext<TEvent>(
            (TEvent) consumeContext.Message,
            consumeContext.Subject,
            consumeContext.Consumer,
            consumeContext.Headers,
            consumeContext.Metadata)
        {
            MessagePayload = consumeContext.MessagePayload
        };

        await providerScope.ServiceProvider.GetServices<IPipeline<ConsumeContext<TEvent>, TEvent>>()
            .Reverse()
            .Aggregate(
                (MessageRequestDelegate<ConsumeContext<TEvent>>) ConsumerHandlerDelegate,
                (next, pipeline) => token => pipeline.ExecuteStepAsync(context, next, token))(cancellationToken)
            .ConfigureAwait(false);
        return;

        Task ConsumerHandlerDelegate(CancellationToken token) => handler.ConsumeAsync(context, cancellationToken);
    }

    protected override async Task ProcessBatchEventsAsync(
        IReadOnlyCollection<ConsumeContext> eventBatch,
        CancellationToken cancellationToken)
    {
        if (!_eventType.IsBatch)
        {
            foreach (var consumeContext in eventBatch)
            {
                await ProcessEventsAsync(consumeContext, cancellationToken);
            }

            return;
        }

        var lastMessage = eventBatch.Last();
        var ctx = new ConsumeContext<Batch<TEvent>>(
            new Batch<TEvent>(eventBatch.Select(x => (TEvent) x.Message).ToList()),
            lastMessage.Subject,
            lastMessage.Consumer)
        {
            MessagePayload = lastMessage.MessagePayload
        };

        Logger.LogDebug(
            "[{StreamName}]: Received batch Id {BatchId} with {BatchSize} {EventType} messages",
            ctx.Consumer.StreamName,
            ctx.Message.Id,
            ctx.Message.Count,
            ctx.EventType.Name);

        using var providerScope = ServiceProvider.CreateScope();
        var handler =
            providerScope.ServiceProvider.GetRequiredKeyedService<IEventConsumer<Batch<TEvent>>>(
                lastMessage.Consumer.Name);

        await providerScope.ServiceProvider.GetServices<IPipeline<ConsumeContext<Batch<TEvent>>, TEvent>>()
            .Reverse()
            .Aggregate(
                (MessageRequestDelegate<ConsumeContext<Batch<TEvent>>>) ConsumerHandlerDelegate,
                (next, pipeline) => token => pipeline.ExecuteStepAsync(ctx, next, token))(cancellationToken)
            .ConfigureAwait(false);
        return;

        Task ConsumerHandlerDelegate(CancellationToken token) => handler.ConsumeAsync(ctx, cancellationToken);
    }
}