namespace Kukui.Infrastructure.MessageBus.Nats.JetStream.Handlers;

using Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public abstract class ConsumerHandlerInternalBase
{
    protected IServiceProvider ServiceProvider { get; }

    protected readonly ILogger Logger;

    protected ConsumerHandlerInternalBase(IServiceProvider serviceProvider, ILogger logger)
    {
        ServiceProvider = serviceProvider;
        Logger = logger;
    }

    public async Task HandleInternalAsync(ConsumeContext context, CancellationToken cancellationToken)
    {
        await ProcessEventsAsync(context, cancellationToken);
    }

    public async Task HandleBatchInternalAsync(
        IReadOnlyCollection<ConsumeContext> eventBatch,
        CancellationToken cancellationToken)
    {
        if (eventBatch.Count == 0)
        {
            return;
        }

        await ProcessBatchEventsAsync(eventBatch, cancellationToken);
    }

    protected abstract Task ProcessEventsAsync(ConsumeContext consumeContext, CancellationToken cancellationToken);

    protected abstract Task ProcessBatchEventsAsync(
        IReadOnlyCollection<ConsumeContext> eventBatch,
        CancellationToken cancellationToken);
}