namespace Kukui.Infrastructure.MessageBus.Nats.RqRs;

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NATS.Client.Core;
using RequestHandlers;

public class NatsRqRsRegistrationService : BackgroundService
{
    private readonly ILogger<NatsRqRsRegistrationService> _logger;
    private readonly INatsConnection _natsConnection;
    private readonly RequestHandlersRegistrator _requestHandlersRegistrator;

    public NatsRqRsRegistrationService(
        RequestHandlersRegistrator requestHandlersRegistrator,
        INatsConnection natsConnection,
        ILogger<NatsRqRsRegistrationService> logger)
    {
        _requestHandlersRegistrator = requestHandlersRegistrator;
        _natsConnection = natsConnection;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            await _natsConnection.ConnectAsync();
            await _requestHandlersRegistrator.RegisterAsync(stoppingToken);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to start NATS RqRs registration service");
        }
    }
}