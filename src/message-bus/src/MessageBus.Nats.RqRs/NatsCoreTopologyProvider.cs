namespace Kukui.Infrastructure.MessageBus.Nats.RqRs;

using System.Collections.Concurrent;
using Abstractions.RqRs;

public class NatsCoreTopologyProvider : INatsCoreTopologyProvider
{
    private readonly ConcurrentDictionary<Type, SubjectTemplate> _templates = [];

    public string GetRoute<TMessage>()
        where TMessage : class =>
        GetSubjectTemplate<TMessage>().Route;

    public string GetSubject<TMessage>(TMessage message)
        where TMessage : class =>
        GetSubjectTemplate<TMessage>().GetSubject(message);

    private SubjectTemplate GetSubjectTemplate<TMessage>()
        where TMessage : class =>
        _templates.GetOrAdd(typeof(TMessage), key => new SubjectTemplate(key));
}