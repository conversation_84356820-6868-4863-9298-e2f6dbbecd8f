namespace Kukui.Infrastructure.MessageBus.Nats.RqRs;

using Abstractions.Publisher;
using Abstractions.RqRs;
using Hosting.Extensions;
using Infrastructure.Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using RequestHandlers;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddNatsRqRs(
        this IServiceCollection services,
        ServerConfiguration configuration,
        Action<RequestHandlerConfigurationOptions> configure)
    {
        services.TryAddNatsConnection(configuration)
            .AddSingleton<RequestHandlersRegistrator>()
            .AddSingleton<INatsCoreTopologyProvider, NatsCoreTopologyProvider>()
            .AddSingleton(typeof(INatsRequestClient<,>), typeof(NatsRequestClient<,>))
            .AddSingleton<INotificationPublisher, NotificationPublisher>()
            .AddHostedService<NatsRqRsRegistrationService>();
        RequestHandlerConfigurationOptions requestHandlerConfiguration = new();
        configure(requestHandlerConfiguration);
        requestHandlerConfiguration.AddRegistrations(services);

        return services;
    }
}