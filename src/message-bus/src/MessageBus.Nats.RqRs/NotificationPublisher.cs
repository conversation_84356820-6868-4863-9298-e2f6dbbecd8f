namespace Kukui.Infrastructure.MessageBus.Nats.RqRs;

using Abstractions.Publisher;
using Abstractions.RqRs;
using Hosting.Extensions;
using Microsoft.Extensions.Logging;
using NATS.Client.Core;

public class NotificationPublisher : INotificationPublisher
{
    private readonly ILogger<NotificationPublisher> _logger;
    private readonly NatsConnection _natsConnection;
    private readonly INatsSerializerFactory _serializerFactory;
    private readonly INatsCoreTopologyProvider _topologyProvider;

    public NotificationPublisher(
        INatsSerializerFactory serializerFactory,
        NatsConnection natsConnection,
        INatsCoreTopologyProvider topologyProvider,
        ILogger<NotificationPublisher> logger)
    {
        _serializerFactory = serializerFactory;
        _natsConnection = natsConnection;
        _topologyProvider = topologyProvider;
        _logger = logger;
    }

    public async ValueTask PublishAsync<TMessage>(TMessage message, CancellationToken cancellationToken = default)
        where TMessage : class
    {
        var subject = _topologyProvider.GetSubject(message);
        await _natsConnection.PublishAsync(
            subject,
            message,
            serializer: _serializerFactory.GetSerializer<TMessage>(),
            cancellationToken: cancellationToken);

        _logger.LogDebug("Notification published to {MessageSubject} subject: {@Message}", subject, message);
    }
}