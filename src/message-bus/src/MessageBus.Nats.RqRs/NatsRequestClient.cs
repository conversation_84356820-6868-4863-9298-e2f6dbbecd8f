namespace Kukui.Infrastructure.MessageBus.Nats.RqRs;

using System.Runtime.CompilerServices;
using Abstractions.RqRs;
using Hosting.Extensions;
using NATS.Client.Core;

public class NatsRequestClient<TRequest, TResponse> : INatsRequestClient<TRequest, TResponse>
    where TRequest : class, INatsRequest<TResponse>
{
    private readonly NatsConnection _natsConnection;
    private readonly INatsSerializerFactory _serializerFactory;
    private readonly INatsCoreTopologyProvider _topologyProvider;

    public NatsRequestClient(
        NatsConnection natsConnection,
        INatsSerializerFactory serializerFactory,
        INatsCoreTopologyProvider topologyProvider)
    {
        _natsConnection = natsConnection;
        _serializerFactory = serializerFactory;
        _topologyProvider = topologyProvider;
    }

    public async ValueTask<TResponse?> SendRequestAsync(TRequest message, CancellationToken cancellationToken)
    {
        var response = await _natsConnection.RequestAsync(
            _topologyProvider.GetSubject(message),
            message,
            requestSerializer: _serializerFactory.GetSerializer<TRequest>(),
            replySerializer: _serializerFactory.GetSerializer<TResponse>(),
            cancellationToken: cancellationToken);
        return response.Data;
    }

    public async IAsyncEnumerable<TResponse> SendRequestWithResponsesAsync(
        TRequest message,
        [EnumeratorCancellation] CancellationToken cancellationToken)
    {
        var requestManyAsync = _natsConnection.RequestManyAsync(
            _topologyProvider.GetSubject(message),
            message,
            requestSerializer: _serializerFactory.GetSerializer<TRequest>(),
            replySerializer: _serializerFactory.GetSerializer<TResponse>(),
            cancellationToken: cancellationToken);

        await foreach (var response in requestManyAsync)
        {
            if (response.Data == null) yield break;

            yield return response.Data;
        }
    }
}