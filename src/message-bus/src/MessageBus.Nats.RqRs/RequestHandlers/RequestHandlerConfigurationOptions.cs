namespace Kukui.Infrastructure.MessageBus.Nats.RqRs.RequestHandlers;

using System.Reflection;
using Abstractions.RqRs;
using Microsoft.Extensions.DependencyInjection;

public class RequestHandlerConfigurationOptions
{
    private List<Assembly> AssembliesToScan { get; } = new();

    internal Dictionary<string, Type> RequestHandlers { get; } = new();

    public RequestHandlerConfigurationOptions RegisterRequestHandlersFromAssembly(Assembly assembly)
    {
        AssembliesToScan.Add(assembly);
        return this;
    }

    public RequestHandlerConfigurationOptions RegisterRequestHandlersFromAssemblyContaining(Type type)
    {
        RegisterRequestHandlersFromAssembly(type.Assembly);
        return this;
    }

    public RequestHandlerConfigurationOptions RegisterRequestHandlersFromAssemblyContaining<T>()
    {
        RegisterRequestHandlersFromAssemblyContaining(typeof(T));
        return this;
    }

    internal void AddRegistrations(IServiceCollection services)
    {
        services.Scan(
            ass => ass.FromAssemblies(AssembliesToScan)
                .AddClasses(classes => classes.AssignableTo(typeof(INatsRequestHandler<,>)))
                .AsImplementedInterfaces()
                .WithTransientLifetime());
    }
}