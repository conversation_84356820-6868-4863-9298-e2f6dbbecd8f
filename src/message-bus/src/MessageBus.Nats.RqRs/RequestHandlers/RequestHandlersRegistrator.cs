namespace Kukui.Infrastructure.MessageBus.Nats.RqRs.RequestHandlers;

using System.Reflection;
using Abstractions.Extensions;
using Abstractions.RqRs;
using Hosting.Extensions;
using Infrastructure.Hosting.Common.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NATS.Client.Core;

public class RequestHandlersRegistrator
{
    private readonly ILogger<RequestHandlersRegistrator> _logger;
    private readonly INatsSerializerFactory _serializerFactory;
    private readonly IServiceProvider _serviceProvider;
    private readonly INatsCoreTopologyProvider _topologyProvider;

    public RequestHandlersRegistrator(
        IServiceProvider serviceProvider,
        ILogger<RequestHandlersRegistrator> logger,
        INatsCoreTopologyProvider topologyProvider,
        INatsSerializerFactory serializerFactory)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _topologyProvider = topologyProvider;
        _serializerFactory = serializerFactory;
    }

    public Task RegisterAsync(CancellationToken cancellationToken = default)
    {
        var requestHandlers = _serviceProvider.GetServices(typeof(INatsRequestHandler));
        var natsConnection = _serviceProvider.GetRequiredService<NatsConnection>();
        var configuration = _serviceProvider.GetRequiredService<ServerConfiguration>();
        foreach (var requestHandler in requestHandlers)
        {
            var requestTypeArguments = requestHandler!.GetType()
                .GetGenericInterfacesTypeArguments(typeof(INatsRequestHandler<,>));
            var request = requestTypeArguments[0];
            var responseType = requestTypeArguments[1];

            var method = typeof(RequestHandlersRegistrator).GetMethod(
                nameof(ProcessNatsRequestAsync),
                BindingFlags.NonPublic | BindingFlags.Instance);
            var genericMethod = method!.MakeGenericMethod(request, responseType);
            genericMethod.Invoke(this, [natsConnection, configuration, cancellationToken]);
        }

        return Task.CompletedTask;
    }

    private async Task ProcessNatsRequestAsync<TRequest, TResponse>(
        INatsConnection natsConnection,
        ServerConfiguration serverConfiguration,
        CancellationToken cancellationToken)
        where TRequest : class, INatsRequest<TResponse>
    {
        await Task.Run(
            async () =>
            {
                var subscription = natsConnection.SubscribeAsync(
                    _topologyProvider.GetRoute<TRequest>(),
                    serverConfiguration.DisplayName.UseKebabCaseFormatting(),
                    _serializerFactory.GetSerializer<TRequest>(),
                    cancellationToken: cancellationToken);

                await foreach (var message in subscription)
                {
                    await HandleMessageAsync<TRequest, TResponse>(message, cancellationToken);
                }
            },
            cancellationToken);
    }

    private async ValueTask HandleMessageAsync<TRequest, TResponse>(
        NatsMsg<TRequest> natsMsg,
        CancellationToken cancellationToken)
        where TRequest : INatsRequest<TResponse>
    {
        if (natsMsg.Error != null)
        {
            _logger.LogError(natsMsg.Error, "Failed to receive message {RequestType}", typeof(TRequest).Name);
            return;
        }

        if (natsMsg.Data == null)
        {
            return;
        }

        foreach (var handler in _serviceProvider
                     .GetRequiredService<IEnumerable<INatsRequestHandler<TRequest, TResponse>>>())
        {
            try
            {
                var response = await handler.HandleRequestAsync(natsMsg.Data, cancellationToken);
                if (response != null && response.GetType() != typeof(Unit))
                {
                    await natsMsg.ReplyAsync(
                        response,
                        serializer: _serializerFactory.GetSerializer<TResponse>(),
                        cancellationToken: cancellationToken);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Failed to handle request {RequestType}", typeof(TRequest).Name);
            }
        }
    }
}