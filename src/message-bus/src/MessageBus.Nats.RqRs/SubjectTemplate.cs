namespace Kukui.Infrastructure.MessageBus.Nats.RqRs;

using System.Reflection;
using Abstractions.Extensions;
using Abstractions.RqRs;

public class SubjectTemplate
{
    private readonly IDictionary<string, int> _routingProperties;

    public SubjectTemplate(Type messageType)
    {
        var template = messageType.GetCustomAttribute<NatsSubjectAttribute>()?.SubjectTemplate ??
                       messageType.FullName ?? throw new InvalidOperationException(
                           $"Message type {messageType.Name} does not have a subject template defined");

        var properties = messageType.GetProperties().Select(p => p.Name).ToList();
        Template = template;
        Parts = Template.Split(['.'], StringSplitOptions.RemoveEmptyEntries);
        _routingProperties = Parts.WithIndex()
            .Where(x => properties.Contains(x.item))
            .Select(
                x => new
                {
                    Item = x.item,
                    Index = x.index
                })
            .ToDictionary(x => x.Item, x => x.Index);
        Route = _routingProperties.Count > 0
            ? string.Join(".", Parts.Select(x => _routingProperties.ContainsKey(x) ? "*" : x))
            : template;
    }

    public string Template { get; }

    public string[] Parts { get; }

    public string Route { get; }

    public string GetSubject<TMessage>(TMessage message)
        where TMessage : class
    {
        if (_routingProperties.Count == 0)
        {
            return Template;
        }

        foreach (var property in _routingProperties)
        {
            var value = typeof(TMessage).GetProperty(property.Key)?.GetValue(message) ??
                        throw new InvalidOperationException(
                            $"Property {property.Key} is not defined for {typeof(TMessage).Name}");
            Parts[property.Value] = value.ToString()!;
        }

        return string.Join(".", Parts);
    }
}