namespace Kukui.Infrastructure.MessageBus.Abstractions;

using NATS.Client.JetStream.Models;
using Streams;

/// <summary>
/// Retention with the typical behavior of a FIFO queue.
/// Each message can be consumed only once.
/// One consumer per subject restriction(no subject overlap).
/// Once a given message is acknowledged, it will be deleted from the stream.
/// </summary>
public abstract class WorkQueue : NatsStreamBase
{
    protected WorkQueue(string name) : base(name, StreamConfigRetention.Workqueue)
    {
    }

    protected sealed override int MaxConsumers { get; } = 1;
}