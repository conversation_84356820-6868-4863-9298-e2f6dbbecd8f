<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.MessageBus.Abstractions</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.MessageBus.Abstractions</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.HighPerformance" />
    <PackageReference Include="OneOf" />
    <PackageReference Include="OneOf.SourceGenerator" />
    <PackageReference Include="OneOf.Serialization.SystemTextJson" />
    <PackageReference Include="NATS.Client.Core" />
    <PackageReference Include="NATS.Client.Serializers.Json" />
    <PackageReference Include="NATS.Client.JetStream" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Common\Kukui.Hosting.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="Kukui.Infrastructure.MessageBus.Nats.JetStream" />
  </ItemGroup>

</Project>