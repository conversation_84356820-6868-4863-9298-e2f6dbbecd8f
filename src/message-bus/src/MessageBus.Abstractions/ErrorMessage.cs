namespace Kukui.Infrastructure.MessageBus.Abstractions;

using System.Text.Json.Serialization;
using Events;
using NATS.Client.JetStream.Models;

public class ErrorMessage : IEvent
{
    [JsonInclude]
    public string Id { get; private set; }

    [JsonInclude]
    public Guid? BatchId { get; private set; }

    public IEvent? Message { get; set; }

    public string Error { get; set; }

    public string? StackTrace { get; set; }

    public EventMetadata Metadata { get; set; }

    public string TypeInfo { get; set; }

    public static ErrorMessage Create(ConsumerInfo consumer, IEvent message, Exception? exception = null) =>
        new()
        {
            Id = message.Id,
            Message = message,
            Error = $"{exception?.Message}.{exception?.InnerException?.Message}",
            StackTrace = exception?.StackTrace,
            Metadata = new EventMetadata
            {
                Subject = $"{consumer.StreamName}_errors.{consumer.Name}.{message.Id}"
            }
        };

    public static ErrorMessage CreateWithBath(
        ConsumerInfo consumer,
        IEvent message,
        Guid batchId,
        Exception? exception) =>
        new()
        {
            Id = message.Id,
            BatchId = batchId,
            Message = message,
            Error = $"{exception?.Message}.{exception?.InnerException?.Message}",
            StackTrace = exception?.StackTrace,
            Metadata = new EventMetadata
            {
                Subject = $"{consumer.StreamName}_errors.{consumer.Name}.{batchId}.{message.Id}"
            }
        };
}