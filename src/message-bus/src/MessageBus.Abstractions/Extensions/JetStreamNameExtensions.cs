namespace Kukui.Infrastructure.MessageBus.Abstractions.Extensions;

using System.Text;

public static class JetStreamNameExtensions
{
    private static readonly char[] InvalidChars = ['*', '.', '>', '/', '\\'];
    private static readonly char Separator = '-';

    public static string UseKebabCaseFormatting(this string? streamName)
    {
        if (string.IsNullOrWhiteSpace(streamName))
        {
            return string.Empty;
        }

        var builder = new StringBuilder();

        foreach (var (symbol, index) in streamName!.WithIndex())
        {
            if (char.IsWhiteSpace(symbol))
            {
                continue;
            }

            if (char.IsLetter(symbol))
            {
                if (index > 0 && char.IsUpper(symbol) && char.IsLetter(streamName![index - 1]) &&
                    !char.IsUpper(streamName[index - 1]))
                {
                    builder.Append(Separator);
                }

                builder.Append(char.ToLower(symbol));
            }
            else if (char.IsSymbol(Separator) || InvalidChars.Contains(symbol))
            {
                builder.Append(Separator);
            }
            else
            {
                builder.Append(symbol);
            }
        }

        return builder.ToString();
    }

    public static string UseDotCaseFormatting(this string? eventName)
    {
        if (string.IsNullOrWhiteSpace(eventName))
        {
            return string.Empty;
        }

        var builder = new StringBuilder();

        foreach (var (symbol, index) in eventName!.WithIndex())
        {
            if (char.IsUpper(symbol) && index > 0)
            {
                builder.Append('.');
            }

            builder.Append(char.ToLower(symbol));
        }

        return builder.ToString();
    }
}