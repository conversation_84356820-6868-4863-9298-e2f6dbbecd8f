namespace Kukui.Infrastructure.MessageBus.Abstractions.Extensions;

public static class TypeExtensions
{
    public static Type? GetGenericInterface(this Type type, Type interfaceType) =>
        type.GetInterfaces().FirstOrDefault(i => i.IsGenericType && i.GetGenericTypeDefinition() == interfaceType);

    public static Type[] GetGenericInterfaces(this Type type, Type interfaceType) =>
        type.GetInterfaces().Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == interfaceType).ToArray();

    public static Type[] GetGenericInterfacesTypeArguments(this Type type, Type interfaceType) =>
        type.GetGenericInterfaces(interfaceType).SelectMany(x => x.GenericTypeArguments).ToArray();
}