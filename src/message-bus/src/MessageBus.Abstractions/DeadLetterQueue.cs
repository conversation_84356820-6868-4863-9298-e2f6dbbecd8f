namespace Kukui.Infrastructure.MessageBus.Abstractions;

using NATS.Client.JetStream.Models;
using Streams;

public sealed class DeadLetterQueue<TStream> : NatsStreamBase
    where TStream : NatsStreamBase, new()
{
    public DeadLetterQueue() : base(retentionPolicy: StreamConfigRetention.Limits)
    {
        var stream = new TStream();
        MainStream = stream.Name;
        Name = $"{MainStream}_errors";
    }

    public string MainStream { get; set; }

    protected override StreamConfigDiscard DiscardPolicy { get; } = StreamConfigDiscard.Old;

    protected override long MaxMessagesPerSubject { get; } = 50_000;

    protected override TimeSpan MessageTtl { get; set; } = TimeSpan.FromDays(30);
}