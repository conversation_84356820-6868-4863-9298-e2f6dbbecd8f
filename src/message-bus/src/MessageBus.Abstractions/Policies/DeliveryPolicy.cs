using NATS.Client.JetStream.Models;

namespace Kukui.Infrastructure.MessageBus.Abstractions.Policies;

public abstract class DeliveryPolicy
{
    protected DeliveryPolicy(ConsumerConfigDeliverPolicy policyType)
    {
        Policy = policyType;
    }

    public ConsumerConfigDeliverPolicy Policy { get; set; }

    public ulong StartSequence { get; protected set; }
    public DateTimeOffset StartTime { get; protected set; }
}