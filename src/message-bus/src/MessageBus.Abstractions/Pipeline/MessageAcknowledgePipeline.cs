namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;
using Microsoft.Extensions.Logging;

internal class MessageAcknowledgePipeline<TContext, TEvent> : IPipeline<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class, IEvent
{
    private readonly IMessageAcknowledgeHandler<TContext, TEvent> _messageAcknowledgeProcessor;
    private readonly ILogger<MessageAcknowledgePipeline<TContext, TEvent>> _logger;

    public MessageAcknowledgePipeline(
        IMessageAcknowledgeHandler<TContext, TEvent> messageAcknowledgeProcessor,
        ILogger<MessageAcknowledgePipeline<TContext, TEvent>> logger)
    {
        _messageAcknowledgeProcessor = messageAcknowledgeProcessor;
        _logger = logger;
    }

    public async Task ExecuteStepAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug(
            "Start executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
            nameof(MessageAcknowledgePipeline<TContext, TEvent>),
            context.EventType.Name,
            context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id);

        await _messageAcknowledgeProcessor.ExecuteAsync(context, next, cancellationToken);

        _logger.LogDebug(
            "Finish executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
            nameof(MessageAcknowledgePipeline<TContext, TEvent>),
            context.EventType.Name,
            context.IsBatchContext ? ((Batch<TEvent>) context.Message).Id : ((IEvent) context.Message).Id);
    }
}