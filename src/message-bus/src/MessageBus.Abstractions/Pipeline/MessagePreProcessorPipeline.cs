namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;
using Microsoft.Extensions.Logging;

internal class MessagePreProcessorPipeline<TContext, TEvent> : IPipeline<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class, IEvent
{
    private readonly IEnumerable<IMessagePreProcessor<TContext>> _messageProcessors;
    private readonly ILogger<MessagePreProcessorPipeline<TContext, TEvent>> _logger;

    public MessagePreProcessorPipeline(
        IEnumerable<IMessagePreProcessor<TContext>> messageProcessors,
        ILogger<MessagePreProcessorPipeline<TContext, TEvent>> logger)
    {
        _messageProcessors = messageProcessors;
        _logger = logger;
    }

    public async Task ExecuteStepAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        foreach (var processor in _messageProcessors)
        {
            await processor.ExecuteAsync(context, cancellationToken);
        }

        await next(cancellationToken);
    }
}