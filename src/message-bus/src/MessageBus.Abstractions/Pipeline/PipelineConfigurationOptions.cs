namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Microsoft.Extensions.DependencyInjection;

internal class PipelineConfigurationOptions
{
    private readonly IServiceCollection _services;

    public PipelineConfigurationOptions(IServiceCollection services)
    {
        _services = services;
    }

    internal PipelineConfigurationOptions AddRetryProcessor(Type processorType)
    {
        _services.Add(new ServiceDescriptor(typeof(IMessageRetryPolicy<,>), processorType, ServiceLifetime.Transient));

        return this;
    }

    internal PipelineConfigurationOptions AddMessageFilter(Type processorType)
    {
        _services.Add(new ServiceDescriptor(typeof(IMessageFilter<>), processorType, ServiceLifetime.Transient));

        return this;
    }

    internal PipelineConfigurationOptions AddMessageScheduler(Type schedulerType)
    {
        _services.Add(new ServiceDescriptor(typeof(IMessageScheduler<>), schedulerType, ServiceLifetime.Transient));

        return this;
    }

    internal PipelineConfigurationOptions AddMessagePreProcessor(Type postProcessorType)
    {
        _services.Add(
            new ServiceDescriptor(typeof(IMessagePreProcessor<>), postProcessorType, ServiceLifetime.Transient));

        return this;
    }

    internal PipelineConfigurationOptions AddMessagePostProcessor(Type postProcessorType)
    {
        _services.Add(
            new ServiceDescriptor(typeof(IMessagePostProcessor<>), postProcessorType, ServiceLifetime.Transient));

        return this;
    }

    internal PipelineConfigurationOptions AddMessageExceptionHandler(Type postProcessorType)
    {
        _services.Add(
            new ServiceDescriptor(typeof(IMessageExceptionProcessor<,>), postProcessorType, ServiceLifetime.Transient));

        return this;
    }

    internal PipelineConfigurationOptions AddMessageAcknowledgeHandler(Type postProcessorType)
    {
        _services.Add(
            new ServiceDescriptor(typeof(IMessageAcknowledgeHandler<,>), postProcessorType, ServiceLifetime.Transient));

        return this;
    }
}