namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;

public delegate Task MessageRequestDelegate<in TContext>(CancellationToken t = default)
    where TContext : ConsumeContext;

internal interface IPipeline<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class, IEvent
{
    Task ExecuteStepAsync(TContext context, MessageRequestDelegate<TContext> next, CancellationToken cancellationToken);
}