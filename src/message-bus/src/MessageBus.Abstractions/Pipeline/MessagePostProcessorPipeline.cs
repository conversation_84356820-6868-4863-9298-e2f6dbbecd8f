namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;
using Microsoft.Extensions.Logging;

internal class MessagePostProcessorPipeline<TContext, TEvent> : IPipeline<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class, IEvent
{
    private readonly IEnumerable<IMessagePostProcessor<TContext>> _messageProcessors;
    private readonly ILogger<MessagePostProcessorPipeline<TContext, TEvent>> _logger;

    public MessagePostProcessorPipeline(
        IEnumerable<IMessagePostProcessor<TContext>> messageProcessors,
        ILogger<MessagePostProcessorPipeline<TContext, TEvent>> logger)
    {
        _messageProcessors = messageProcessors;
        _logger = logger;
    }

    public async Task ExecuteStepAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        await next(cancellationToken);

        foreach (var processor in _messageProcessors)
        {
            await processor.ExecuteAsync(context, cancellationToken);
        }
    }
}