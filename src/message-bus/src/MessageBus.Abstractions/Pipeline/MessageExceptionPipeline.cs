namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;

internal class MessageExceptionPipeline<TContext, TEvent> : IPipeline<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class, IEvent
{
    private readonly IMessageExceptionProcessor<TContext, TEvent> _messageExceptionProcessor;

    public MessageExceptionPipeline(IMessageExceptionProcessor<TContext, TEvent> messageExceptionProcessor)
    {
        _messageExceptionProcessor = messageExceptionProcessor;
    }

    public async Task ExecuteStepAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        await _messageExceptionProcessor.ExecuteAsync(context, next, cancellationToken);
    }
}