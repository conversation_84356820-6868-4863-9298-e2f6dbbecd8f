namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;

internal class MessageRetryPipeline<TContext, TEvent> : IPipeline<TContext, TEvent>
    where TContext : ConsumeContext
    where TEvent : class, IEvent
{
    private readonly IMessageRetryPolicy<TContext, TEvent> _messageRetryPolicy;

    public MessageRetryPipeline(IMessageRetryPolicy<TContext, TEvent> messageRetryPolicy)
    {
        _messageRetryPolicy = messageRetryPolicy;
    }


    public async Task ExecuteStepAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken) =>
        await _messageRetryPolicy.ExecuteAsync(context, next, cancellationToken);
}