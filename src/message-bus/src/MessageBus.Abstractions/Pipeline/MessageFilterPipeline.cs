namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;
using Microsoft.Extensions.Logging;

internal class MessageFilterPipeline<TContext, TEvent> : IPipeline<TContext, TEvent>
    where TContext : ConsumeContext<TEvent>
    where TEvent : class, IEvent
{
    private readonly IEnumerable<IMessageFilter<TEvent>> _filters;
    private readonly ILogger<MessageFilterPipeline<TContext, TEvent>> _logger;

    public MessageFilterPipeline(
        IEnumerable<IMessageFilter<TEvent>> filters,
        ILogger<MessageFilterPipeline<TContext, TEvent>> logger)
    {
        _filters = filters;
        _logger = logger;
    }

    public async Task ExecuteStepAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        foreach (var filter in _filters)
        {
            _logger.LogDebug(
                "Message filter {MessageFilter} start processing {EventType} message with Id {MessageId}",
                filter.GetType().Name,
                typeof(TEvent).Name,
                context.Message.Id);
            var approved = await filter.FilterAsync(context, cancellationToken);

            if (approved) continue;

            _logger.LogDebug(
                "Message filter {MessageFilter} skip {EventType} message with Id {MessageId}",
                filter.GetType().Name,
                typeof(TEvent).Name,
                context.Message.Id);

            return;
        }

        await next(cancellationToken);
    }
}