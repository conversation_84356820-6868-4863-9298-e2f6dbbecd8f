namespace Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;

using Consumers;
using Events;
using Microsoft.Extensions.Logging;

public class MessageSchedulerPipeline<TContext, TEvent> : IPipeline<TContext, TEvent>
    where TContext : ConsumeContext<TEvent>
    where TEvent : class, IEvent
{
    private readonly IMessageScheduler<TEvent> _messageScheduler;
    private readonly ILogger<MessageSchedulerPipeline<TContext, TEvent>> _logger;

    public MessageSchedulerPipeline(
        IMessageScheduler<TEvent> messageScheduler,
        ILogger<MessageSchedulerPipeline<TContext, TEvent>> logger)
    {
        _messageScheduler = messageScheduler;
        _logger = logger;
    }

    public async Task ExecuteStepAsync(
        TContext context,
        MessageRequestDelegate<TContext> next,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug(
            "Start executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
            nameof(MessageSchedulerPipeline<TContext, TEvent>),
            context.EventType.Name,
            context.Message.Id);

        if (await _messageScheduler.ScheduleAsync(context, cancellationToken))
        {
            return;
        }

        await next(cancellationToken);

        _logger.LogDebug(
            "Finish executing {MessagePipelineStep} for {EventType} with Id {MessageId}",
            nameof(MessageSchedulerPipeline<TContext, TEvent>),
            context.EventType.Name,
            context.Message.Id);
    }
}