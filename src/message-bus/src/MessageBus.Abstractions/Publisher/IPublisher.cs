namespace Kukui.Infrastructure.MessageBus.Abstractions.Publisher;

using Events;
using NATS.Client.Core;

public interface IPublisher
{
    Task PublishAsync(IEvent message, CancellationToken cancellationToken);

    Task PublishAsync(IEvent message, NatsHeaders? headers = null, CancellationToken cancellationToken = default);

    Task ScheduleAsync(IEvent message, DateTimeOffset scheduleTime, CancellationToken cancellationToken);

    Task RePublishAsync(IEvent message, CancellationToken cancellationToken);

    Task RePublishAsync(IEvent message, NatsHeaders? headers = null, CancellationToken cancellationToken = default);
}

public interface IPublisher<in TMessage> : IPublisher
    where TMessage : IEvent
{
    Task PublishAsync(TMessage message, CancellationToken cancellationToken);

    Task PublishAsync(TMessage message, NatsHeaders headers, CancellationToken cancellationToken);

    Task ScheduleAsync(TMessage message, DateTimeOffset scheduleTime, CancellationToken cancellationToken);

    Task RePublishAsync(TMessage message, CancellationToken cancellationToken);

    Task RePublishAsync(TMessage message, NatsHeaders? headers = null, CancellationToken cancellationToken = default);
}