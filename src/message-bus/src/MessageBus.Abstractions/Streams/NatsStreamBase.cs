namespace Kukui.Infrastructure.MessageBus.Abstractions.Streams;

using Extensions;
using NATS.Client.JetStream.Models;

/// <summary>
/// Default stream, bound to the limit policies defined.Consumed messages are not deleted once consumed. Support multiple consumers.
/// Once stream limits are reached any new messages will be discarded.
/// </summary>
public abstract class NatsStreamBase : IEquatable<NatsStreamBase>
{
    protected NatsStreamBase(string? name = null, StreamConfigRetention retentionPolicy = StreamConfigRetention.Limits)
    {
        Name = GetStreamFormattedName(name);
        Retention = retentionPolicy;
    }

    public string Name { get; protected set; }

    protected virtual string Subject => $"{Name}.>";

    protected virtual string Description { get; set; } = default!;

    private StreamConfigStorage Storage { get; } = StreamConfigStorage.File;

    private bool AllowRollUps { get; } = false;

    private StreamConfigRetention Retention { get; }

    private TimeSpan DeDuplicationWindow { get; } = TimeSpan.FromDays(1);

    protected virtual StreamConfigDiscard DiscardPolicy { get; } = StreamConfigDiscard.New;

    protected virtual bool DisableMessageDelete { get; set; }

    protected virtual bool DisableMessagePurge { get; set; }

    protected virtual Republish? RepublishConfig { get; set; } = default!;

    protected virtual Dictionary<string, string>? MetaData { get; set; } = default!;

    protected virtual SubjectTransform? SubjectTransformConfig { get; set; } = default!;

    public int Replicas { get; internal set; } = 3;

    #region LimitsPolicy

    private int MaxMessageSize { get; } = 256 * 1024;

    protected virtual long MaxNumberOfMessages { get; set; } = default;

    protected virtual long MaxNumberOfBytes { get; set; } = -1;

    protected virtual TimeSpan MessageTtl { get; set; } = default;

    protected virtual long MaxMessagesPerSubject { get; } = default;

    protected virtual int MaxConsumers { get; } = default;

    #endregion

    public static implicit operator StreamConfig(NatsStreamBase natsStream)
    {
        return new StreamConfig(natsStream.Name, [natsStream.Subject])
        {
            Description = natsStream.Description,
            Storage = natsStream.Storage,
            AllowRollupHdrs = natsStream.AllowRollUps,
            MaxMsgs = natsStream.MaxNumberOfMessages,
            MaxBytes = natsStream.MaxNumberOfBytes,
            MaxAge = natsStream.MessageTtl,
            MaxMsgsPerSubject = natsStream.MaxMessagesPerSubject,
            DenyDelete = natsStream.DisableMessageDelete,
            DenyPurge = natsStream.DisableMessagePurge,
            MaxMsgSize = natsStream.MaxMessageSize,
            MaxConsumers = natsStream.MaxConsumers,
            Retention = natsStream.Retention,
            Discard = natsStream.DiscardPolicy,
            DuplicateWindow = natsStream.DeDuplicationWindow,
            NumReplicas = natsStream.Replicas,
            Republish = natsStream.RepublishConfig!,
            SubjectTransform = natsStream.SubjectTransformConfig!,
            Metadata = natsStream.MetaData!,
            Compression = StreamConfigCompression.S2
        };
    }

    private string GetStreamFormattedName(string? name)
    {
        return name ?? GetType().Name.Replace("Stream", "").UseKebabCaseFormatting();
    }

    public bool Equals(NatsStreamBase? other)
    {
        if (ReferenceEquals(null, other))
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return Name == other.Name;
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(null, obj))
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        return obj.GetType() == GetType() && Equals((NatsStreamBase) obj);
    }

    public override int GetHashCode()
    {
        return Name.GetHashCode();
    }
}