namespace Kukui.Infrastructure.MessageBus.Abstractions.Consumers;

using System.Collections;

public class Batch<T> : IReadOnlyCollection<T>
{
    internal readonly ICollection<T> Messages;

    public Guid Id { get; }

    public Batch(ICollection<T> messages)
    {
        Messages = messages;
        Count = messages.Count;
        Id = Guid.NewGuid();
    }

    internal Batch(int count) : this(new List<T>(count))
    {
    }

    public IEnumerator<T> GetEnumerator() => Messages.GetEnumerator();

    IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();

    public int Count { get; }
}