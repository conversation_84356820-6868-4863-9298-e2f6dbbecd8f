namespace Kukui.Infrastructure.MessageBus.Abstractions.Consumers;

using Events;
using Exceptions;
using NATS.Client.Core;
using NATS.Client.JetStream;
using NATS.Client.JetStream.Models;

public class ConsumeContext<TMessage> : ConsumeContext
    where TMessage : class
{
    public ConsumeContext(TMessage message, NatsHeaders? headers = null, NatsJSMsgMetadata? metadata = null) : this(
        message,
        string.Empty,
        null!,
        headers,
        metadata)
    {
        Message = message;
    }

    internal ConsumeContext(
        TMessage message,
        string subject,
        ConsumerInfo consumer,
        NatsHeaders? headers = null,
        NatsJSMsgMetadata? metadata = null) : base(message, subject, consumer, headers, metadata)
    {
        Message = message;
    }

    public new TMessage Message { get; }
}

public class ConsumeContext
{
    private readonly Dictionary<Type, object> _items = new();

    public ConsumeContext(
        object message,
        string subject,
        ConsumerInfo consumer,
        NatsHeaders? headers = null,
        NatsJSMsgMetadata? metadata = null)
    {
        Message = message;
        Consumer = consumer;
        Subject = subject;
        EventType = message is IEvent ? message.GetType() : message.GetType().GetGenericArguments()[0];
        Headers = headers ?? new NatsHeaders();
        Metadata = metadata ?? new NatsJSMsgMetadata();
    }

    public object Message { get; }

    public ConsumerInfo Consumer { get; }

    public string Subject { get; }

    public NatsHeaders? Headers { get; }

    public NatsJSMsgMetadata? Metadata { get; }

    internal Type EventType { get; }

    internal bool IsBatchContext => Message is not IEvent;

    internal NatsJSMsg<NatsMemoryOwner<byte>> MessagePayload { get; set; }


    public void AddContextItem<T>(Func<T> factory)
    {
        _items[typeof(T)] = factory() ?? throw new ArgumentNullException(nameof(factory));
    }

    public T GetContextItem<T>()
    {
        if (!_items.ContainsKey(typeof(T))) throw new ContextItemNotFoundException(typeof(T));

        return (T) _items[typeof(T)];
    }
}