namespace Kukui.Infrastructure.MessageBus.Abstractions.Events;

public class TypeInfo
{
    public TypeInfo(Type eventType)
    {
        FullName = eventType.FullName!;
        AssemblyName = eventType.Assembly.GetName()
            .Name!;
    }

    public string FullName { get; }

    public string AssemblyName { get; }

    public override string ToString()
    {
        return $"{FullName}, {AssemblyName}";
    }
}