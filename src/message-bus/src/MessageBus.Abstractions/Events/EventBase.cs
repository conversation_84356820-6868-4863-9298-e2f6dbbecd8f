namespace Kukui.Infrastructure.MessageBus.Abstractions.Events;

using System.Text.Json.Serialization;
using Streams;

public abstract class EventBase<TStream> : IEvent<TStream>
    where TStream : NatsStreamBase, new()
{
    protected EventBase(string id)
    {
        Id = id;
        var eventType = GetType();
        Metadata = new EventMetaData<TStream>(eventType.Name);
        TypeInfo = new TypeInfo(eventType).ToString();
    }

    protected EventBase(Guid? id = null)
    {
        var eventType = GetType();
        Metadata = new EventMetaData<TStream>(eventType.Name);
        TypeInfo = new TypeInfo(eventType).ToString();
        Id = (id ?? Metadata.EventId).ToString();
    }

    public string Id { get; set; }

    public EventMetadata Metadata { get; set; }

    [JsonPropertyName("$type")] public string TypeInfo { get; set; }
}