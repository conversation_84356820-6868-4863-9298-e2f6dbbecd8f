namespace Kukui.Infrastructure.MessageBus.Abstractions.Events;

using System.Text.Json.Serialization;
using Streams;

public class EventMetaData<TStream> : EventMetadata
    where TStream : NatsStreamBase
{
    [JsonConstructor]
    public EventMetaData(string eventTypeName)
    {
        EventTypeName = eventTypeName;
    }

    public string EventTypeName { get; set; }
}

public class EventMetadata
{
    internal EventMetadata()
    {
        EventId = Guid.NewGuid();
        EventTime = DateTimeOffset.UtcNow;
        IdempotenceKey = EventId.ToString();
    }

    [JsonConstructor]
    private EventMetadata(Guid eventId, string idempotenceKey, DateTimeOffset eventTime)
    {
        EventId = eventId;
        EventTime = eventTime;
        IdempotenceKey = idempotenceKey;
    }

    public Guid EventId { get; }

    public string IdempotenceKey { get; }

    public string Subject { get; set; }

    public EventTopology Topology { get; set; }

    public DateTimeOffset EventTime { get; }
}