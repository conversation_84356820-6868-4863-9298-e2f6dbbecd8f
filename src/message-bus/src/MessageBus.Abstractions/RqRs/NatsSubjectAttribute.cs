namespace Kukui.Infrastructure.MessageBus.Abstractions.RqRs;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Interface, Inherited = false)]
public class NatsSubjectAttribute : Attribute
{
    public NatsSubjectAttribute(string subjectTemplate)
    {
        SubjectTemplate = subjectTemplate ?? throw new ArgumentNullException(nameof(subjectTemplate));
    }

    public string SubjectTemplate { get; }
}