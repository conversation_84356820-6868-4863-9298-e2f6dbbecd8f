namespace Kukui.Infrastructure.MessageBus.Abstractions.RqRs;

/// <summary>
///     marker interface for request messages with no empty response
/// </summary>
public interface INatsRequest
{
}

/// <summary>
///     marker interface for request messages with response
/// </summary>
/// <typeparam name="TResponse">Request Response type</typeparam>
public interface INatsRequest<TResponse> : INatsRequest
{
}