namespace Kukui.Infrastructure.MessageBus.Abstractions.RqRs;

/// <summary>
///     publish a message and wait for a response
/// </summary>
/// <typeparam name="TRequest"></typeparam>
/// <typeparam name="TResponse"></typeparam>
public interface INatsRequestClient<in TRequest, TResponse>
    where TRequest : INatsRequest<TResponse>
{
    ValueTask<TResponse?> SendRequestAsync(TRequest message, CancellationToken cancellationToken);

    IAsyncEnumerable<TResponse> SendRequestWithResponsesAsync(TRequest message, CancellationToken cancellationToken);
}

public interface INatsRequestClient<in TRequest> : INatsRequestClient<TRequest, Unit>
    where TRequest : INatsRequest<Unit>
{
}