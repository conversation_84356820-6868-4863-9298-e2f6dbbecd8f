using Kukui.Infrastructure.MessageBus.Abstractions.Streams;
using NATS.Client.JetStream.Models;

namespace Kukui.Infrastructure.MessageBus.Abstractions;

/// <summary>
/// Similar to <see cref="WorkQueue"/> but with the typical behavior of a FIFO queue.
/// Only one consumer can be created for an interest-consumers queue.
/// Message is being deleted once consumed.
/// </summary>
public abstract class InterestConsumersQueue : NatsStreamBase
{
    protected InterestConsumersQueue(string? name = null)
        : base(name, StreamConfigRetention.Interest)
    {
    }
}