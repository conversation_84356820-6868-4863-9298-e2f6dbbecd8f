version: "3.8"
services:
  nats-1:
    image: nats
    hostname: nats-main
    ports:
      - "4222:4222"
      - "8222:8222"
    command: "-c /etc/nats/server.conf --name nats-main -p 4222"
    volumes:
      - ./server.conf:/etc/nats/server.conf
      - ./jetstream/data-1:/etc/nats/js
  worker-1:
    image: nats
    hostname: worker-1
    ports:
      - "4223:4222"
    command: "-c /etc/nats/server.conf --name worker-1 -p 4222"
    volumes:
      - ./server.conf:/etc/nats/server.conf
      - ./jetstream/data-2:/etc/nats/js
  worker-2:
    image: nats
    hostname: worker-2
    ports:
      - "4224:4222"
    command: "-c /etc/nats/server.conf --name worker-2 -p 4222"
    volumes:
      - ./server.conf:/etc/nats/server.conf
      - ./jetstream/data-3:/etc/nats/js