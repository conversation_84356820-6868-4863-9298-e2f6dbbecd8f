namespace Nats.Events.Shared;

using Kukui.Infrastructure.MessageBus.Abstractions;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;
using Kukui.Infrastructure.MessageBus.Abstractions.Streams;

public class RetentionEmailStream : NatsStreamBase
{
}

public class RetentionStream : NatsStreamBase
{
}

public class CoreStream : NatsStreamBase
{
}

public class CoreTestStream : NatsStreamBase
{
}

public class EmailRetentionBlastQueue : InterestConsumersQueue
{
}

public class LocationBlastCreated : EventBase<EmailRetentionBlastQueue>
{
    public LocationBlastCreated(string id) : base(id)
    {
    }

    public string Name { get; set; }

    public ExternalIdentification ExternalIdentification { get; set; }
}

public class ExternalIdentification
{
    public int ClientId { get; set; }

    public int LocationId { get; set; }
}

public class RetentionEmailRecipientCreated : EventBase<RetentionEmailStream>
{
    public static int Counter { get; set; }

    public RetentionEmailRecipientCreated(string id) : base(id)
    {
        Counter++;
    }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string EmailAddress { get; set; }
}