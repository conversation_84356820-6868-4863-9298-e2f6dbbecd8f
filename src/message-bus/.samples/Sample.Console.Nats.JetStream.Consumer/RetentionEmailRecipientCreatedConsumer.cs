namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Kukui.Infrastructure.MessageBus.Abstractions.Extensions;
using Microsoft.Extensions.Logging;

//[ConsumerName("EmailRecipientCreatedConsumer")]
public class RetentionEmailRecipientCreatedConsumer : IEventConsumer<Batch<RetentionEmailRecipientCreated>>
{
    private readonly ILogger<RetentionEmailRecipientCreatedConsumer> _logger;

    public RetentionEmailRecipientCreatedConsumer(ILogger<RetentionEmailRecipientCreatedConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(
        ConsumeContext<Batch<RetentionEmailRecipientCreated>> context,
        CancellationToken cancellationToken = default)
    {
        foreach (var (message, index) in context.Message.WithIndex())
        {
            if (index == 30)
            {
                throw new InvalidOperationException("Simulated exception");
            }

            _logger.LogInformation(
                "Consumed {EventType} event with {MessageId}",
                nameof(RetentionEmailRecipientCreated),
                message.Id);
        }

        return Task.CompletedTask;
    }
}