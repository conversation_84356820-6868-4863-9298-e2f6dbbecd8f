namespace Sample.Console.Nats.JetStream.Consumer;

using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;
using Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;
using Microsoft.Extensions.Logging;

public class GlobalLogFilter<TEvent> : IMessageFilter<TEvent>
    where TEvent : class, IEvent
{
    private readonly ILogger<GlobalLogFilter<TEvent>> _logger;

    public GlobalLogFilter(ILogger<GlobalLogFilter<TEvent>> logger)
    {
        _logger = logger;
    }

    public Task<bool> FilterAsync(ConsumeContext<TEvent> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Global filter for {EventType} ", typeof(TEvent).Name);

        return Task.FromResult(true);
    }
}