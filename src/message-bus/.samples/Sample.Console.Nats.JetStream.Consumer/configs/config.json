{"DisplayName": "Nats JetStream Consumer", "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": "Information", "Override": {"NATS.Client": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}}, "Server": {"DataBase": {"ConnectionString": "secret", "DefaultPageSize": 100, "DefaultKeyspace": "leads", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}, "Metrics": {"Enabled": true, "DatadogConfig": {"BaseUri": "https://intake.logs.datadoghq.com", "ApiKey": "secret"}}}, "MessageService": {"Enabled": true, "ConsumerPrefix": "Retention Server", "Url": "nats://host.docker.internal:4222", "Username": "nats-user", "Password": "password"}, "Vault": {"Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"Name": "database", "ConfigurationSectionName": "server:Database", "Keys": [{"Source": "core-scylladb", "Target": "ConnectionString"}]}, {"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}