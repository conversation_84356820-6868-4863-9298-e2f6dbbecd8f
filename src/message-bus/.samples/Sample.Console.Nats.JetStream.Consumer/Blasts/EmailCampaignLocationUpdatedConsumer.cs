namespace Sample.Console.Nats.JetStream.Consumer.Blasts;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class EmailCampaignLocationUpdatedConsumer : IEventConsumer<LocationUpdated>
{
    private readonly ILogger<EmailCampaignLocationUpdatedConsumer> _logger;

    public EmailCampaignLocationUpdatedConsumer(ILogger<EmailCampaignLocationUpdatedConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<LocationUpdated> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "{Consumer} consumed event {EventType} ",
            nameof(EmailCampaignLocationUpdatedConsumer),
            nameof(LocationUpdated));

        return Task.CompletedTask;
    }
}