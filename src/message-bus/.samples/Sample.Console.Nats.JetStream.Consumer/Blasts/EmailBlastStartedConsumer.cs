namespace Sample.Console.Nats.JetStream.Consumer.Blasts;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class EmailBlastStartedConsumer : IEventConsumer<EmailCampaignStartedEvent>
{
    private readonly ILogger _logger;

    public EmailBlastStartedConsumer(ILogger<EmailBlastStartedConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(
        ConsumeContext<EmailCampaignStartedEvent> context,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Consumed {EventType} event with {@Payload}",
            nameof(EmailCampaignStartedEvent),
            context.Message);
        return Task.CompletedTask;
    }
}