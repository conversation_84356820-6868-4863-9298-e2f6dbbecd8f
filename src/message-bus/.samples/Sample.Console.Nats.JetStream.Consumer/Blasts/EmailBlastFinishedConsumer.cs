namespace Sample.Console.Nats.JetStream.Consumer.Blasts;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class EmailBlastFinishedConsumer : IEventConsumer<EmailCampaignFinishedEvent>
{
    private readonly ILogger _logger;

    public EmailBlastFinishedConsumer(ILogger<EmailBlastFinishedConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(
        ConsumeContext<EmailCampaignFinishedEvent> context,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Consumed {EventType} event with {@Payload}",
            nameof(EmailCampaignFinishedEvent),
            context.Message);
        return Task.CompletedTask;
    }
}