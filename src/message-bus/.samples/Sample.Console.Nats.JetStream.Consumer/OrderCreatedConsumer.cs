namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class OrderCreatedConsumer : IEventConsumer<OrderBatchCreated>
{
    private readonly ILogger<OrderCreatedConsumer> _logger;

    public OrderCreatedConsumer(ILogger<OrderCreatedConsumer> logger)
    {
        _logger = logger;
    }

    public async Task ConsumeAsync(ConsumeContext<OrderBatchCreated> context, CancellationToken cancellationToken)
    {
        if (context.Message.BatchId == Guid.NewGuid()) throw new InvalidOperationException("Invalid client id");

        _logger.LogInformation(
            "{Consumer} consumed event {EventType} with {MessageId}",
            nameof(OrderCreatedConsumer),
            nameof(OrderBatchCreated),
            context.Message.Id);
        await Task.Delay(TimeSpan.FromSeconds(3), cancellationToken);
    }
}