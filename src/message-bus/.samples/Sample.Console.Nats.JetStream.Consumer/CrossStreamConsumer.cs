namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class CrossStreamConsumer : IEventConsumer<LocationUpdated>, IEventConsumer<OrderCreated>
{
    private readonly ILogger<CrossStreamConsumer> _logger;

    public CrossStreamConsumer(ILogger<CrossStreamConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<LocationUpdated> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "{Consumer} consumed event {EventType} ",
            nameof(CrossStreamConsumer),
            nameof(LocationUpdated));

        return Task.CompletedTask;
    }

    public Task ConsumeAsync(ConsumeContext<OrderCreated> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "{Consumer} consumed event {EventType} ",
            nameof(CrossStreamConsumer),
            nameof(OrderCreated));

        return Task.CompletedTask;
    }
}