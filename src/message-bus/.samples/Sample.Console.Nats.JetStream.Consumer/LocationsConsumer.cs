namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class LocationsConsumer : IEventConsumer<LocationCreated>
{
    private readonly ILogger<LocationsConsumer> _logger;

    public LocationsConsumer(ILogger<LocationsConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<LocationCreated> context, CancellationToken cancellationToken)
    {
        var item = context.GetContextItem<Dictionary<int, object>>();

        if (context.Message.ClientId == 78) throw new InvalidOperationException("Invalid client id");

        _logger.LogInformation(
            "{Consumer} consumed event {EventType} ",
            nameof(LocationsConsumer),
            nameof(LocationCreated));

        return Task.CompletedTask;
    }

    public Task ConsumeAsync(ConsumeContext<LocationUpdated> context, CancellationToken cancellationToken)
    {
        if (context.Message.ClientId == 78) throw new InvalidOperationException("Invalid client id");

        _logger.LogInformation(
            "{Consumer} consumed event {EventType} ",
            nameof(LocationsConsumer),
            nameof(LocationUpdated));

        return Task.CompletedTask;
    }
}