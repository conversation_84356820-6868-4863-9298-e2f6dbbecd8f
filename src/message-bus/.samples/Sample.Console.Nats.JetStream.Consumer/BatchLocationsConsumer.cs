namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class BatchLocationsConsumer : IEventConsumer<Batch<LocationCreated>>, IEventConsumer<Batch<LocationUpdated>>
{
    private readonly ILogger<BatchLocationsConsumer> _logger;

    public BatchLocationsConsumer(ILogger<BatchLocationsConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<Batch<LocationCreated>> context, CancellationToken cancellationToken)
    {
        foreach (var message in context.Message)
        {
            if (message.ClientId == 78) throw new InvalidOperationException("Invalid client id");

            _logger.LogInformation(
                "{Consumer} consumed event {EventType} ",
                nameof(BatchLocationsConsumer),
                nameof(LocationCreated));
        }

        return Task.CompletedTask;
    }

    public Task ConsumeAsync(ConsumeContext<Batch<LocationUpdated>> context, CancellationToken cancellationToken)
    {
        foreach (var message in context.Message)
        {
            if (message.ClientId == 78) throw new InvalidOperationException("Invalid client id");

            _logger.LogInformation(
                "{Consumer} consumed event {EventType} ",
                nameof(BatchLocationsConsumer),
                nameof(LocationUpdated));
        }

        return Task.CompletedTask;
    }
}