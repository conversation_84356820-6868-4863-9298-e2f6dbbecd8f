<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Server\Kukui.Hosting.Server.csproj" />
    <ProjectReference Include="..\..\src\MessageBus.Nats.JetStream\MessageBus.Nats.JetStream.csproj" />
    <ProjectReference Include="..\Nats.Events.Shared\Nats.Events.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="configs\*" CopyToOutputDirectory="Always" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="EmailCampaignConsumer.cs" />
    <Compile Remove="EmailCampaignCreatedBatchConsumer.cs" />
    <Compile Remove="LocationBlastCreatedConsumer.cs" />
  </ItemGroup>

</Project>