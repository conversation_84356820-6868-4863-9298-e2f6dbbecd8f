namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Kukui.Infrastructure.MessageBus.Abstractions.Pipeline;
using Microsoft.Extensions.Logging;

public class LocationConsumerFilter : IMessageFilter<LocationCreated>, IMessageFilter<LocationUpdated>
{
    private readonly ILogger<LocationConsumerFilter> _logger;

    public LocationConsumerFilter(ILogger<LocationConsumerFilter> logger)
    {
        _logger = logger;
    }

    public async Task<bool> FilterAsync(ConsumeContext<LocationCreated> context, CancellationToken cancellationToken)
    {
        context.AddContextItem(() => new Dictionary<int, object> { { 77, new object() } });
        return await FilterAsync(context.Message.ClientId, cancellationToken);
    }

    public Task<bool> FilterAsync(ConsumeContext<LocationUpdated> context, CancellationToken cancellationToken) =>
        FilterAsync(context.Message.ClientId, cancellationToken);

    private Task<bool> FilterAsync(int clientId, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "{MessageFilter} filter for {EventType} ",
            nameof(LocationConsumerFilter),
            nameof(LocationCreated));

        if (clientId == 77)
        {
            return Task.FromResult(false);
        }

        return Task.FromResult(true);
    }
}