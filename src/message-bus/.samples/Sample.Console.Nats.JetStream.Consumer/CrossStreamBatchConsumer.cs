namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class CrossStreamBatchConsumer : IEventConsumer<Batch<LocationUpdated>>, IEventConsumer<Batch<OrderCreated>>
{
    private readonly ILogger<CrossStreamBatchConsumer> _logger;

    public CrossStreamBatchConsumer(ILogger<CrossStreamBatchConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<Batch<LocationUpdated>> context, CancellationToken cancellationToken)
    {
        foreach (var msg in context.Message)
        {
            _logger.LogInformation(
                "{Consumer} consumed event {EventType} ",
                nameof(CrossStreamBatchConsumer),
                nameof(LocationUpdated));
        }

        return Task.CompletedTask;
    }

    public Task ConsumeAsync(ConsumeContext<Batch<OrderCreated>> context, CancellationToken cancellationToken)
    {
        foreach (var msg in context.Message)
        {
            _logger.LogInformation(
                "{Consumer} consumed event {EventType} ",
                nameof(CrossStreamBatchConsumer),
                nameof(OrderCreated));
        }

        return Task.CompletedTask;
    }
}