using Kukui.Infrastructure.Hosting.Server;
using Kukui.Infrastructure.MessageBus.Nats.JetStream;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NATS.Client.Core;
using Sample.Console.Nats.JetStream.Consumer;

await ServerHostRunner.RunApp(
    args,
    (services, config) =>
    {
        services.AddNatsJetStream(
            config,
            options =>
            {
                options.AddConsumerGroup(
                    "orders_group",
                    consumer => consumer.AddConsumer<OrderCreatedConsumer>()
                        // .AddConsumer<OrdersConsumer>()
                        .AddConsumer<OrderCompletedConsumer>()
                        .WithBatchConsumerConfiguration(
                            cfg =>
                            {
                                cfg.WithDeliveryByNewMessages();
                                cfg.MaxMessages = 5;
                            }));

                options.AddConsumer<LocationsConsumer>();
                //options.AddConsumer<CrossStreamConsumer>();
                //options.AddBatchConsumer<CrossStreamBatchConsumer>(opt => opt.MaxMessages = 5);
                //options.AddConsumer<LocationsConsumer>();
                // options.RegisterConsumersFromAssemblyContaining<Program>(
                //     consumers => consumers.WithDeliveryByNewMessages());
                //consumer.AddConsumer<EmailCampaignCreatedConsumer>();
                //consumer.AddConsumer<EmailCampaignUpdatedConsumer>();
                //options.RegisterConsumersFromAssemblyContaining<Program>();
                // consumer.AddConsumer<EmailCampaignCreatedBatchConsumer>(opt => opt.MaxMessages = 500);

                // options.AddConsumer<LocationsConsumer>();
                options.AddMessageFilter(typeof(GlobalLogFilter<>));
                options.AddMessageFilter<LocationConsumerFilter>();

                //options.RegisterConsumersFromAssemblyContaining<Program>();
                // options.AddBatchConsumer<LocationsConsumer>(
                //     cfg =>
                //     {
                //         cfg.WithDeliveryByNewMessages();
                //         cfg.MaxMessages = 10;
                //     });
                //options.AddBatchConsumer<BatchEmailBlastConsumer>(cfg => cfg.MaxMessages = 5);
                //options.AddBatchConsumer<OrderCratedBatchConsumer>(x => { x.MaxMessages = 2; });
                //options.AddBatchConsumer<OrdersConsumer>(opt => opt.MaxMessages = 2);
                // consumer.AddConsumer<EmailCampaignConsumer<EmailCampaignCreated>>();
                // consumer.AddConsumer<EmailCampaignConsumer<EmailCampaignUpdated>>();
            });
    },
    (provider, configuration) =>
    {
        var logger = provider.GetRequiredService<ILogger<Program>>();
        var natsConnection = provider.GetRequiredService<NatsConnection>();
        natsConnection.OnConnectingAsync += host =>
        {
            logger.LogInformation("Connecting to NATS");
            return ValueTask.FromResult(host);
        };

        natsConnection.ConnectionDisconnected += (sender, eventArgs) =>
        {
            logger.LogWarning("Disconnected from NATS");
            return ValueTask.CompletedTask;
        };
    });