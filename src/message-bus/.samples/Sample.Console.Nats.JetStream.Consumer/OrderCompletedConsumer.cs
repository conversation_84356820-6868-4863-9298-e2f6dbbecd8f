namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class OrderCompletedConsumer : IEventConsumer<OrderBatchCompleted>
{
    private readonly ILogger<OrderCompletedConsumer> _logger;

    public OrderCompletedConsumer(ILogger<OrderCompletedConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<OrderBatchCompleted> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "{Consumer} consumed event {EventType} with {MessageId}",
            nameof(OrderCompletedConsumer),
            nameof(OrderBatchCompleted),
            context.Message.Id);

        return Task.CompletedTask;
    }
}