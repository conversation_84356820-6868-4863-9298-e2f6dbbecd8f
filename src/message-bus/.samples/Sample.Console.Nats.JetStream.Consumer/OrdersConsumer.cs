namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class OrdersConsumer : IEventConsumer<Batch<OrderCreated>>
{
    private readonly ILogger<OrdersConsumer> _logger;

    public OrdersConsumer(ILogger<OrdersConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<Batch<OrderCreated>> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Received batch of {Count} messages {Messages}",
            context.Message.Count,
            context.Message.Select(x => x.Id));
        foreach (var message in context.Message)
        {
            if (context.Message.Any(x => x.Id == "598776aa-f535-447b-ab9f-b1e6dc267398"))
                throw new InvalidOperationException("Invalid client id");

            _logger.LogInformation(
                "{Consumer} consumed event {EventType} with {MessageId}",
                nameof(OrdersConsumer),
                nameof(OrderCreated),
                message.Id);
        }

        return Task.CompletedTask;
    }
}