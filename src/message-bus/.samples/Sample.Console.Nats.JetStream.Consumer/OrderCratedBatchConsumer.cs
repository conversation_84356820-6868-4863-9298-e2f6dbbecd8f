namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class OrderCratedBatchConsumer : IEventConsumer<Batch<OrderCreated>>
{
    private readonly ILogger<OrderCratedBatchConsumer> _logger;

    public OrderCratedBatchConsumer(ILogger<OrderCratedBatchConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(ConsumeContext<Batch<OrderCreated>> context, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Start processing batch of {EventType} events", nameof(OrderCreated));

        foreach (var message in context.Message)
        {
            if (message.Id == "598776aa-f535-447b-ab9f-b1e6dc267398")
            {
                throw new ArgumentException($"Failed to process message with id {message.Id}");
            }

            _logger.LogInformation("Consumed {EventType} event with {@Payload}", nameof(OrderCreated), message);
        }

        return Task.CompletedTask;
    }
}