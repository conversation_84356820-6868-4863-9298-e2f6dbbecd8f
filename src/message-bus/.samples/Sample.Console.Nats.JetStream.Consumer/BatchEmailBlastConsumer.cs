namespace Sample.Console.Nats.JetStream.Consumer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Microsoft.Extensions.Logging;

public class BatchEmailBlastConsumer : IEventConsumer<Batch<EmailCampaignCreated>>
{
    private readonly ILogger _logger;

    public BatchEmailBlastConsumer(ILogger<BatchEmailBlastConsumer> logger)
    {
        _logger = logger;
    }

    public Task ConsumeAsync(
        ConsumeContext<Batch<EmailCampaignCreated>> context,
        CancellationToken cancellationToken = default)
    {
        foreach (var message in context.Message)
            _logger.LogInformation("Consumed {EventType} event with {@Payload}", nameof(EmailCampaignCreated), message);

        return Task.CompletedTask;
    }
}