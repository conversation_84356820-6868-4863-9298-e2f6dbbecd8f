#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace Sample.WebApi.Nats.RqRs;

using Kukui.Infrastructure.MessageBus.Abstractions.RqRs;
using Kukui.Infrastructure.MessageBus.Nats.RqRs;
using OneOf;

[GenerateOneOf]
public partial class GetPhoneResponse : OneOfBase<PhoneResponse, NatsResponseError>;

public class GetPhoneRequest : INatsRequest<GetPhoneResponse>
{
    public GetPhoneRequest(string phoneNumber, string phoneType)
    {
        PhoneNumber = phoneNumber;
        PhoneType = phoneType;
    }

    public string PhoneNumber { get; set; }

    public string PhoneType { get; set; }
}

public class PhoneResponse
{
    public PhoneResponse(string customerName, string phoneNumber)
    {
        CustomerName = customerName;
        PhoneNumber = phoneNumber;
    }

    public string CustomerName { get; set; }

    public string PhoneNumber { get; set; }
}

public class GetLocationRequest : INatsRequest<GetLocationResponse>
{
    public GetLocationRequest(int locationId)
    {
        LocationId = locationId;
    }

    public int LocationId { get; }

    public int? Responses { get; set; }
}

[GenerateOneOf]
public partial class GetLocationResponse : OneOfBase<LocationData, LocationNotFound, OperationNotAllowed>;

public class LocationNotFound
{
    public string Description { get; set; }
}

public class OperationNotAllowed
{
    public string Description { get; set; }
}

public class LocationData
{
    public int LocationId { get; set; }

    public string Name { get; set; }

    public bool Enabled { get; set; } = true;
}