namespace Sample.WebApi.Nats.RqRs;

using Kukui.Infrastructure.MessageBus.Abstractions.RqRs;

public class GetLocationRequestHandler : INatsRequestHandler<GetLocationRequest, GetLocationResponse>
{
    public async ValueTask<GetLocationResponse> HandleRequestAsync(
        GetLocationRequest request,
        CancellationToken cancellationToken = default)
    {
        await Task.Delay(TimeSpan.Zero, cancellationToken);
        return new LocationData
            { LocationId = request.LocationId, Name = $"{request.LocationId} - Name", Enabled = true };
    }

    public class GetLocationRequestHandler2 : INatsRequestHandler<GetLocationRequest, GetLocationResponse>
    {
        public async ValueTask<GetLocationResponse> HandleRequestAsync(
            GetLocationRequest request,
            CancellationToken cancellationToken = default)
        {
            await Task.Delay(TimeSpan.Zero, cancellationToken);
            return new LocationData { LocationId = request.LocationId * 2, Name = $"{request.LocationId * 2} - Name" };
        }
    }
}