using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.Hosting.Web.Common;
using Kukui.Infrastructure.MessageBus.Nats.RqRs;

namespace Sample.WebApi.Nats.RqRs;

public class WebApiConfigurator : WebConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddNatsRqRs(configuration.ServerConfiguration,
            options => options.RegisterRequestHandlersFromAssemblyContaining<WebApiConfigurator>());
    }
}