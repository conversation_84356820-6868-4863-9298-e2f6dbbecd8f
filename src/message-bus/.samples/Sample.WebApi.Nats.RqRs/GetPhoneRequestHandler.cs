namespace Sample.WebApi.Nats.RqRs;

using Kukui.Infrastructure.MessageBus.Abstractions.RqRs;
using Kukui.Infrastructure.MessageBus.Nats.RqRs;

public class GetPhoneRequestHandler : INatsRequestHandler<GetPhoneRequest, GetPhoneResponse>
{
    private readonly ILogger _logger;

    public GetPhoneRequestHandler(ILogger<GetPhoneRequestHandler> logger)
    {
        _logger = logger;
    }

    public async ValueTask<GetPhoneResponse> HandleRequestAsync(
        GetPhoneRequest request,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Processing  {RequestType} request with {@Payload}", nameof(GetPhoneRequest), request);
        await Task.Delay(TimeSpan.Zero, cancellationToken);

        return request.PhoneType switch
        {
            "Home" => new NatsResponseError("Home phone not allowed"),
            _ => new PhoneResponse("John Doe", request.PhoneNumber)
        };
    }
}