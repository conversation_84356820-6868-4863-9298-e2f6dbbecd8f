using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.MessageBus.Abstractions.Publisher;
using Kukui.Infrastructure.MessageBus.Abstractions.RqRs;
using Microsoft.AspNetCore.Mvc;
using Sample.WebApi.Nats.RqRs;
using Sample.WebApi.Nats.RqRs.Notifications;
using GetLocationResponse = Sample.WebApi.Nats.RqRs.GetLocationResponse;
using GetPhoneResponse = Sample.WebApi.Nats.RqRs.GetPhoneResponse;

await WebHostRunner.RunApp<WebApiConfigurator>(
    args,
    (app, _) =>
    {
        app.MapPost(
            "api/rqrs/phones",
            async (
                [FromServices] INatsRequestClient<GetPhoneRequest, GetPhoneResponse> requestClient,
                [FromBody] GetPhoneRequest request) =>
            {
                var response = await requestClient.SendRequestAsync(request, default);
                return response?.Match(data => Results.Ok(data), err => Results.BadRequest(err));
            });

        app.MapGet(
            "api/rqrs/locations/{id:int}",
            async (
                [FromServices] INatsRequestClient<GetLocationRequest, GetLocationResponse> handler,
                [FromServices] ILogger<Program> logger,
                [FromRoute] int id,
                [FromQuery] int? responses) =>
            {
                var request = new GetLocationRequest(id);

                var data = new List<object>();
                await foreach (var response in handler.SendRequestWithResponsesAsync(request, default))
                {
                    data.Add(response.Value);
                }

                return Results.Ok(data);
            });

        app.MapPost(
            "api/rqrs/locations",
            async (
                [FromServices] INatsRequestClient<GetLocationRequest, GetLocationResponse> handler,
                [FromBody] GetLocationRequest request) =>
            {
                var response = await handler.SendRequestAsync(request, default);
                return response?.Match(
                    data => Results.Ok(data),
                    notFound => Results.NotFound(notFound.Description),
                    notAllowed => Results.BadRequest(notAllowed.Description));
            });

        app.MapPost(
            "api/rqrs/publish",
            async ([FromServices] INotificationPublisher publisher, [FromBody] LocationModified request) =>
            {
                await publisher.PublishAsync(request, default);
                return Results.Ok();
            });
    });