{"DisplayName": "Sample Nats Request Response", "Security": {"EnforceHttps": false, "AllowedHosts": "*", "AllowedCorsOrigins": ["https://localhost", "https://*.server.cluster", "https://*.kukui.com"], "Authentication": {"Enabled": false, "IdentityServerUrl": "https://docker-sandbox.server.clsuter:8100/auth"}}, "Kestrel": {"AddServerHeader": false, "Endpoints": {"HttpsDefaultCert": {"Url": "https://*:5501"}}}, "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Debug"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}}, "Server": {"DataBase": {"ConnectionString": "secret", "DefaultPageSize": 100, "DefaultKeyspace": "leads", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}, "Metrics": {"Enabled": false}}, "MessageService": {"Url": "nats://localhost", "Username": "nats-user", "Password": "secret"}, "Vault": {"Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"Name": "database", "ConfigurationSectionName": "server:Database", "Keys": [{"Source": "core-scylladb", "Target": "ConnectionString"}]}, {"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}