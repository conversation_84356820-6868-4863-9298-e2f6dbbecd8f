using System.Text.Json;
using System.Text.Json.Serialization;
using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.MessageBus.Abstractions;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;
using Kukui.Infrastructure.MessageBus.Abstractions.Publisher;
using Microsoft.AspNetCore.Mvc;
using NATS.Client.Core;
using NATS.Client.JetStream;
using NATS.Client.JetStream.Models;
using Nats.Events.Shared;
using Sample.WebApi.Nats.JetStream.Producer;

await WebHostRunner.RunApp<AppConfigurator>(
    args,
    (app, _) =>
    {
        app.MapPost(
            "api/queue",
            async ([FromServices] IPublisher<LocationBlastCreated> publisher, LocationBlastCreatedRequest request) =>
            {
                var @event = new LocationBlastCreated(Guid.NewGuid().ToString())
                    { Name = request.Name, ExternalIdentification = request.ExternalIdentification };

                await publisher.PublishAsync(@event);
            });
        app.MapGet(
            "api/events",
            ([FromQuery] string eventType) =>
            {
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase, Converters = { new OutboxJsonConverter() }
                };
                var eventTypes = AppDomain.CurrentDomain.GetAssemblies()
                    .SelectMany(x => x.GetTypes())
                    .Where(
                        type => type.IsAssignableTo(typeof(IEvent)) &&
                                type is { IsAbstract: false, IsInterface: false })
                    .ToDictionary(x => x.FullName!);

                if (!eventTypes.TryGetValue(eventType, out var @event)) return Results.NotFound(eventType);

                var instance = Activator.CreateInstance(@event, "123");
                return Results.Ok(instance);
            });
        app.MapPost(
            "api/js/raw",
            async ([FromServices] IServiceProvider serviceProvider, HttpRequest request) =>
            {
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                    Converters = { new OutboxJsonConverter(), new OutboxJsonResultConverter() }
                };

                var bodyStream = request.BodyReader.AsStream();
                var outboxEntries = JsonSerializer.Deserialize<IEnumerable<IEvent>>(bodyStream, jsonOptions) ?? [];

                foreach (var outboxEntry in outboxEntries)
                {
                    var publisher = (IPublisher?) serviceProvider.GetService(
                        typeof(IPublisher<>).MakeGenericType(outboxEntry.GetType()));
                    await publisher!.PublishAsync(outboxEntry);
                }
            });

        app.MapPost(
            "api/js/recipient/created/{startIndex:int}/{numbers:int}",
            async ([FromServices] IPublisher<RetentionEmailRecipientCreated> publisher, int startIndex, int numbers) =>
            {
                foreach (var num in Enumerable.Range(startIndex, numbers))
                {
                    var message = new RetentionEmailRecipientCreated(num.ToString())
                    {
                        FirstName = $"FirstName- {num}",
                        LastName = $"LastName{num}",
                        EmailAddress = $"first_name.last_name{num}@email.com"
                    };
                    await publisher.PublishAsync(message);
                }
            });

        app.MapPost(
            "api/js/batch/{startIndex:int}/{numbers:int}",
            async (
                [FromServices] IPublisher<EmailCampaignCreated> publisher,
                [FromRoute] int startIndex,
                int numbers) =>
            {
                foreach (var num in Enumerable.Range(startIndex, numbers))
                    await publisher.PublishAsync(new EmailCampaignCreated(num.ToString()) { LocationId = num * 200 });
            });
        app.MapPost(
            "api/orders/{startIndex:int}/{numbers:int}",
            async (
                [FromServices] IPublisher<OrderBatchCreated> createdPublisher,
                [FromServices] IPublisher<OrderBatchCompleted> completedPublisher,
                [FromServices] IPublisher<OrderCreated> orderCreatedPublisher,
                [FromRoute] int startIndex,
                int numbers) =>
            {
                var batchId = Guid.NewGuid();
                await createdPublisher.PublishAsync(new OrderBatchCreated(batchId, numbers));
                if (numbers > 0)
                {
                    foreach (var num in Enumerable.Range(startIndex, numbers))
                    {
                        var orderCreated = new OrderCreated(
                            num == 3 ? "598776aa-f535-447b-ab9f-b1e6dc267398" : Guid.NewGuid().ToString())
                        {
                            OrderNumber = num,
                            BatchId = batchId,
                            Total = Random.Shared.Next(50, 500),
                            OrderDate = DateTimeOffset.UtcNow
                        };

                        await orderCreatedPublisher.PublishAsync(orderCreated);
                    }
                }

                await completedPublisher.PublishAsync(new OrderBatchCompleted(batchId));
            });

        app.MapGet(
            "api/locations/create/{clientId:int}",
            async (
                [FromServices] IPublisher<LocationCreated> publisher,
                [FromRoute] int clientId,
                [FromQuery] int? number) =>
            {
                foreach (var num in Enumerable.Range(0, number ?? 1))
                {
                    await publisher.PublishAsync(
                        new LocationCreated(Guid.NewGuid().ToString())
                        {
                            Name = "Location Created",
                            ClientId = clientId + num
                        });
                }

                return Results.Ok();
            });
        app.MapGet(
            "api/locations/batch/{clientId:int}",
            async (
                [FromServices] IPublisher<LocationCreated> created,
                [FromServices] IPublisher<LocationUpdated> updated,
                [FromRoute] int clientId,
                [FromQuery] int? number) =>
            {
                var createdList = new List<IEvent>();
                var updatedList = new List<IEvent>();
                foreach (var num in Enumerable.Range(0, number ?? 1))
                {
                    if (num % 2 == 0)
                        createdList.Add(
                            new LocationCreated(Guid.NewGuid().ToString())
                            {
                                Name = "Location Created",
                                ClientId = clientId + num
                            });
                    else
                        updatedList.Add(
                            new LocationUpdated(Guid.NewGuid().ToString())
                            {
                                Name = "Location Updated",
                                ClientId = clientId + num
                            });
                }

                foreach (var @event in createdList)
                {
                    await created.RePublishAsync(@event);
                }

                foreach (var @event in updatedList)
                {
                    await updated.RePublishAsync(@event);
                }

                return Results.Ok();
            });

        app.MapGet(
            "api/locations/schedule/{clientId:int}",
            async ([FromServices] IPublisher<LocationCreated> publisher, [FromRoute] int clientId) =>
            {
                await publisher.ScheduleAsync(
                    new LocationCreated(Guid.NewGuid().ToString())
                    {
                        Name = "Location Created",
                        ClientId = clientId
                    },
                    DateTimeOffset.Now.AddSeconds(15),
                    default);

                return Results.Ok();
            });

        app.MapGet(
            "api/locations/update/{clientId:int}",
            async ([FromServices] IPublisher<LocationUpdated> publisher, [FromRoute] int clientId) =>
            {
                await publisher.PublishAsync(
                    new LocationUpdated(Guid.NewGuid().ToString())
                    {
                        Name = "Location Updated",
                        ClientId = clientId
                    });

                return Results.Ok();
            });

        app.MapGet(
            "/api/stream/errors",
            async (INatsConnection natsConnection) =>
            {
                var jsContext = new NatsJSContext(natsConnection);

                var conf = new ConsumerConfig
                {
                    Description = "Error stream consumer",
                    FilterSubjects = ["core-test_errors.retention-server_location-updated-consumer.*"],
                    DeliverPolicy = ConsumerConfigDeliverPolicy.All,
                    InactiveThreshold = TimeSpan.FromSeconds(5)
                };

                var consumer = await jsContext.CreateConsumerAsync("core-test_errors", conf);

                var errors = new List<ErrorMessage>();
                await foreach (var msg in consumer.FetchAsync<ErrorMessage>(
                                   new NatsJSFetchOpts { Expires = TimeSpan.FromSeconds(1), MaxMsgs = 100 }))
                {
                    if (msg.Data == null)
                    {
                        break;
                    }

                    errors.Add(msg.Data);
                }

                return Results.Ok(errors);
            });
    });

public class LocationBlastCreatedRequest
{
    public string Name { get; set; }

    public ExternalIdentification ExternalIdentification { get; set; }
}