using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.Hosting.Web.Common;
using Kukui.Infrastructure.MessageBus.Nats.JetStream;

namespace Sample.WebApi.Nats.JetStream.Producer;

public class AppConfigurator : WebConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddNatsJetStream(configuration.ServerConfiguration);
    }
}