namespace Sample.WebApi.Nats.JetStream.Producer;

using System.Text.Json;
using System.Text.Json.Serialization;
using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;

public class OutboxJsonConverter : JsonConverter<IEvent>
{
    public override IEvent? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var element = JsonElement.ParseValue(ref reader);
        var typeInfo = element.GetProperty("$type").GetString();
        var convertType = Type.GetType(typeInfo!);

        if (convertType == null)
        {
            return null;
        }

        return (IEvent?) element.Deserialize(convertType, options);
    }

    public override void Write(Utf8JsonWriter writer, IEvent value, JsonSerializerOptions options)
    {
        JsonSerializer.Serialize(writer, value, Type.GetType(value.TypeInfo)!, options);
    }
}

public class OutboxJsonResultConverter : JsonConverter<SerializationResult<IEvent>>
{
    public override SerializationResult<IEvent> Read(
        ref Utf8JsonReader reader,
        Type typeToConvert,
        JsonSerializerOptions options)
    {
        var element = JsonElement.ParseValue(ref reader);
        var typeInfo = element.GetProperty("$type").GetString();
        var convertType = Type.GetType(typeInfo!);

        if (convertType == null)
        {
            return new SerializationResult<IEvent>(element.GetRawText());
        }

        return new SerializationResult<IEvent>(
            element.GetRawText(),
            (IEvent?) element.Deserialize(convertType, options));
    }

    public override void Write(Utf8JsonWriter writer, SerializationResult<IEvent> value, JsonSerializerOptions options)
    {
        if (value.Value == null)
        {
            throw new InvalidOperationException("Value cannot be null");
        }

        JsonSerializer.Serialize(writer, value.Value, Type.GetType(value.Value.TypeInfo)!, options);
    }
}