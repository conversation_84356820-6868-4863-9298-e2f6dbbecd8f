namespace Sample.WebApi.Nats.JetStream.Producer;

using global::Nats.Events.Shared;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;

public class OutboxEntry
{
    public OutboxEntry(DateTimeOffset partition, IEvent?[] events)
    {
        Partition = partition;
        Events = events;
    }

    public DateTimeOffset Partition { get; set; }

    public IEvent?[] Events { get; set; }
}

public class OutboxEntryResult
{
    public DateTimeOffset Partition { get; set; }

    public SerializationResult<IEvent?>[] Events { get; set; }
}