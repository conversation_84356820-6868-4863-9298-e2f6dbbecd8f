{"DisplayName": "Nats JetStream Producer", "Description": "Sample Web Api for producing messages to Nats JetStream", "Security": {"EnforceHttps": false, "AllowedHosts": "*", "AllowedCorsOrigins": ["https://localhost", "https://*.server.cluster", "https://*.kukui.com"], "Authentication": {"Enabled": false, "IdentityServerUrl": "https://docker-sandbox.server.clsuter:8100/auth"}}, "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Debug"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "formatter": "Serilog.Formatting.Json.JsonFormatter"}, {"Name": "Seq", "Args": {"ServerUrl": "http://localhost:5341", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}, {"Name": "File", "Args": {"path": "logs/application.log", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "fileSizeLimitBytes": "104857600", "restrictedToMinimumLevel": "Warning", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}]}}, "Server": {"DataBase": {"ConnectionString": "secret", "DefaultPageSize": 100, "DefaultKeyspace": "leads", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}, "Metrics": {"Enabled": false}}, "MessageService": {"Enabled": true, "Url": "nats://host.docker.internal:4222", "Username": "nats-user", "Password": "password"}, "Vault": {"Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"Name": "database", "ConfigurationSectionName": "server:Database", "Keys": [{"Source": "core-scylladb", "Target": "ConnectionString"}]}, {"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}