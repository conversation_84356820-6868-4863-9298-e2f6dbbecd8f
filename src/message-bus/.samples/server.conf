max_payload: 256KB,
monitor_port: 8222
accounts: {
    $SYS:{
        users: [{
            user: root
            password: p@ssw0rd
        }]
    },
    CP:{
        users: [{
            user: cp-admin
            password: password
            }
        ],
        jetstream : enabled
    }      
},
cluster: {
    name: "nats-sandbox-cluster",
    port: 6222,
    routes: [
        nats://nats-main:6222
    ]
},
jetstream: {
    store_dir:/etc/nats/js
}
