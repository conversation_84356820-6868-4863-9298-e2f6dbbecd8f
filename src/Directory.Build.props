<Project>

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <LangVersion>latest</LangVersion>
    <ImplicitUsings>true</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>true</IsPackable>
    <NoWarn>1701;1702;1591;CS8618;CA1805;NU1507;NU1701;CA2007</NoWarn>
  </PropertyGroup>

 <!-- SourceLink -->
 <PropertyGroup>
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <IncludeSymbols>false</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <DebugType>embedded</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TF_BUILD)' == 'true'">
    <ContinuousIntegrationBuild>true</ContinuousIntegrationBuild>
  </PropertyGroup>

  <ItemGroup>
    <SourceLinkAzureDevOpsServerGitHost Include="https://nexus.server.cluster" />
  </ItemGroup>

 <!-- SourceLink END -->

 <!--Package info-->
 <PropertyGroup>
    <Authors>Stefan Kolev</Authors>
    <Company>Kukui Holdings</Company>
    <Copyright>Copyright © Kukui Holdings $(date:YYYY)</Copyright>
    <RepositoryUrl>https://github.com/KukuiCorp/Kukui.Infrastructure</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageReleaseNotes>CHANGELOG.md</PackageReleaseNotes>
  </PropertyGroup>
 <!--Package info END-->

 <ItemGroup>
    <None Condition="'$(IsPackable)' == 'true'" Include="..\..\README.md" Pack="true" PackagePath="\" />
    <None Condition="'$(IsPackable)' == 'true'" Include="..\..\CHANGELOG.md" Pack="true" PackagePath="\" />
  </ItemGroup>

 <!--Versioning-->
 <PropertyGroup>
    <MinVerTagPrefix>v</MinVerTagPrefix>
    <MinVerDefaultPreReleaseIdentifiers>preview.0</MinVerDefaultPreReleaseIdentifiers>
    <MinVerSkip Condition="'$(Configuration)' == 'Debug'">true</MinVerSkip>
  </PropertyGroup>
 <!--Versioning END-->

 <!--Global references-->
 <ItemGroup>
    <PackageReference Include="MinVer" />
  </ItemGroup>
 <!--Global references END-->

</Project>