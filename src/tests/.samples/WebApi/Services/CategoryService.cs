using WebApi.Data.Repositories.Interfaces;
using WebApi.Models;
using WebApi.Services.Interfaces;

namespace WebApi.Services;

public class CategoryService : ICategoryService
{
    private readonly ICategoryRepository _categoryRepository;

    public CategoryService(ICategoryRepository categoryRepository)
    {
        _categoryRepository = categoryRepository;
    }

    public Task<IEnumerable<CategoryResponseObject>> GetCategories()
    {
        return _categoryRepository.GetAll();
    }
}