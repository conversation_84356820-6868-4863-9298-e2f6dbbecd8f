using WebApi.Services.Interfaces;

namespace WebApi.Services;

public class CustomerService : ICustomerService
{
    private readonly IConnectorServerService _connectorService;
    private readonly ICpGatewayService _gatewayService;

    public CustomerService(ICpGatewayService gatewayService, IConnectorServerService connectorService)
    {
        _gatewayService = gatewayService;
        _connectorService = connectorService;
    }

    public async Task<IEnumerable<string>> GetUnsubscribedEmails(int clientId, List<int> locations)
    {
        var connectorEmails = await _connectorService.GetUnsubscribedEmailsForAllLocations(locations);
        var gatewayEmails = await _gatewayService.GetUnsubscribedEmails(clientId);

        var result = connectorEmails
            .Concat(gatewayEmails)
            .Distinct();
        return result;
    }
}