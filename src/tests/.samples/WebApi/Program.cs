#if NET6_0_OR_GREATER
using System.Reflection;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Scrutor;
using WebApi.Data;
using WebApi.Extensions;
using WebApi.Services;

var builder = WebApplication.CreateBuilder();
Console.WriteLine("Starting WebApi");
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddDbContext<RetentionDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("RetentionContext")));

builder.Services.AddScoped<IDbSeeder<RetentionDbContext>, DbSeeder>();
builder.Services.AddHttpClient<ConnectorServerService>(client =>
    client.BaseAddress = new Uri(builder.Configuration["ConnectorServerUrl"]!));
builder.Services.AddMemoryCache();

builder.Services.AddAuthentication(options =>
    {
        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    })
    .AddJwtBearer(
        JwtBearerDefaults.AuthenticationScheme,
        x =>
        {
            x.Authority = builder.Configuration["AuthorizationServer"];
            x.TokenValidationParameters.ValidateAudience = false;
            x.TokenValidationParameters.ValidateIssuer = false;
            x.RequireHttpsMetadata = false;
        });

builder.Services.Scan(selector => selector.FromAssemblies(Assembly.GetExecutingAssembly())
    .AddClasses(false)
    .UsingRegistrationStrategy(RegistrationStrategy.Skip)
    .AsMatchingInterface()
    .WithLifetime(ServiceLifetime.Transient));

var app = builder.Build();
if (app.Environment.IsDevelopment())
{
    app.ApplyMigrations<RetentionDbContext, DbSeeder>("db_seed_data.json");
}

app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
await app.RunAsync();

public partial class Program
{
}
#else
namespace WebApi;

public static class Program
{
    public static void Main(string[] args)
    {
            CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureWebHostDefaults(webBuilder => { webBuilder.UseStartup<Startup>(); });
}
#endif