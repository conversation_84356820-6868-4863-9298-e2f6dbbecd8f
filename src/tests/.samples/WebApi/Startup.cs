#if NET5_0
namespace WebApi;

public class Startup
{
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    public IConfiguration Configuration { get; }

    public void ConfigureServices(IServiceCollection services)
    {
        services.AddControllers();
        services.AddSwaggerGen(c => { c.SwaggerDoc("v1", new OpenApiInfo { Title = "WebApi", Version = "v1" }); });

        services.AddDbContext<RetentionDbContext>(options =>
            options.UseNpgsql(Configuration.GetConnectionString("RetentionContext")!));
        services.AddScoped<IDbSeeder<RetentionDbContext>, DbSeeder>();
        services.AddAutoMapper(typeof(Startup).Assembly);
        services.AddHttpClient<ConnectorServerService>(client => client.BaseAddress =
            new Uri(Configuration["ConnectorServerUrl"]));
        services.AddMemoryCache();

        services.Scan(
            selector => selector.FromAssemblies(Assembly.GetExecutingAssembly())
                .AddClasses(false)
                .UsingRegistrationStrategy(RegistrationStrategy.Skip)
                .AsMatchingInterface()
                .WithLifetime(ServiceLifetime.Transient));
    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "WebApi v1"));
            app.ApplyMigrations<RetentionDbContext, DbSeeder>("db_seed_data.json");
        }

        app.UseRouting();

        app.UseAuthentication();

        app.UseAuthorization();

        app.UseEndpoints(endpoints => { endpoints.MapControllers(); });
    }
}
#endif