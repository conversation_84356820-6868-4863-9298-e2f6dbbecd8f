using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using WebApi.Data.Seeding;

namespace WebApi.Extensions;

public static class AppBuilderExtensions
{
    public static bool ApplyMigrations<TContext>(this IApplicationBuilder app) where TContext : DbContext
    {
        using var scope = app.ApplicationServices.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<TContext>();
        context.Database.Migrate();
        return context.Database.EnsureCreated();
    }

    public static void ApplyMigrations<TContext, TSeeder>(this IApplicationBuilder app, string fileName)
        where TContext : DbContext
        where TSeeder : IDbSeeder<TContext>
    {
        using var scope = app.ApplicationServices.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<TContext>();
        var dbSeeder = scope.ServiceProvider.GetRequiredService<IDbSeeder<TContext>>();

        ApplyMigrations<TContext>(app);

        var seedingModel =
            JsonConvert.DeserializeObject<SeedingModel>(
                File.ReadAllText(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Seeding", fileName)));
        dbSeeder
            .SeedAsync(context, seedingModel)
            .ConfigureAwait(false)
            .GetAwaiter()
            .GetResult();
    }
}