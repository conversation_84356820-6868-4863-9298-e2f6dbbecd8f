services:
  webapi:
    image: webapi
    build:
      context: .
      dockerfile: .samples/WebApi/Dockerfile
  postgres_db:
    image: postgres:16.3
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: p@ssw0rd
      POSTGRES_DB: retention
  sqlserver:
    container_name: sqlserver
    restart: unless-stopped
    image: mcr.microsoft.com/mssql/server:2019-CU18-ubuntu-20.04
    environment:
      - ACCEPT_EULA=Y
      - MSSQL_SA_PASSWORD=p@ssw0rd
    ports:
      - "1433:1433"
    volumes:
      - '/Users/<USER>/sqlserver/data:/var/opt/mssql/data'
      - '/Users/<USER>/sqlserver/log:/var/opt/mssql/log'
    deploy:
      resources:
        limits:
          memory: 2048M

volumes:
  postgres_data: