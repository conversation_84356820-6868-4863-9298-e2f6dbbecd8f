// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using WebApi.Data;

#nullable disable

namespace WebApi.Data.Migrations
{
    [DbContext(typeof(RetentionDbContext))]
    [Migration("20240606111958_InitialMigration")]
    partial class InitialMigration
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.31")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("WebApi.Data.Entities.Category", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("image");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("pk_category");

                    b.ToTable("category", (string)null);
                });

            modelBuilder.Entity("WebApi.Data.Entities.EmailCampaign", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("body");

                    b.Property<string>("CampaignName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("campaign_name");

                    b.Property<int>("ClientId")
                        .HasColumnType("integer")
                        .HasColumnName("client_id");

                    b.Property<string>("CpId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cp_id");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("display_name");

                    b.Property<DateTime?>("DraftSendDateReminder")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("draft_send_date_reminder");

                    b.Property<string>("EmailFrom")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("email_from");

                    b.Property<string>("EmailSubject")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("email_subject");

                    b.Property<int>("LocationId")
                        .HasColumnType("integer")
                        .HasColumnName("location_id");

                    b.Property<string>("Recipients")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("recipients");

                    b.Property<DateTime?>("SendDate")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("send_date");

                    b.Property<string>("Settings")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("settings");

                    b.Property<int?>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<Guid>("TemplateId")
                        .HasColumnType("uuid")
                        .HasColumnName("template_id");

                    b.HasKey("Id")
                        .HasName("pk_email_campaign");

                    b.HasIndex("TemplateId")
                        .HasDatabaseName("ix_email_campaign_template_id");

                    b.ToTable("email_campaign", (string)null);
                });

            modelBuilder.Entity("WebApi.Data.Entities.EmailCampaignTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("CategoryId")
                        .IsRequired()
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<Guid?>("EventId")
                        .IsRequired()
                        .HasColumnType("uuid")
                        .HasColumnName("event_id");

                    b.Property<string>("Images")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasDefaultValue("[]")
                        .HasColumnName("images");

                    b.Property<string>("PreviewTemplateImage")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("preview_template_image");

                    b.Property<string>("TemplateImage")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("template_image");

                    b.HasKey("Id")
                        .HasName("pk_email_campaign_template");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_email_campaign_template_category_id");

                    b.HasIndex("EventId")
                        .HasDatabaseName("ix_email_campaign_template_event_id");

                    b.ToTable("email_campaign_template", (string)null);
                });

            modelBuilder.Entity("WebApi.Data.Entities.Event", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uuid")
                        .HasColumnName("category_id");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("image");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<string>("StockContent")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("stock_content");

                    b.HasKey("Id")
                        .HasName("pk_event");

                    b.HasIndex("CategoryId")
                        .HasDatabaseName("ix_event_category_id");

                    b.ToTable("event", (string)null);
                });

            modelBuilder.Entity("WebApi.Data.Entities.Image", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid?>("CampaignId")
                        .HasColumnType("uuid")
                        .HasColumnName("campaign_id");

                    b.Property<bool>("InUse")
                        .HasColumnType("boolean")
                        .HasColumnName("in_use");

                    b.Property<string>("KeyName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("key_name");

                    b.Property<string>("Uri")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("uri");

                    b.HasKey("Id")
                        .HasName("pk_image");

                    b.HasIndex("CampaignId")
                        .HasDatabaseName("ix_image_campaign_id");

                    b.ToTable("image", (string)null);
                });

            modelBuilder.Entity("WebApi.Data.Entities.LocationSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("DefaultAccentColors")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("default_accent_colors");

                    b.Property<int>("LocationId")
                        .HasColumnType("integer")
                        .HasColumnName("location_id");

                    b.HasKey("Id")
                        .HasName("pk_location_setting");

                    b.ToTable("location_setting", (string)null);
                });

            modelBuilder.Entity("WebApi.Data.Entities.EmailCampaign", b =>
                {
                    b.HasOne("WebApi.Data.Entities.EmailCampaignTemplate", "EmailCampaignTemplate")
                        .WithMany("EmailCampaigns")
                        .HasForeignKey("TemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_email_campaign_email_campaign_template_template_id");

                    b.Navigation("EmailCampaignTemplate");
                });

            modelBuilder.Entity("WebApi.Data.Entities.EmailCampaignTemplate", b =>
                {
                    b.HasOne("WebApi.Data.Entities.Category", "Category")
                        .WithMany("EmailCampaignTemplates")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_email_campaign_template_category_category_id");

                    b.HasOne("WebApi.Data.Entities.Event", "Event")
                        .WithMany("EmailCampaignTemplates")
                        .HasForeignKey("EventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_email_campaign_template_event_event_id");

                    b.Navigation("Category");

                    b.Navigation("Event");
                });

            modelBuilder.Entity("WebApi.Data.Entities.Event", b =>
                {
                    b.HasOne("WebApi.Data.Entities.Category", "Category")
                        .WithMany("Events")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_event_category_category_id");

                    b.Navigation("Category");
                });

            modelBuilder.Entity("WebApi.Data.Entities.Image", b =>
                {
                    b.HasOne("WebApi.Data.Entities.EmailCampaign", "EmailCampaign")
                        .WithMany()
                        .HasForeignKey("CampaignId")
                        .HasConstraintName("fk_image_email_campaign_campaign_id");

                    b.Navigation("EmailCampaign");
                });

            modelBuilder.Entity("WebApi.Data.Entities.Category", b =>
                {
                    b.Navigation("EmailCampaignTemplates");

                    b.Navigation("Events");
                });

            modelBuilder.Entity("WebApi.Data.Entities.EmailCampaignTemplate", b =>
                {
                    b.Navigation("EmailCampaigns");
                });

            modelBuilder.Entity("WebApi.Data.Entities.Event", b =>
                {
                    b.Navigation("EmailCampaignTemplates");
                });
#pragma warning restore 612, 618
        }
    }
}
