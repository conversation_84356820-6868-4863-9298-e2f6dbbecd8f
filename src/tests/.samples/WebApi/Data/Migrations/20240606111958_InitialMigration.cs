using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WebApi.Data.Migrations
{
    public partial class InitialMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "category",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    image = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_category", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "location_setting",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    location_id = table.Column<int>(type: "integer", nullable: false),
                    default_accent_colors = table.Column<string>(type: "jsonb", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_location_setting", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "event",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "text", nullable: false),
                    stock_content = table.Column<string>(type: "jsonb", nullable: false),
                    category_id = table.Column<Guid>(type: "uuid", nullable: false),
                    image = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_event", x => x.id);
                    table.ForeignKey(
                        name: "fk_event_category_category_id",
                        column: x => x.category_id,
                        principalTable: "category",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "email_campaign_template",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    category_id = table.Column<Guid>(type: "uuid", nullable: false),
                    event_id = table.Column<Guid>(type: "uuid", nullable: false),
                    preview_template_image = table.Column<string>(type: "text", nullable: false),
                    template_image = table.Column<string>(type: "text", nullable: false),
                    images = table.Column<string>(type: "jsonb", nullable: false, defaultValue: "[]")
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_email_campaign_template", x => x.id);
                    table.ForeignKey(
                        name: "fk_email_campaign_template_category_category_id",
                        column: x => x.category_id,
                        principalTable: "category",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_email_campaign_template_event_event_id",
                        column: x => x.event_id,
                        principalTable: "event",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "email_campaign",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    campaign_name = table.Column<string>(type: "text", nullable: false),
                    client_id = table.Column<int>(type: "integer", nullable: false),
                    location_id = table.Column<int>(type: "integer", nullable: false),
                    cp_id = table.Column<string>(type: "text", nullable: false),
                    template_id = table.Column<Guid>(type: "uuid", nullable: false),
                    body = table.Column<string>(type: "text", nullable: false),
                    send_date = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: true),
                    email_from = table.Column<string>(type: "text", nullable: false),
                    display_name = table.Column<string>(type: "text", nullable: false),
                    email_subject = table.Column<string>(type: "text", nullable: false),
                    recipients = table.Column<string>(type: "jsonb", nullable: false),
                    settings = table.Column<string>(type: "jsonb", nullable: false),
                    draft_send_date_reminder = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_email_campaign", x => x.id);
                    table.ForeignKey(
                        name: "fk_email_campaign_email_campaign_template_template_id",
                        column: x => x.template_id,
                        principalTable: "email_campaign_template",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "image",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    campaign_id = table.Column<Guid>(type: "uuid", nullable: true),
                    uri = table.Column<string>(type: "text", nullable: false),
                    key_name = table.Column<string>(type: "text", nullable: false),
                    in_use = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_image", x => x.id);
                    table.ForeignKey(
                        name: "fk_image_email_campaign_campaign_id",
                        column: x => x.campaign_id,
                        principalTable: "email_campaign",
                        principalColumn: "id");
                });

            migrationBuilder.CreateIndex(
                name: "ix_email_campaign_template_id",
                table: "email_campaign",
                column: "template_id");

            migrationBuilder.CreateIndex(
                name: "ix_email_campaign_template_category_id",
                table: "email_campaign_template",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "ix_email_campaign_template_event_id",
                table: "email_campaign_template",
                column: "event_id");

            migrationBuilder.CreateIndex(
                name: "ix_event_category_id",
                table: "event",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "ix_image_campaign_id",
                table: "image",
                column: "campaign_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "image");

            migrationBuilder.DropTable(
                name: "location_setting");

            migrationBuilder.DropTable(
                name: "email_campaign");

            migrationBuilder.DropTable(
                name: "email_campaign_template");

            migrationBuilder.DropTable(
                name: "event");

            migrationBuilder.DropTable(
                name: "category");
        }
    }
}
