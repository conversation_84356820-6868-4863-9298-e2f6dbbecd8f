using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WebApi.Data.Entities;

[Table("email_campaign_template")]
public partial class EmailCampaignTemplate
{
    [Key] [Required] [Column("id")] public Guid Id { get; set; }

    [ForeignKey(nameof(Category))]
    [Column("category_id")]
    public Guid? CategoryId { get; set; }

    [ForeignKey(nameof(Event))]
    [Column("event_id")]
    public Guid? EventId { get; set; }

    [Column("preview_template_image")] public string PreviewTemplateImage { get; set; }

    [Column("template_image")] public string TemplateImage { get; set; }

    [Required]
    [Column("images", TypeName = "jsonb")]
    public string Images { get; set; }

    public Category Category { get; set; }

    public Event Event { get; set; }

    public ICollection<EmailCampaign> EmailCampaigns { get; set; }
}