using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WebApi.Data.Entities;

public enum StatusEnum
{
    Draft = 0,
    Scheduled = 1,
    Sent = 2,
    InProgress = 3
}

[Table("email_campaign")]
public class EmailCampaign
{
    [Key] [Column("id")] public Guid Id { get; set; }

    [Column("campaign_name")] public string CampaignName { get; set; }

    [Column("client_id")] public int ClientId { get; set; }

    [Column("location_id")] public int LocationId { get; set; }

    [Column("cp_id")] public string CpId { get; set; }

    [ForeignKey(nameof(EmailCampaignTemplate))]
    [Column("template_id")]
    public Guid TemplateId { get; set; }

    [Column("body")] public string Body { get; set; }

    [Column("send_date")] public DateTime? SendDate { get; set; }

    [Column("status")] public StatusEnum? Status { get; set; }

    [Column("email_from")] public string EmailFrom { get; set; }

    [Column("display_name")] public string DisplayName { get; set; }

    [Column("email_subject")] public string EmailSubject { get; set; }

    [Column("recipients", TypeName = "jsonb")]
    public string Recipients { get; set; }

    [Column("settings", TypeName = "jsonb")]
    public string Settings { get; set; }

    [Column("draft_send_date_reminder")] public DateTime? DraftSendDateReminder { get; set; }

    public EmailCampaignTemplate EmailCampaignTemplate { get; set; }
}