using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

// using System.Text.Json.Serialization;

namespace WebApi.Data.Entities;

[Table("event")]
public partial class Event
{
    [Key] [Required] [Column("id")] public Guid Id { get; set; }

    [Required] [Column("name")] public string Name { get; set; }

    [Column("stock_content", TypeName = "jsonb")]
    public string StockContent { get; set; }

    [ForeignKey(nameof(Category))]
    [Column("category_id")]
    public Guid CategoryId { get; set; }

    [Column("image")] public string Image { get; set; }

    public ICollection<EmailCampaignTemplate> EmailCampaignTemplates { get; set; }

    public Category Category { get; set; }
}