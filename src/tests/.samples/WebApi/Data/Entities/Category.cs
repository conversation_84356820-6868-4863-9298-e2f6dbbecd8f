using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WebApi.Data.Entities;

[Table("category")]
public partial class Category
{
    [Key] [Required] [Column("id")] public Guid Id { get; set; }

    [Required] [Column("name")] public string Name { get; set; }

    [Column("image")] public string Image { get; set; }

    public ICollection<EmailCampaignTemplate> EmailCampaignTemplates { get; set; }

    public ICollection<Event> Events { get; set; }
}