using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WebApi.Data.Entities;

[Table("location_setting")]
public class LocationSetting
{
    [Key] [Required] [Column("id")] public Guid Id { get; set; }

    [Column("location_id")] public int LocationId { get; set; }

    [Column("default_accent_colors", TypeName = "jsonb")]
    public string DefaultAccentColors { get; set; }
}