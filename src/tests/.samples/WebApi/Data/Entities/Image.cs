using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace WebApi.Data.Entities;

[Table("image")]
public partial class Image
{
    [Key] [Required] [Column("id")] public Guid Id { get; set; }

    [Column("campaign_id")]
    [ForeignKey(nameof(EmailCampaign))]
    public Guid? CampaignId { get; set; }

    [Column("uri")] public string Uri { get; set; }

    [Column("key_name")] public string KeyName { get; set; }

    [Column("in_use")] public bool InUse { get; set; }

    [JsonIgnore] public EmailCampaign EmailCampaign { get; set; }
}