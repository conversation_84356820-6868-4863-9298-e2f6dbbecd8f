using Microsoft.EntityFrameworkCore;
using WebApi.Data.Entities;

namespace WebApi.Data;

public class RetentionDbContext : DbContext
{
    public RetentionDbContext(DbContextOptions<RetentionDbContext> options) : base(options)
    {
    }

    public virtual DbSet<EmailCampaign> EmailCampaigns { get; set; }
    public virtual DbSet<EmailCampaignTemplate> EmailCampaignTemplates { get; set; }
    public virtual DbSet<Category> Categories { get; set; }
    public virtual DbSet<Event> Events { get; set; }
    public virtual DbSet<Image> Images { get; set; }
    public virtual DbSet<LocationSetting> LocationSettings { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        builder
            .Entity<EmailCampaignTemplate>()
            .Property(t => t.Images)
            .HasDefaultValue("[]");
        base.OnModelCreating(builder);
    }
}