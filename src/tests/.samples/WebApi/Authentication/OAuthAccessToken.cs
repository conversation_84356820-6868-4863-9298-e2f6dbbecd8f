using Newtonsoft.Json;

namespace WebApi.Authentication;

public class OAuthAccessToken
{
    [JsonProperty(PropertyName = "access_token")]
    public string AccessToken { get; set; }

    /// <summary>
    /// The access token expiration time in seconds
    /// </summary>
    [JsonProperty(PropertyName = "expires_in")]
    public int ExpiresIn { get; set; }

    [JsonProperty(PropertyName = "refresh_token")]
    public string RefreshToken { get; set; }

    [JsonProperty(PropertyName = "refresh_expires_in")]
    public int RefreshExpiresIn { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public bool IsExpired => CreatedAt.AddSeconds(ExpiresIn - 10) <= GetUtcNow();

    public bool IsRefreshExpired => CreatedAt.AddSeconds(RefreshExpiresIn - 10) <= GetUtcNow();

    public virtual DateTime GetUtcNow()
    {
        return DateTime.UtcNow;
    }
}