namespace WebApi.Authentication;

public class OAuthSettings
{
    public OAuthSettings(
        string clientId,
        string clientSecret,
        string providerEndpoint,
        CacheKey key,
        string grantType = "client_credentials",
        string scope = "openid")
    {
        ClientId = clientId;
        ClientSecret = clientSecret;
        ProviderEndpoint = providerEndpoint;
        CacheKey = key;
        GrantType = grantType;
        Scope = scope;
    }

    public string ClientId { get; set; }

    public string GrantType { get; set; }

    public string ClientSecret { get; set; }

    public string Scope { get; set; }

    public string ProviderEndpoint { get; set; }

    public CacheKey CacheKey { get; set; }
}