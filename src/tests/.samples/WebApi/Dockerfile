FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY [".samples/WebApi/WebApi.csproj", ".samples/WebApi/"]
COPY ["src/Testing.Containers.Database/Testing.Containers.Database.csproj", "src/Testing.Containers.Database/"]
COPY ["src/Testing.Containers.Abstractions/Testing.Containers.Abstractions.csproj", "src/Testing.Containers.Abstractions/"]
COPY ["src/Testing.Containers.Database.ScyllaDb/Testing.Containers.Database.ScyllaDb.csproj", "src/Testing.Containers.Database.ScyllaDb/"]
COPY ["src/Testing.InetegrationTests/Testing.InetegrationTests.csproj", "src/Testing.InetegrationTests/"]
COPY ["src/Testing.UnitTests/Testing.UnitTests.csproj", "src/Testing.UnitTests/"]
RUN dotnet restore ".samples/WebApi/WebApi.csproj"
COPY . .
WORKDIR "/src/.samples/WebApi"
RUN dotnet build "WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "WebApi.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "WebApi.dll"]