using AutoMapper;
using Newtonsoft.Json;
using WebApi.Data.Entities;
using WebApi.Models;

namespace WebApi.Mappings;

public class CategoryProfile : Profile
{
    public CategoryProfile()
    {
        CreateMap<Category, CategoryResponseObject>();
    }
}

public class EventProfile : Profile
{
    public EventProfile()
    {
        CreateMap<Event, EventResponseObject>()
            .ForMember(
                x => x.StockContent,
                opts => opts.MapFrom(
                    source => JsonConvert.DeserializeObject<List<StockContent>>(source.StockContent)
                )
            );
    }
}