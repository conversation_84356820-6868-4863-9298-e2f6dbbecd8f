using System.ComponentModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using WebApi.Authorization;
using WebApi.Models;
using WebApi.Services.Interfaces;

namespace WebApi.Controllers;

[Authorize(Roles = Roles.RetentionWeb)]
[ApiController]
[Route("api/categories")]
public class CategoriesController : ControllerBase
{
    private readonly ICategoryService _categoryService;

    public CategoriesController(ICategoryService categoryService)
    {
        _categoryService = categoryService;
    }

    [HttpGet("")]
    [ProducesResponseType(typeof(IEnumerable<CategoryResponseObject>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(BadRequestResult), StatusCodes.Status400BadRequest)]
    [Description("Get categories")]
    public async Task<IActionResult> GetCategories()
    {
        try
        {
            return Ok(await _categoryService.GetCategories());
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Get categories failed.");
            return BadRequest();
        }
    }
}