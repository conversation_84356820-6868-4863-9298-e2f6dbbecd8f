using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using WebApi.Authorization;
using WebApi.Exceptions;
using WebApi.Services.Interfaces;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize(Roles = Roles.RetentionWeb + "," + Roles.EmailService)]
public class ClientsController : ControllerBase
{
    private readonly ICpGatewayService _cpGatewayService;
    private readonly ICustomerService _customerService;

    public ClientsController(ICpGatewayService cpGatewayService, ICustomerService customerService)
    {
        _cpGatewayService = cpGatewayService;
        _customerService = customerService;
    }

    [HttpGet("/v1/client/{clientId:int}/unsubscribedEmails")]
    [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetUnsubscribedCustomersEmails([FromRoute] int clientId,
        [FromQuery] List<int> locations)
    {
        try
        {
            return Ok(await _customerService.GetUnsubscribedEmails(clientId, locations));
        }
        catch (BubbleException ex)
        {
            Log
                .ForContext("ClientId", clientId)
                .ForContext("Locations", locations)
                .Error(ex, "Get unsubscribed customers emails failed with CP Gateway error.");
            return NotFound();
        }
        catch (Exception ex)
        {
            Log
                .ForContext("ClientId", clientId)
                .ForContext("Locations", locations)
                .Error(ex, "Get unsubscribed customers emails failed.");
            return BadRequest();
        }
    }

    [Route("/v1/client/{clientId:int}/clientSettings/logoUrl")]
    [HttpGet]
    [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetLogo([FromRoute] int clientId)
    {
        try
        {
            return Ok(await _cpGatewayService.GetLogo(clientId));
        }
        catch (BubbleException ex)
        {
            Log
                .ForContext("ClientId", clientId)
                .Error(ex, "Get logo failed with CP Gateway error.");
            return NotFound();
        }
        catch (Exception ex)
        {
            Log
                .ForContext("ClientId", clientId)
                .Error(ex, "Get logo failed.");
            return BadRequest();
        }
    }
}