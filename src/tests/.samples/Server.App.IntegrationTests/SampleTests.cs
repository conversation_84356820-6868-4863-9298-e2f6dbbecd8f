namespace Server.App.IntegrationTests;

using Cassandra;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;

public class SampleTests
{
    private readonly TestApplication _application;

    public SampleTests(TestApplication application)
    {
        _application = application;
    }

    [Fact]
    public void Test1()
    {
        var session = _application.Services.GetService<ISession>();

        session.Should().NotBeNull();
    }
}