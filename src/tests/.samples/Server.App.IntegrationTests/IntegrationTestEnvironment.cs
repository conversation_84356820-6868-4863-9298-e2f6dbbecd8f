namespace Server.App.IntegrationTests;

using Kukui.Infrastructure.Testing.Containers.Database;
using Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;
using Kukui.Infrastructure.Testing.IntegrationTests;

public class IntegrationTestEnvironment : TestEnvironmentBase, IAssemblyFixture<IntegrationTestEnvironment>
{
    public IntegrationTestEnvironment()
    {
        Configure(builder => builder.AddScyllaDb());
    }

    protected override async Task AfterInitializationAsync()
    {
        var scyllaDbContainer = GetContainer<ScyllaDbContainer>();

        await foreach (var script in GetScriptFilesContent(searchPattern: "*.cql"))
        {
            await scyllaDbContainer.ExecScriptAsync(script);
        }
    }
}