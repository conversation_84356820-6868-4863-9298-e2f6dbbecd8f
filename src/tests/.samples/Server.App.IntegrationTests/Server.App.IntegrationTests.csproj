<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\message-bus\src\MessageBus.Nats.Hosting.Extensions\MessageBus.Nats.Hosting.Extensions.csproj" />
    <ProjectReference Include="..\..\src\Testing.Containers.Database\Testing.Containers.Database.csproj" />
    <ProjectReference Include="..\..\src\Testing.Containers.MessageBus\Testing.Containers.MessageBus.csproj" />
    <ProjectReference Include="..\..\src\Testing.IntegrationTests\Testing.IntegrationTests.csproj" />
    <ProjectReference Include="..\Server.Application\Server.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <InternalsVisibleTo Include="Server.Application.IntegrationTests" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="configs\config.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>