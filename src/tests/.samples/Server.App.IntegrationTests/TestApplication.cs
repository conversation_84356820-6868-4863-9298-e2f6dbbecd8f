namespace Server.App.IntegrationTests;

using Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;
using Kukui.Infrastructure.Testing.IntegrationTests.AppFactories;
using Microsoft.Extensions.DependencyInjection;

public class TestApplication : ServerAppFactory<IntegrationTestEnvironment, Program>, IAssemblyFixture<TestApplication>
{
    public TestApplication(IntegrationTestEnvironment environment) : base(environment)
    {
    }

    protected override string ConfigJsonFileName { get; } = "config.json";

    protected override void ConfigureServices(IServiceCollection services)
    {
    }

    protected override void UpdateAppConfiguration(Dictionary<string, string?> configuration)
    {
        configuration.Add(
            "Server:Database:ConnectionString",
            Environment.GetContainer<ScyllaDbContainer>().GetConnectionString());
    }
}