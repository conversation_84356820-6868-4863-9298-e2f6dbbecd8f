{"DisplayName": "Sample ScyllaDb Application", "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "formatter": "Serilog.Formatting.Json.JsonFormatter"}, {"Name": "Seq", "Args": {"ServerUrl": "http://localhost:5341", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}, {"Name": "File", "Args": {"path": "logs/application.log", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "fileSizeLimitBytes": "104857600", "restrictedToMinimumLevel": "Warning", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}]}}, "Server": {"DataBase": {"ConnectionString": "Contact Points=host.docker.internal:9042;username=*********;password=*********", "DefaultPageSize": 100, "DefaultKeyspace": "core", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}}, "MessageService": {"Url": "nats://localhost:4222", "Username": "amdin", "Password": "secret"}, "Vault": {"Enabled": false, "Address": "http://docker-sandbox.server.cluster:8200/", "Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}