<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Testing.Containers.Database\Testing.Containers.Database.csproj" />
    <ProjectReference Include="..\..\src\Testing.Containers.MessageBus\Testing.Containers.MessageBus.csproj" />
    <ProjectReference Include="..\..\src\Testing.IntegrationTests\Testing.IntegrationTests.csproj" />
    <ProjectReference Include="..\WebApi.ScyllaDb\WebApi.ScyllaDb.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="scripts\**">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Remove="configs\config.json" />
    <Content Include="configs\config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>