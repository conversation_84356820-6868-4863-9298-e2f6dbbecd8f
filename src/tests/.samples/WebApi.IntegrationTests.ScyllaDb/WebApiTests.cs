namespace WebApi.IntegrationTests.ScyllaDb;

using System.Net;
using FluentAssertions;

public class WebApiTests
{
    private readonly TestApplication _application;

    public WebApiTests(TestApplication application)
    {
        _application = application;
    }

    //[Fact]
    public async Task GetCategories_returns_categories()
    {
        var response = await _application.HttpClient.GetAsync("entities?name=test-name");

        response.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
    }
}