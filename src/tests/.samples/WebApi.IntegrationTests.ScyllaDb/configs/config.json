{"Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Debug"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"ServerUrl": "http://localhost:5341", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}]}}, "Server": {"MessageService": {"Url": "nats://localhost:4222", "Username": "admin", "Password": "secret", "ConsumerPrefix": "int-test"}}}