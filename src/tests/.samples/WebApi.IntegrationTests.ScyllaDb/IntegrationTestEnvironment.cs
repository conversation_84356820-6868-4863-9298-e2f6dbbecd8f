namespace WebApi.IntegrationTests.ScyllaDb;

using Kukui.Infrastructure.Testing.Containers.Database;
using Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;
using Kukui.Infrastructure.Testing.Containers.MessageBus;
using Kukui.Infrastructure.Testing.IntegrationTests;

public class IntegrationTestEnvironment : TestEnvironmentBase
//, IAssemblyFixture<IntegrationTestEnvironment>
{
    public IntegrationTestEnvironment()
    {
        Configure(builder => builder.AddScyllaDb().AddNats());
    }

    protected override async Task AfterInitializationAsync()
    {
        var scyllaDbContainer = GetContainer<ScyllaDbContainer>();

        await foreach (var script in GetScriptFilesContent(searchPattern: "*.cql"))
        {
            await scyllaDbContainer.ExecScriptAsync(script);
        }

        await foreach (var script in GetScriptFilesContent(searchPattern: "*.csv"))
        {
            await scyllaDbContainer.ImportCsvFile("core", "business_entity", script);
        }

        await scyllaDbContainer.ExportCsvFile("core", "business_entity");
    }
}