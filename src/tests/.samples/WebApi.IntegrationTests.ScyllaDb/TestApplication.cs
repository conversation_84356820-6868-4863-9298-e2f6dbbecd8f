namespace WebApi.IntegrationTests.ScyllaDb;

using Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;
using Kukui.Infrastructure.Hosting.Common.Extensions;
using Kukui.Infrastructure.MessageBus.Hosting.Extensions;
using Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;
using Kukui.Infrastructure.Testing.IntegrationTests.AppFactories;
using Microsoft.Extensions.DependencyInjection;
using Testcontainers.Nats;
using WebApi.ScyllaDb;

public class TestApplication : WebAppFactory<IntegrationTestEnvironment, WebServerConfigurator>
//, IAssemblyFixture<TestApplication>
{
    public TestApplication(IntegrationTestEnvironment environment) : base(environment)
    {
    }

    protected override void ConfigureServices(IServiceCollection services)
    {
        var scyllaDbContainer = Environment.GetContainer<ScyllaDbContainer>();
        var settings = services.GetRequiredService<ScyllaDbConnectionSettings>();
        settings.ConnectionString = scyllaDbContainer.GetConnectionString();

        var natsContainers = Environment.GetContainers<NatsContainer>();
        var natsConnectionOptions = services.GetRequiredService<NatsConnectionOptions>();
        natsConnectionOptions.Url = string.Join(",", natsContainers.Select(x => x.GetConnectionString()[..^1]));
    }

    protected override Task AfterTestServerStartAsync() => base.AfterTestServerStartAsync();
}