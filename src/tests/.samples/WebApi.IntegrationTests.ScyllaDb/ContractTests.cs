namespace WebApi.IntegrationTests.ScyllaDb;

public class ContractTests
{
    private readonly WebApp _app;

    public ContractTests(WebApp app)
    {
        _app = app;
    }

    [Fact]
    public async Task JustTest()
    {
        var client = new HttpClient { BaseAddress = _app.HttpClient.BaseAddress };
        var response = await _app.HttpClient.GetAsync("entities?name=test-name");

        await Task.Delay(10000);
    }
}