using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

namespace WebApi.IntegrationTests.ScyllaDb;

public class SchemaBuilderMappingProcessor : IMappingProcessor
{
    private readonly IMappingProcessor _mappingProcessor;

    public SchemaBuilderMappingProcessor(IMappingProcessor mappingProcessor)
    {
        _mappingProcessor = mappingProcessor;
    }

    public IEntityMappingBuilder ProcessMappings(EntityMapperConfiguration mapperConfiguration)
    {
        var mappingBuilder = _mappingProcessor.ProcessMappings(mapperConfiguration);

        return mappingBuilder;
    }
}