CREATE KEYSPACE IF NOT EXISTS core WITH REPLICATION = { 'class' : 'SimpleStrategy', 'replication_factor' : 1 };

CREATE TYPE IF NOT EXISTS core.geo_coordinate
    (
        latitude  FLOAT,
        longitude FLOAT
    );

CREATE TYPE IF NOT EXISTS core.location_address
    (
        country     TEXT,
        region      TEXT,
        city        TEXT,
        postal_code TEXT,
        street      TEXT,
        coordinates FRO<PERSON><PERSON><geo_coordinate>
    );

DROP TABLE IF EXISTS core.business_entity;

CREATE TABLE IF NOT EXISTS core.business_entity
(
    id                 UUID,
    name               TEXT,
    admin_email        TEXT,
    enabled            BOOLEAN,
    created_date_time  TIMESTAMP,
    modified_date_time TIMESTAMP,
    name_search        LIST<TEXT>,
    PRIMARY KEY ( id, name )
) WITH CACHING = {'enabled': 'true'};
CREATE INDEX IF NOT EXISTS business_entity_by_name ON core.business_entity (VALUES (name_search));


DROP TABLE IF EXISTS core.entity_groups;

CREATE TABLE IF NOT EXISTS core.entity_groups
(
    entity_id          UUID,
    id                 UUID,
    name               TEXT,
    locations          MAP<UUID, TEXT>,
    description        TEXT,
    enabled            BOOLEAN,
    created_date_time  TIMESTAMP,
    modified_date_time TIMESTAMP,
    PRIMARY KEY ( entity_id, name)
) WITH CACHING = { 'enabled': 'true' };
CREATE INDEX IF NOT EXISTS entity_groups_by_id ON core.entity_groups ((entity_id), ID);

DROP TABLE IF EXISTS core.location;

CREATE TABLE IF NOT EXISTS core.location
(
    id                      UUID,
    name                    TEXT,
    entity_id               UUID,
    group_names             SET<TEXT>,
    timezone                TEXT,
    address                 FROZEN<location_address>,
    enabled                 BOOLEAN,
    created_date_time       TIMESTAMP,
    modified_date_time      TIMESTAMP,
    external_identification MAP<TEXT, INT>,
    cp_name_search          LIST<TEXT>,
    name_search             LIST<TEXT>,
    cp_index                TINYINT,
    PRIMARY KEY ( id, name )
) WITH COMPACTION = { 'class' : 'LeveledCompactionStrategy' } AND CACHING = {'enabled': 'true'};

CREATE INDEX IF NOT EXISTS location_by_external_id ON core.location (VALUES (external_identification));
CREATE INDEX IF NOT EXISTS location_by_cp_name_search ON core.location (VALUES (cp_name_search));
CREATE INDEX IF NOT EXISTS location_by_name_search ON core.location (VALUES (name_search));


CREATE MATERIALIZED VIEW core.locations_by_entity
AS
SELECT
    entity_id
  , name
  , id
  , address
  , cp_index
  , cp_name_search
  , created_date_time
  , enabled
  , external_identification
  , group_names
  , modified_date_time
  , name_search
  , timezone
FROM core.location
WHERE
      entity_id IS NOT NULL
  AND id IS NOT NULL
  AND name IS NOT NULL
PRIMARY KEY (entity_id, name, id) modified_date_time  TIMESTAMP,
    name_search         LIST<TEXT>,
    event_cdc_date_time TIMESTAMP,
    events              TEXT,
    PRIMARY KEY ( ID, name )
)
WITH
CACHING = {'enabled': 'true'};
CREATE INDEX IF NOT EXISTS business_entity_by_name ON core.business_entity (VALUES (name_search));


DROP TABLE IF EXISTS core.entity_groups;

CREATE TABLE IF NOT EXISTS core.entity_groups
(
    entity_id          UUID,
    id                 UUID,
    name               TEXT,
    locations          MAP<UUID, TEXT>,
    description        TEXT,
    enabled            BOOLEAN,
    created_date_time  TIMESTAMP,
    modified_date_time TIMESTAMP,
    PRIMARY KEY ( entity_id, name)
) WITH CACHING = { 'enabled': 'true' };
CREATE INDEX IF NOT EXISTS entity_groups_by_id ON core.entity_groups ((entity_id), ID);

DROP TABLE IF EXISTS core.location;

CREATE TABLE IF NOT EXISTS core.location
(
    id                      UUID,
    name                    TEXT,
    entity_id               UUID,
    group_names             SET<TEXT>,
    timezone                TEXT,
    address                 FROZEN<location_address>,
    enabled                 BOOLEAN,
    created_date_time       TIMESTAMP,
    modified_date_time      TIMESTAMP,
    external_identification MAP<TEXT, INT>,
    cp_name_search          LIST<TEXT>,
    name_search             LIST<TEXT>,
    cp_index                TINYINT,
    PRIMARY KEY ( id, name )
) WITH COMPACTION = { 'class' : 'LeveledCompactionStrategy' } AND CACHING = {'enabled': 'true'};

CREATE INDEX IF NOT EXISTS location_by_external_id ON core.location (VALUES (external_identification));
CREATE INDEX IF NOT EXISTS location_by_cp_name_search ON core.location (VALUES (cp_name_search));
CREATE INDEX IF NOT EXISTS location_by_name_search ON core.location (VALUES (name_search));


CREATE MATERIALIZED VIEW core.locations_by_entity
AS
SELECT
    entity_id
  , name
  , id
  , address
  , cp_index
  , cp_name_search
  , created_date_time
  , enabled
  , external_identification
  , group_names
  , modified_date_time
  , name_search
  , timezone
FROM core.location
WHERE
      entity_id IS NOT NULL
  AND id IS NOT NULL
  AND name IS NOT NULL
PRIMARY KEY (entity_id, name, id)