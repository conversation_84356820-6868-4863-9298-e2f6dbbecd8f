CREATE KEYSPACE retention WITH REPLICATION = { 'class' : 'SimpleStrategy', 'replication_factor' : 1 };
DROP MATERIALIZED VIEW IF EXISTS retention.email_dashboard_by_status;
DROP MATERIALIZED VIEW IF EXISTS retention.outbox_entity_blast_campaigns;
DROP MATERIALIZED VIEW IF EXISTS retention.outbox_location_blast_campaigns;
DROP TABLE IF EXISTS retention.email_template_categories;
DROP TABLE IF EXISTS retention.email_template_events;
DROP TABLE IF EXISTS retention.email_templates;
DROP TABLE IF EXISTS retention.entity_blast_campaigns;
DROP TABLE IF EXISTS retention.location_blast_campaigns;
DROP TABLE IF EXISTS retention.email_recipients;

DROP TYPE IF EXISTS retention.recipient_filter;
DROP TYPE IF EXISTS retention.campaign_settings;

CREATE TYPE retention.recipient_filter
    (
        min_spent                    DECIMAL,
        max_spent                    DECIMAL,
        min_visits                   DECIMAL,
        max_visits                   DECIMAL,
        min_order_amount             DECIMAL,
        max_order_amount             DECIMAL,
        start_date                   TIMESTAMP,
        end_date                     TIMESTAMP,
        receive_recommended_services BOOLEAN,
        fleet_accounts_only          BOOLEAN,
        vehicle_makes                LIST<TEXT>
    );

CREATE TYPE retention.campaign_settings
    (
        logo_background       TEXT,
        menu_text_color       TEXT,
        theme_color           TEXT,
        menu_background_color TEXT,
        footer_icon_color     TEXT
    );

CREATE TABLE retention.email_template_categories
(
    id          UUID,
    name        TEXT,
    description TEXT,
    image_url   TEXT, // S3
    PRIMARY KEY ( id, name )
) WITH CLUSTERING ORDER BY (name ASC);

CREATE TABLE retention.email_template_events
(
    category_id   UUID,
    name          TEXT,
    stock_content TEXT,
    image_url     TEXT, //S3
    PRIMARY KEY (category_id, name)
) WITH CLUSTERING ORDER BY (name ASC);

// How templates are added? Do we want to show latest added templates first?
CREATE TABLE retention.email_templates
(
    id                TIMEUUID,
    category_id       UUID,
    event_name        TEXT,
    image_url         TEXT, // S3
    preview_image_url TEXT, // S3
    resources         TEXT,

    PRIMARY KEY (category_id, id, event_name)
) WITH CLUSTERING ORDER BY (id DESC);

CREATE TABLE retention.entity_blast_campaigns
(
    id                TIMEUUID,
    entity_id         UUID,
    category_id       UUID,
    event_name        UUID,
    template_id       UUID,
    locations         SET<UUID>,
    groups            SET<TEXT>,
    name              TEXT,
    display_name      TEXT,
    search_keywords   LIST<TEXT>, // email_event, campaign name, groups
    status            TEXT,       //draft, scheduled, sent
    body_url          TEXT,       // either save entire content here and upload to s3 as background operation
    subject           TEXT,
    sender_email      TEXT,
    send_date         TIMESTAMP,
    recipients_filter FROZEN<recipient_filter>,
    campaign_settings FROZEN<campaign_settings>,
    recipients_count  INT,
    enabled           BOOLEAN,
    created_date      TIMESTAMP,
    modified_date     TIMESTAMP,
    outbox_partition  TIMESTAMP,
    outbox_offset     TIMESTAMP,
    outbox_events     TEXT,

    PRIMARY KEY (entity_id, id)
) WITH CLUSTERING ORDER BY (id DESC);

-- CREATE MATERIALIZED VIEW retention.email_dashboard_by_status
-- AS
--     SELECT id, entity_id, name, groups, send_date
--     FROM retention.entity_blast_campaigns
--     WHERE status IS NOT NULL AND id IS NOT NULL
--     PRIMARY KEY ( (entity_id, status), id) WITH CLUSTERING ORDER BY (id DESC );

CREATE INDEX blast_campaigns_search_keywords ON retention.entity_blast_campaigns (search_keywords);

CREATE MATERIALIZED VIEW retention.outbox_entity_blast_campaigns
AS
SELECT
    id
  , entity_id
  , outbox_partition
  , outbox_offset
  , outbox_events
FROM retention.entity_blast_campaigns
WHERE
      outbox_partition IS NOT NULL
  AND id IS NOT NULL
PRIMARY KEY ( (outbox_partition), entity_id, id ) WITH CLUSTERING ORDER BY (id ASC);

// Created to simplify creation of same campaign on retention api side. campaign `id` will be stored in retention api. This will help mapping email log events(read/converted/unsubscribed) without the need to keep external identification.
CREATE TABLE retention.location_blast_campaigns
(
    id                 TIMEUUID,
    entity_id          UUID,
    entity_campaign_id TIMEUUID,
    location_id        UUID,
    send_date          TIMESTAMP,
    recipients_count   INT,
    read_count         INT,
    converted_count    INT,
    confirmed_count    INT,
    unsubscribed_count INT,
    enabled            BOOLEAN,
    created_date       TIMESTAMP,
    modified_date      TIMESTAMP,
    outbox_partition   TIMESTAMP,
    outbox_offset      TIMESTAMP,
    outbox_events      TEXT,

    PRIMARY KEY ( id )
);

CREATE MATERIALIZED VIEW retention.outbox_location_blast_campaigns
AS
SELECT
    id
  , entity_campaign_id
  , location_id
  , outbox_partition
  , outbox_offset
  , outbox_events
FROM retention.location_blast_campaigns
WHERE
      outbox_partition IS NOT NULL
  AND id IS NOT NULL
PRIMARY KEY ( outbox_partition, id )
WITH CLUSTERING ORDER BY (id ASC);

CREATE TABLE retention.email_recipients
(
    location_campaign_id TIMEUUID,
    entity_id            UUID,
    entity_campaign_id   TIMEUUID,
    email_address        TEXT,
    customer_id          TEXT,
    first_name           TEXT,
    last_name            TEXT,
    vehicle_make         TEXT,
    vehicle_model        TEXT,
    vehicle_year         TEXT,
    phones               LIST<TEXT>,
    read                 BOOLEAN,
    converted            BOOLEAN,
    confirmed            BOOLEAN,
    unsubscribed         BOOLEAN,
    reviewed             BOOLEAN,
    approved             BOOLEAN,
    PRIMARY KEY ( location_campaign_id, email_address)
);