namespace WebApi.IntegrationTests;

using Kukui.Infrastructure.Testing.IntegrationTests.AppFactories;
using Kukui.Infrastructure.Testing.xUnit.AssemblyFixture;
using Microsoft.Extensions.DependencyInjection;
using Testcontainers.PostgreSql;

public class TestApplication
    :
#if NET6_0_OR_GREATER
        WebAppFactory<IntegrationTestEnvironment, Program>,
#else
    WebAppFactory<IntegrationTestEnvironment, Startup>,
#endif
        IAssemblyFixture<TestApplication>
{
    protected override string EnvironmentName => "Development";

    public TestApplication(IntegrationTestEnvironment environment) : base(environment)
    {
    }

    protected override void ConfigureServices(IServiceCollection services)
    {
    }

    protected override void UpdateAppConfiguration(Dictionary<string, string?> configuration)
    {
        var pgContainer = Environment.GetContainer<PostgreSqlContainer>();
        configuration.Add("ConnectionStrings:RetentionContext", pgContainer.GetConnectionString());
    }
}