namespace WebApi.IntegrationTests;

using Kukui.Infrastructure.Testing.Containers.Database;
using Kukui.Infrastructure.Testing.IntegrationTests;
using Kukui.Infrastructure.Testing.xUnit.AssemblyFixture;

public class IntegrationTestEnvironment : TestEnvironmentBase, IAssemblyFixture<IntegrationTestEnvironment>
{
    public IntegrationTestEnvironment()
    {
        Configure(
            builder => builder.AddPostgreSql("retention_admin", "p@ssw0rd", "retention")

            // .AddRabbitMq()
            // .AddRedis()
        );
    }
}