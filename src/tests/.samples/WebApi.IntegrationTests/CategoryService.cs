using WebApi.Models;
using WebApi.Services.Interfaces;

namespace WebApi.IntegrationTests;

public class CategoryServiceStub : ICategoryService
{
    public Task<IEnumerable<CategoryResponseObject>> GetCategories()
    {
        return Task.FromResult<IEnumerable<CategoryResponseObject>>(
            new List<CategoryResponseObject>
            {
                new() { Id = Guid.NewGuid(), Name = "Category 1" },
                new() { Id = Guid.NewGuid(), Name = "Category 2" }
            });
    }
}