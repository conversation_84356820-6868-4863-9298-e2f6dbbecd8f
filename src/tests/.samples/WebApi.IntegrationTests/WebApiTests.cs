namespace WebApi.IntegrationTests;

using FluentAssertions;

public class WebApiTests
{
    private readonly TestApplication _application;

    public WebApiTests(TestApplication application)
    {
        _application = application;
    }

    [Fact]
    public async Task GetCategories_returns_categories()
    {
        var response = await _application.CreateClient().GetAsync("api/Categories");

        response.IsSuccessStatusCode.Should().BeTrue();
    }
}