using FluentAssertions;

namespace WebApi.IntegrationTests;

public class WebApiTests3
{
    private readonly TestApplication _application;

    public WebApiTests3(TestApplication application)
    {
        _application = application;
    }

    [Fact]
    public async Task GetCategories_returns_categories()
    {
        var response = await _application
            .CreateClient()
            .GetAsync("api/Categories");

        response
            .IsSuccessStatusCode.Should()
            .BeTrue();
    }
}