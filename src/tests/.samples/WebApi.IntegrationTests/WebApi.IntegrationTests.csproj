<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Testing.Containers.Database\Testing.Containers.Database.csproj" />
    <ProjectReference Include="..\..\src\Testing.Containers.MessageBus\Testing.Containers.MessageBus.csproj" />
    <ProjectReference Include="..\..\src\Testing.Containers.Redis\Testing.Containers.Redis.csproj" />
    <ProjectReference Include="..\..\src\Testing.Containers.WireMock\Testing.Containers.WireMock.csproj" />
    <ProjectReference Include="..\..\src\Testing.IntegrationTests\Testing.IntegrationTests.csproj" />
    <ProjectReference Include="..\WebApi\WebApi.csproj" />
  </ItemGroup>

</Project>