using Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;
using Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;
using Kukui.Infrastructure.Hosting.Common.Models;
using Kukui.Infrastructure.Hosting.Server;
using Microsoft.Extensions.DependencyInjection;

await ServerHostRunner.RunApp(args, RegisterServices);

return;

void RegisterServices(IServiceCollection services, ServerConfiguration configuration)
{
    services.AddScyllaDb(configuration, options => options.AddMappings(typeof(Program).Assembly));
}

public partial class Program
{
}