<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\database\src\ScyllaDb.Hosting.Extensions\ScyllaDb.Hosting.Extensions.csproj" />
    <ProjectReference Include="..\..\..\hosting\src\Kukui.Hosting.Server\Kukui.Hosting.Server.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="configs\config.json" />
    <Content Include="configs\config.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
