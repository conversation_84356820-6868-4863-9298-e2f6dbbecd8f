namespace WebApi.ScyllaDb;

using Kukui.Infrastructure.Database.ScyllaDb.Hosting.Extensions;
using Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;
using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.Hosting.Web.Common;
using Kukui.Infrastructure.MessageBus.Nats.JetStream;

public class WebServerConfigurator : WebConfiguratorBase
{
    public override void RegisterServices(IServiceCollection services, WebServerConfiguration configuration)
    {
        services.AddNatsJetStream(configuration.ServerConfiguration)
            .AddScyllaDb(
                configuration.ServerConfiguration,
                config => config.AddMappings(typeof(WebServerConfigurator).Assembly));
    }
}