using Kukui.Infrastructure.Hosting.Web;
using Kukui.Infrastructure.MessageBus.Abstractions.Consumers;
using Kukui.Infrastructure.MessageBus.Abstractions.Events;
using Kukui.Infrastructure.MessageBus.Abstractions.Publisher;
using Kukui.Infrastructure.MessageBus.Abstractions.Streams;
using Microsoft.AspNetCore.Mvc;
using WebApi.ScyllaDb;

await WebHostRunner.RunApp<WebServerConfigurator>(
    args,
    (app, _) =>
    {
        app.MapGet(
                "entities",
                async ([FromServices] IPublisher<LocationCreated> publisher, [FromQuery] string name) =>
                {
                    await publisher.PublishAsync(
                        new LocationCreated(
                            Guid.NewGuid()
                                .ToString()) { Name = "test-name" });

                    Results.Ok();
                })
            .WithName("get-entities")
            .WithOpenApi();
    });

public class RetentionStream : NatsStreamBase
{
}

public class LocationCreated : EventBase<RetentionStream>
{
    public LocationCreated(string id) : base(id)
    {
    }

    public string Name { get; set; }
}

public class LocationCreatedConsumer : IEventConsumer<LocationCreated>
{
    public Task ConsumeAsync(ConsumeContext<LocationCreated> context, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }
}