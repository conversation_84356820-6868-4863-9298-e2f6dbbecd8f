using Kukui.Infrastructure.Database.ScyllaDb.Mapping.Extensions;

namespace WebApi.ScyllaDb.Data;

public class ExternalIdentificationConverter : ScyllaDbTypeConverter<ExternalIdentification, IDictionary<string, int>>
{
    protected override IDictionary<string, int> GetUserDefinedFromDbConverter(ExternalIdentification? entity)
    {
        return entity == null
            ? default!
            : new Dictionary<string, int>
            {
                {
                    nameof(entity.ClientId)
                        .ToSnakeCaseFormatting(),
                    entity.ClientId
                },
                {
                    nameof(entity.LocationId)
                        .ToSnakeCaseFormatting(),
                    entity.LocationId
                }
            };
    }

    protected override ExternalIdentification GetUserDefinedToDbConverter(IDictionary<string, int>? database)
    {
        return database == null
            ? default!
            : new ExternalIdentification
            {
                ClientId = database[nameof(ExternalIdentification.ClientId)
                    .ToSnakeCaseFormatting()],
                LocationId = database[nameof(ExternalIdentification.LocationId)
                    .ToSnakeCaseFormatting()]
            };
    }
}