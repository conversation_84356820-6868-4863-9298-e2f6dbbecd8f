using Cassandra.Mapping;
using Kukui.Infrastructure.Database.ScyllaDb.Abstractions.Mappings;

namespace WebApi.ScyllaDb.Data;

public class LocationEntityMapping : IEntityMapping<LocationEntity>
{
    public void Configure(IEntityMappingBuilder<LocationEntity> builder)
    {
        builder
            .WithTable("location", "core",
                map => map
                    .PartitionKey(x => x.Id)
                    .ClusteringKey(x => x.Name, SortOrder.Ascending))
            .AutoMapColumns(options =>
            {
                options.Column(x => x.CreatedDate, map => map.WithName("created_date_time"));
                options.Column(x => x.ModifiedDate, map => map.WithName("modified_date_time"));
                options.Column(x => x.TimeZone, map => map.WithName("timezone"));
            })
            .WithUdtMap(x => x.Address, map => map
                .WithName("location_address", "core")
                .AutoMap())
            .WithUdtMap(x => x.Address.Coordinates, map => map
                .WithName("geo_coordinate", "core")
                .AutoMap())
            .WithMaterializedView<LocationsByEntityView>("locations_by_entity", "core",
                map => map
                    .PartitionKey(x => x.EntityId)
                    .ClusteringKey(x => x.Name, SortOrder.Ascending)
                    .ClusteringKey(x => x.Id))
            .WithCollectionIndex(x => x.NameSearch, CollectionIndexKind.Values)
            .WithGlobalIndex(x => x.EntityId)
            .WithLocalIndex(x => x.CpIndex);
    }
}

public class LocationEntity
{
    public Guid Id { get; set; }

    public string Name { get; set; }

    public Guid? EntityId { get; set; }

    public ISet<string> GroupNames { get; set; }

    public string TimeZone { get; set; }

    public LocationAddress Address { get; set; }

    public bool Enabled { get; set; }

    public DateTimeOffset CreatedDate { get; set; }

    public DateTimeOffset? ModifiedDate { get; set; }

    public IDictionary<string, int> ExternalIdentification { get; set; }

    public List<string> CpNameSearch { get; set; }

    public List<string> NameSearch { get; set; }

    public sbyte CpIndex { get; set; }
}

public class LocationAddress
{
    public string Country { get; set; }

    public string Region { get; set; }

    public string City { get; set; }

    public string PostalCode { get; set; }

    public string Street { get; set; }

    public Coordinates Coordinates { get; set; }
}

public class Coordinates
{
    public float? Latitude { get; set; }

    public float? Longitude { get; set; }
}