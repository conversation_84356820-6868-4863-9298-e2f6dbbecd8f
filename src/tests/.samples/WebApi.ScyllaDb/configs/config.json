{"DisplayName": "Web Api ScyllaDB", "Description": "Web Api ScyllaDB", "Security": {"EnforceHttps": false, "AllowedHosts": "*", "AllowedCorsOrigins": ["https://localhost", "https://*.server.cluster", "https://*.kukui.com"], "Authentication": {"Enabled": false, "IdentityServerHost": "http://docker-sandbox.server.cluster:8100/auth/realms/ARX/"}}, "Kestrel": {"AddServerHeader": false, "Endpoints": {"Http": {"Url": "http://localhost:5050"}}}, "Logging": {"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Debug"}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"ServerUrl": "http://localhost:5341", "formatter": "Serilog.Formatting.Json.JsonFormatter"}}]}}, "Server": {"DataBase": {"ConnectionString": "secret", "DefaultPageSize": 100, "DefaultKeyspace": "core", "ReconnectionPolicy": {"MinDelay": "00:00:02", "MaxDelay": "00:00:05"}, "Metrics": {"Enabled": false}}, "MessageService": {"Url": "nats://localhost:4222", "Username": "admin", "Password": "secret"}, "Vault": {"Enabled": false, "Address": "http://docker-sandbox.server.cluster:8200/", "Sections": [{"Name": "datadog", "ConfigurationSectionName": "Server:Database:Metrics:DatadogConfig", "Keys": [{"Source": "api-key", "Target": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"Name": "database", "ConfigurationSectionName": "server:Database", "Keys": [{"Source": "core-scylladb", "Target": "ConnectionString"}]}, {"Name": "nats", "ConfigurationSectionName": "server:MessageService", "Keys": [{"Source": "password", "Target": "Password"}]}]}}}