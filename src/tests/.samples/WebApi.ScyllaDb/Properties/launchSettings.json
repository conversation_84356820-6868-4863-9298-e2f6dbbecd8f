{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "", "applicationUrl": "http://localhost:5010", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger", "applicationUrl": "https://localhost:5011;http://localhost:5010", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}