namespace Kukui.Infrastructure.Testing.Containers.Database;

using Abstractions;
using DotNet.Testcontainers.Builders;
using ScyllaDb;
using Testcontainers.MsSql;
using Testcontainers.PostgreSql;

public static class TestContainerBuilderExtensions
{
    public static TestContainerBuilder AddPostgreSql(
        this TestContainerBuilder builder,
        string username,
        string password,
        string database = "postgres")
    {
        builder.ContainersMetaData.Add(
            new ContainerMetaData(
                new PostgreSqlBuilder().WithImage("postgres:16.3")
                    .WithUsername(username)
                    .WithPassword(password)
                    .WithDatabase(database)
                    .Build()));

        return builder;
    }

    public static TestContainerBuilder AddMssql(this TestContainerBuilder builder, params string[] databases)
    {
        const int msSqlPort = 1433;
        builder.ContainersMetaData.Add(
            new ContainerMetaData(
                new MsSqlBuilder().WithImage("mcr.microsoft.com/mssql/server:2019-CU30-ubuntu-20.04")
                    .WithPassword("P@ssw0rd")
                    .WithStartupCallback(
                        (container, cancellationToken) => CreateDatabases(container, databases, cancellationToken))
                    .WithPortBinding(msSqlPort, true)
                    .WithWaitStrategy(Wait.ForUnixContainer().UntilPortIsAvailable(msSqlPort))
                    .Build()));
        return builder;
    }

    public static TestContainerBuilder AddScyllaDb(this TestContainerBuilder builder)
    {
        builder.ContainersMetaData.Add(new ContainerMetaData(new ScyllaDbBuilder().Build()));

        return builder;
    }

    private static async Task CreateDatabases(
        MsSqlContainer container,
        string[] databases,
        CancellationToken cancellationToken)
    {
        await Task.WhenAll(
            databases.Select(database => container.ExecScriptAsync($"CREATE DATABASE {database}", cancellationToken)));
    }
}