namespace Kukui.Infrastructure.Testing.Containers.Abstractions;

using Microsoft.Data.SqlClient;
using Testcontainers.MsSql;

public static class MsSqlContainerExtensions
{
    public static string GetConnectionString(this MsSqlContainer container, string database) =>
        new SqlConnectionStringBuilder(container.GetConnectionString())
        {
            InitialCatalog = database
        }.ConnectionString;
}