<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Containers.Build.props', '$(MSBuildThisFileDirectory)/'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Testing.Containers.Database</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Testing.Containers.Database</RootNamespace>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" />
    <PackageReference Include="Testcontainers.MsSql" />
    <PackageReference Include="Testcontainers.PostgreSql" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Testing.Containers.Abstractions\Testing.Containers.Abstractions.csproj" />
    <ProjectReference Include="..\Testing.Containers.Database.ScyllaDb\Testing.Containers.Database.ScyllaDb.csproj" />
  </ItemGroup>

</Project>