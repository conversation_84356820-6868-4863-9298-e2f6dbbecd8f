using System.Text;
using DotNet.Testcontainers.Configurations;
using DotNet.Testcontainers.Containers;

namespace Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;

public class ScyllaDbContainer : DockerContainer, IDatabaseContainer
{
    public ScyllaDbContainer(IContainerConfiguration configuration) : base(configuration)
    {
    }

    public string GetConnectionString()
    {
        var properties = new Dictionary<string, string>
        {
            { "Contact Points", Hostname + ":" + GetMappedPublicPort(ScyllaDbBuilder.ScyllaDbPort) },
            { "username", ScyllaDbBuilder.DefaultUsername },
            { "password", ScyllaDbBuilder.DefaultPassword }
        };
        return string.Join(";", properties.Select(property => string.Join("=", property.Key, property.Value)));
    }

    public async Task<ExecResult> ExecScriptAsync(string scriptContent, CancellationToken ct = default)
    {
        var scriptFilePath =
            string.Join("/", string.Empty, "tmp", Guid
                .NewGuid()
                .ToString("D"), Path.GetRandomFileName());

        await CopyAsync(Encoding.Default.GetBytes(scriptContent), scriptFilePath, Unix.FileMode644, ct)
            .ConfigureAwait(false);

        return await ExecAsync(
                new[]
                {
                    "cqlsh", "-u", ScyllaDbBuilder.DefaultUsername, "-p", ScyllaDbBuilder.DefaultPassword, "-f",
                    scriptFilePath
                }, ct)
            .ConfigureAwait(false);
    }

    public async Task<ExecResult> ImportCsvFile(string keyspace, string tableName, string csvContent,
        CancellationToken ct = default)
    {
        var scriptFilePath =
            string.Join("/", string.Empty, "tmp", Guid
                    .NewGuid()
                    .ToString("D"), $"{Path.GetRandomFileName()}.csv");

        await CopyAsync(Encoding.Default.GetBytes(csvContent), scriptFilePath, Unix.FileMode644, ct)
            .ConfigureAwait(false);

        return await ExecAsync(
                new[]
                {
                    "cqlsh", "-u", ScyllaDbBuilder.DefaultUsername, "-p", ScyllaDbBuilder.DefaultPassword, "-e",
                    $"COPY {keyspace}.{tableName} FROM '{scriptFilePath}' WITH DELIMITER=',' AND HEADER=TRUE;"
                }, ct)
            .ConfigureAwait(false);
    }

    public async Task<string> ExportCsvFile(string keyspace, string tableName, CancellationToken ct = default)
    {
        var exportDirectory =
            string.Join("/", string.Empty, "tmp", Guid
                .NewGuid()
                .ToString("D"));

        var scriptFilePath = Path.Combine(exportDirectory, $"{Path.GetRandomFileName()}.csv");

        await ExecAsync(new[] { "mkdir", "-p", exportDirectory }, ct)
            .ConfigureAwait(false);

        await ExecAsync(
                new[]
                {
                    "cqlsh", "-u", ScyllaDbBuilder.DefaultUsername, "-p", ScyllaDbBuilder.DefaultPassword, "-e",
                    $"COPY {keyspace}.{tableName} TO '{scriptFilePath}' WITH DELIMITER=',' AND HEADER=TRUE;"
                }, ct)
            .ConfigureAwait(false);

        return Encoding.UTF8.GetString(await ReadFileAsync(scriptFilePath, ct)
            .ConfigureAwait(false));
    }
}