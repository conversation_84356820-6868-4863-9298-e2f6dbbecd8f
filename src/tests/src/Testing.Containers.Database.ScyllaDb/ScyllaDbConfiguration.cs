using Docker.DotNet.Models;
using DotNet.Testcontainers.Configurations;

namespace Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;

public class ScyllaDbConfiguration : ContainerConfiguration
{
    public ScyllaDbConfiguration()
    {
    }

    public ScyllaDbConfiguration(IResourceConfiguration<CreateContainerParameters> resourceConfiguration)
        : base(resourceConfiguration)
    {
        // Passes the configuration upwards to the base implementations to create an updated immutable copy.
    }

    public ScyllaDbConfiguration(IContainerConfiguration resourceConfiguration)
        : base(resourceConfiguration)
    {
        // Passes the configuration upwards to the base implementations to create an updated immutable copy.
    }

    public ScyllaDbConfiguration(ScyllaDbConfiguration resourceConfiguration)
        : this(new ScyllaDbConfiguration(), resourceConfiguration)
    {
        // Passes the configuration upwards to the base implementations to create an updated immutable copy.
    }

    public ScyllaDbConfiguration(ScyllaDbConfiguration oldValue, ScyllaDbConfiguration newValue)
        : base(oldValue, newValue)
    {
    }
}