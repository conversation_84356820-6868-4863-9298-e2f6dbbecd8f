namespace Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb;

using Docker.DotNet.Models;
using DotNet.Testcontainers.Builders;
using DotNet.Testcontainers.Configurations;
using DotNet.Testcontainers.Containers;

public class ScyllaDbBuilder : ContainerBuilder<ScyllaDbBuilder, ScyllaDbContainer, ScyllaDbConfiguration>
{
    public const string ScyllaDbImage = "scylladb/scylla:6.2.0";

    public const ushort ScyllaDbPort = 9042;

    public const string DefaultUsername = "cassandra";

    public const string DefaultPassword = "cassandra";

    public ScyllaDbBuilder() : this(new ScyllaDbConfiguration())
    {
        DockerResourceConfiguration = Init().DockerResourceConfiguration;
    }

    private ScyllaDbBuilder(ScyllaDbConfiguration dockerResourceConfiguration) : base(dockerResourceConfiguration)
    {
        DockerResourceConfiguration = dockerResourceConfiguration;
    }

    protected override ScyllaDbConfiguration DockerResourceConfiguration { get; }

    protected override ScyllaDbBuilder Clone(IResourceConfiguration<CreateContainerParameters> resourceConfiguration) =>
        Merge(DockerResourceConfiguration, new ScyllaDbConfiguration(resourceConfiguration));

    protected override ScyllaDbBuilder Merge(ScyllaDbConfiguration oldValue, ScyllaDbConfiguration newValue) =>
        new(new ScyllaDbConfiguration(oldValue, newValue));

    protected override ScyllaDbBuilder Clone(IContainerConfiguration resourceConfiguration) =>
        Merge(DockerResourceConfiguration, new ScyllaDbConfiguration(resourceConfiguration));

    public override ScyllaDbContainer Build() => new(DockerResourceConfiguration);

    protected sealed override ScyllaDbBuilder Init() =>
        base.Init()
            .WithImage(ScyllaDbImage)
            .WithPortBinding(ScyllaDbPort, true)
            .WithCommand(
                "--smp 1",
                "--memory 2G",
                "--api-address localhost",
                "--authenticator PasswordAuthenticator",
                "--authorizer CassandraAuthorizer")
            .WithWaitStrategy(Wait.ForUnixContainer().AddCustomWaitStrategy(new WaitUntil()));

    private sealed class WaitUntil : IWaitUntil
    {
        private readonly string[] _waitCommand =
            ["cqlsh", "-u", "cassandra", "-p", "cassandra", "-e", "SELECT now() FROM system.local"];

        /// <inheritdoc />
        public async Task<bool> UntilAsync(IContainer container)
        {
            while (true)
            {
                var execResult = await container.ExecAsync(_waitCommand).ConfigureAwait(false);
                if (execResult.ExitCode == 0)
                {
                    return true;
                }

                await Task.Delay(TimeSpan.FromSeconds(5));
            }
        }
    }
}