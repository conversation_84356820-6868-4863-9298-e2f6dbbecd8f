<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Containers.Build.props', '$(MSBuildThisFileDirectory)/'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Testing.Containers.Database.ScyllaDb</RootNamespace>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Testing.Containers.Abstractions\Testing.Containers.Abstractions.csproj" />
  </ItemGroup>

</Project>