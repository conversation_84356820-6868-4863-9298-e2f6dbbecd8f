using Kukui.Infrastructure.Testing.Containers.Abstractions;
using Testcontainers.Redis;

namespace Kukui.Infrastructure.Testing.Containers.Redis;

public static class TestContainerBuilderExtensions
{
    public static TestContainerBuilder AddRedis(this TestContainerBuilder containerBuilder)
    {
        containerBuilder.ContainersMetaData.Add(new ContainerMetaData(
            new RedisBuilder()
                .WithImage("redis:7.0.5")
                .Build()
        ));

        return containerBuilder;
    }
}