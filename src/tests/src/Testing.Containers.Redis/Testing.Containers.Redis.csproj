<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Containers.Build.props', '$(MSBuildThisFileDirectory)/'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Testing.Containers.Redis</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Testing.Containers.Redis</RootNamespace>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Testcontainers.Redis" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Testing.Containers.Abstractions\Testing.Containers.Abstractions.csproj" />
  </ItemGroup>

</Project>