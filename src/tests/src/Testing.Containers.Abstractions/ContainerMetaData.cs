using DotNet.Testcontainers.Containers;

namespace Kukui.Infrastructure.Testing.Containers.Abstractions;

public class ContainerMetaData
{
    public ContainerMetaData(IContainer container, string serviceName)
    {
        Container = container;
        ServiceName = serviceName;
    }

    public ContainerMetaData(IContainer container)
        : this(container, string.Empty)
    {
    }

    public IContainer Container { get; }

    public string ServiceName { get; }
}