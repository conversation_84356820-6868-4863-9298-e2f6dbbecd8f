<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Containers.Build.props', '$(MSBuildThisFileDirectory)/'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Testing.Containers.Keycloak</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Testing.Containers.Keycloak</RootNamespace>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Testcontainers.Keycloak" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Testing.Containers.Abstractions\Testing.Containers.Abstractions.csproj" />
  </ItemGroup>

</Project>