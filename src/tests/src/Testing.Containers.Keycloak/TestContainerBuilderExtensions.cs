using Kukui.Infrastructure.Testing.Containers.Abstractions;
using Testcontainers.Keycloak;

namespace Kukui.Infrastructure.Testing.Containers.Keycloak;

public static class TestContainerBuilderExtensions
{
    public static TestContainerBuilder AddKeycloak(this TestContainerBuilder builder,
        Action<KeycloakBuilder>? configure = null)
    {
        var containerBuilder = new KeycloakBuilder();
        builder.ContainersMetaData.Add(new ContainerMetaData(containerBuilder.Build()));

        return builder;
    }
}