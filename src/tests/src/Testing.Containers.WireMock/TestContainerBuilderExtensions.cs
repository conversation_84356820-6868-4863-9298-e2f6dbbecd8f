using Kukui.Infrastructure.Testing.Containers.Abstractions;
using WireMock.Net.Testcontainers;

namespace Kukui.Infrastructure.Testing.Containers.WireMock;

public static class TestContainerBuilderExtensions
{
    public static TestContainerBuilder AddWireMock(this TestContainerBuilder containerBuilder,
        string serviceName,
        string mappingFilePath)
    {
        containerBuilder.ContainersMetaData.Add(new ContainerMetaData(
            new WireMockContainerBuilder()
                .WithImage("wiremock/wiremock:latest")
                .WithEnvironment("WIREMOCK_OPTIONS", "--https-port 8443 --verbose --global-response-templating")
                .WithMappings(mappingFilePath)
                .WithWatchStaticMappings(true)
                .Build(),
            serviceName)
        );

        return containerBuilder;
    }
}