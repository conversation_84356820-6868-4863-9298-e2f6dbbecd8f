namespace Kukui.Infrastructure.Testing.UnitTests;

using Xunit;

/// <summary>
/// Base test class for unit tests following Arrange, Act, Assert and SystemUnderTest patterns
/// </summary>
/// <typeparam name="TSystemUnderTest"></typeparam>
public abstract class UnitTestBase<TSystemUnderTest> : TestBase<TSystemUnderTest>, IAsyncLifetime
{
    public virtual Task InitializeAsync()
    {
        SetupMocks();
        SystemUnderTest = CreateSystemUnderTest();
        return Task.CompletedTask;
    }

    public virtual Task DisposeAsync()
    {
        return Task.CompletedTask;
    }
}