namespace Kukui.Infrastructure.Testing.UnitTests;

public abstract class TestBase<TSystemUnderTest>
{
    /// <summary>
    /// Instance of current system under test
    /// </summary>
    protected TSystemUnderTest SystemUnderTest { get; set; } = default!;

    protected abstract TSystemUnderTest CreateSystemUnderTest();

    /// <summary>
    /// Setup all mock dependencies
    /// </summary>
    protected virtual void SetupMocks()
    {
    }
}