<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Containers.Build.props', '$(MSBuildThisFileDirectory)/'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Testing.Containers.MessageBus</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Testing.Containers.MessageBus</RootNamespace>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Testcontainers.Nats" />
    <PackageReference Include="Testcontainers.RabbitMq" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Testing.Containers.Abstractions\Testing.Containers.Abstractions.csproj" />
  </ItemGroup>

</Project>