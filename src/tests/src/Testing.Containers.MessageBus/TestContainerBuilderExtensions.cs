namespace Kukui.Infrastructure.Testing.Containers.MessageBus;

using Abstractions;
using Testcontainers.Nats;
using Testcontainers.RabbitMq;

public static class TestContainerBuilderExtensions
{
    public static TestContainerBuilder AddNats(this TestContainerBuilder builder)
    {
        var natsContainer = new NatsBuilder().WithImage("nats:2.10.22")
            .WithPortBinding(4222, true)
            .WithCommand("-p", "4222", "-js")
            .Build();
        builder.ContainersMetaData.Add(new ContainerMetaData(natsContainer));

        return builder;
    }

    public static TestContainerBuilder AddRabbitMq(this TestContainerBuilder builder)
    {
        builder.ContainersMetaData.Add(
            new ContainerMetaData(
                new RabbitMqBuilder().WithImage("rabbitmq:3.13.3")
                    .WithPortBinding(5672, true)
                    .WithPortBinding(15672, true)
                    .WithUsername("guest")
                    .WithPassword("guest")
                    .Build()));

        return builder;
    }
}