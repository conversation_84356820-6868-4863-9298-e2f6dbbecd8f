namespace Kukui.Infrastructure.Testing.IntegrationTests;

using Containers.Abstractions;
using DotNet.Testcontainers.Containers;

public abstract class TestEnvironmentBase : ITestEnvironment
{
    private Dictionary<Type, ContainerMetaData[]> _testContainers = new();

    protected virtual string ScriptsPath => "scripts";

    public TContainer GetContainer<TContainer>()
        where TContainer : IContainer =>
        (TContainer) _testContainers[typeof(TContainer)].Last().Container;

    public TContainer GetWireMockContainer<TContainer>(string serviceName)
        where TContainer : IContainer
    {
        return (TContainer) _testContainers[typeof(TContainer)]
            .Last(x => x.ServiceName.Equals(serviceName, StringComparison.OrdinalIgnoreCase))
            .Container;
    }

    public IEnumerable<TContainer> GetContainers<TContainer>()
        where TContainer : IContainer
    {
        return _testContainers[typeof(TContainer)].Select(x => x.Container).Cast<TContainer>();
    }

    public async Task InitializeAsync()
    {
        foreach (var containerMeta in _testContainers.SelectMany(x => x.Value))
        {
            await containerMeta.Container.StartAsync().ConfigureAwait(false);
        }

        await AfterInitializationAsync().ConfigureAwait(false);
    }

    public async Task DisposeAsync()
    {
        foreach (var containerMeta in _testContainers.SelectMany(x => x.Value))
        {
            await containerMeta.Container.DisposeAsync().AsTask().ConfigureAwait(false);
        }

        await AfterDisposeAsync().ConfigureAwait(false);
    }

    protected void Configure(Action<TestContainerBuilder> configure)
    {
        var builder = new TestContainerBuilder();
        configure.Invoke(builder);
        _testContainers = builder.ContainersMetaData.GroupBy(k => k.Container.GetType())
            .ToDictionary(k => k.Key, v => v.ToArray());
    }

    protected virtual Task AfterInitializationAsync() => Task.CompletedTask;

    protected virtual Task AfterDisposeAsync() => Task.CompletedTask;

    protected async IAsyncEnumerable<string> GetScriptFilesContent(
        string? subDirectory = null,
        string? searchPattern = null)
    {
        var scriptsDirectory = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory,
            ScriptsPath,
            subDirectory ?? string.Empty);

        if (!Directory.Exists(scriptsDirectory))
        {
            yield break;
        }

        var scripts = Directory.GetFiles(scriptsDirectory, searchPattern ?? "*").OrderBy(x => x).ToList();

        foreach (var script in scripts)
        {
            yield return await File.ReadAllTextAsync(script).ConfigureAwait(false);
        }
    }
}