namespace Kukui.Infrastructure.Testing.IntegrationTests;

using DotNet.Testcontainers.Containers;

public interface ITestEnvironment
{
    TContainer GetContainer<TContainer>()
        where TContainer : IContainer;

    IEnumerable<TContainer> GetContainers<TContainer>()
        where TContainer : IContainer;

    TContainer GetWireMockContainer<TContainer>(string serviceName)
        where TContainer : IContainer;
}