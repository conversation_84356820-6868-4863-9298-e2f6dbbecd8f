namespace Kukui.Infrastructure.Testing.IntegrationTests.AppFactories;

using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Xunit;

public abstract class ServerAppFactory<TEnvironment, TEntryPoint> : AppFactoryBase<TEntryPoint>, IAsyncLifetime
    where TEnvironment : TestEnvironmentBase
    where TEntryPoint : class
{
    protected ServerAppFactory(TEnvironment environment)
    {
        Environment = environment;
    }

    protected TEnvironment Environment { get; }

    public async Task InitializeAsync()
    {
        await Environment.InitializeAsync();
        await AfterTestServerStartAsync();
    }

    public new async Task DisposeAsync()
    {
        await Environment.DisposeAsync();
        await ((WebApplicationFactory<TEntryPoint>) this).DisposeAsync();
    }

    protected sealed override void ConfigureWebHost(IWebHostBuilder builder)
    {
        base.ConfigureWebHost(builder);
        builder.Configure(_ => { });
    }

    protected virtual Task AfterTestServerStartAsync() => Task.CompletedTask;
}