namespace Kukui.Infrastructure.Testing.IntegrationTests.AppFactories;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

public abstract class AppFactoryBase<TEntryPoint> : WebApplicationFactory<TEntryPoint>
    where TEntryPoint : class
{
    protected virtual string ConfigJsonFileName => "config.json";

    protected virtual string EnvironmentName => "IntegrationTests";

    protected virtual void UpdateAppConfiguration(Dictionary<string, string?> configuration)
    {
    }

    protected virtual void ConfigureServices(IServiceCollection services)
    {
    }

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureLogging(logBuilder =>
        {
            logBuilder.ClearProviders();
            logBuilder.AddConsole().SetMinimumLevel(LogLevel.Information);
        });

        builder.ConfigureServices(services =>
        {
            services.AddControllers().AddApplicationPart(typeof(TEntryPoint).Assembly);
            services.AddSingleton<IAuthorizationHandler, AllowAnonymousHandler>();
            ConfigureServices(services);
        });
    }

    protected override IHost CreateHost(IHostBuilder builder)
    {
        builder.UseContentRoot(AppDomain.CurrentDomain.BaseDirectory);
        builder.UseEnvironment(EnvironmentName);
        builder.ConfigureHostConfiguration(configBuilder =>
        {
            configBuilder.Sources.Clear();
            configBuilder.SetBasePath(AppDomain.CurrentDomain.BaseDirectory);
            configBuilder.AddJsonFile(ConfigJsonFileName, true);
            var testConfiguration = new Dictionary<string, string?>();
            UpdateAppConfiguration(testConfiguration);

            Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", EnvironmentName);
            Environment.SetEnvironmentVariable("DOTNET_ENVIRONMENT", EnvironmentName);
            foreach (var keyValue in testConfiguration)
            {
                Environment.SetEnvironmentVariable(keyValue.Key.Replace(":", "__"), keyValue.Value);
            }

            configBuilder.AddEnvironmentVariables();
        });

        return base.CreateHost(builder);
    }
}