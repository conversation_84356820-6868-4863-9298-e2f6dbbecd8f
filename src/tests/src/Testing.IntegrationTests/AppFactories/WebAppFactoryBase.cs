namespace Kukui.Infrastructure.Testing.IntegrationTests.AppFactories;

using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Hosting;
using Xunit;

public abstract class WebAppFactoryBase<TEntryPoint> : AppFactoryBase<TEntryPoint>, IAsyncLifetime
    where TEntryPoint : class
{
    private readonly Uri _baseAddress;

    protected WebAppFactoryBase(int? portNumber = 0)
    {
        _baseAddress = portNumber == default ? new Uri("http://localhost") : new Uri($"http://localhost:{portNumber}");
    }

    public HttpClient HttpClient { get; set; } = default!;

    public virtual async Task InitializeAsync()
    {
        HttpClient = CreateClient(
            new WebApplicationFactoryClientOptions
            {
                AllowAutoRedirect = false,
                BaseAddress = _baseAddress
            });

        await AfterTestServerStartAsync();
    }

    public new virtual async Task DisposeAsync()
    {
        await ((WebApplicationFactory<TEntryPoint>) this).DisposeAsync();
    }

    protected sealed override void ConfigureWebHost(IWebHostBuilder builder)
    {
        base.ConfigureWebHost(builder);
        if (!_baseAddress.IsDefaultPort) builder.UseUrls(_baseAddress.ToString());
    }

    protected sealed override IHost CreateHost(IHostBuilder builder)
    {
        var host = base.CreateHost(builder);

        if (_baseAddress.IsDefaultPort) return host;

        var testHost = builder.Build();

        builder.ConfigureWebHost(webHostBuilder => webHostBuilder.UseKestrel()).Build().Start();

        return testHost;
    }

    protected virtual Task AfterTestServerStartAsync() => Task.CompletedTask;
}