namespace Kukui.Infrastructure.Testing.IntegrationTests.AppFactories;

public abstract class WebAppFactory<TEnvironment, TEntryPoint> : WebAppFactoryBase<TEntryPoint>
    where TEnvironment : TestEnvironmentBase
    where TEntryPoint : class
{
    protected WebAppFactory(TEnvironment environment, int? port = null) : base(port)
    {
        Environment = environment;
    }

    protected TEnvironment Environment { get; }

    public sealed override async Task InitializeAsync()
    {
        await Environment.InitializeAsync();
        await base.InitializeAsync();
    }

    public sealed override async Task DisposeAsync()
    {
        await base.DisposeAsync();
        await Environment.DisposeAsync();
    }
}