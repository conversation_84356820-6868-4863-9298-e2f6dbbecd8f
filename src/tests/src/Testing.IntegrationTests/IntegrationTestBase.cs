namespace Kukui.Infrastructure.Testing.IntegrationTests;

using UnitTests;
using Xunit;

public abstract class IntegrationTestBase : IAsyncLifetime
{
    public Task InitializeAsync() => SetupTestAsync();

    public async Task DisposeAsync()
    {
        await CleanTestAsync();
    }

    protected virtual Task SetupTestAsync() => Task.CompletedTask;

    protected virtual Task CleanTestAsync() => Task.CompletedTask;
}

public abstract class IntegrationTestBase<TSystemUnderTest> : UnitTestBase<TSystemUnderTest>
{
    protected virtual Task SetupTestAsync() => Task.CompletedTask;

    protected virtual Task CleanTestAsync() => Task.CompletedTask;

    public sealed override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await SetupTestAsync();
    }

    public sealed override async Task DisposeAsync()
    {
        await base.DisposeAsync();
        await CleanTestAsync();
    }
}