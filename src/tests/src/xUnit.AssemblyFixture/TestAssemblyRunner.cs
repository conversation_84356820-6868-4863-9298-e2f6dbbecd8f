namespace Kukui.Infrastructure.Testing.xUnit.AssemblyFixture;

using Xunit;
using Xunit.Abstractions;
using Xunit.Sdk;

public class TestAssemblyRunner : XunitTestAssemblyRunner
{
    private readonly Dictionary<Type, object?> _assemblyFixtureMappings = new();
    private readonly IAssemblyFixtureResolver _fixtureResolver;

    // ReSharper disable once TooManyDependencies
    public TestAssemblyRunner(
        ITestAssembly testAssembly,
        IEnumerable<IXunitTestCase> testCases,
        IMessageSink diagnosticMessageSink,
        IMessageSink executionMessageSink,
        ITestFrameworkExecutionOptions executionOptions) : base(
        testAssembly,
        testCases,
        diagnosticMessageSink,
        executionMessageSink,
        executionOptions)
    {
        _fixtureResolver = new DefaultAssemblyFixtureResolver((IReflectionAssemblyInfo) TestAssembly.Assembly);
    }

    protected override async Task AfterTestAssemblyStartingAsync()
    {
        await base.AfterTestAssemblyStartingAsync()
            .ConfigureAwait(false);

        await Aggregator.RunAsync(
                async () =>
                {
                    foreach (var fixtureType in _fixtureResolver.AssemblyFixtures.OrderByDescending(x => x.FullName))
                        _assemblyFixtureMappings[fixtureType] = _fixtureResolver.CreateInstance(fixtureType);

                    foreach (var asyncLifetime in _assemblyFixtureMappings.Values.OfType<IAsyncLifetime>())
                        await Aggregator.RunAsync(
                                async () => await asyncLifetime.InitializeAsync()
                                    .ConfigureAwait(false))
                            .ConfigureAwait(false);
                })
            .ConfigureAwait(false);
    }

    protected override async Task BeforeTestAssemblyFinishedAsync()
    {
        await Task.WhenAll(
                _assemblyFixtureMappings.Values
                    .OfType<IAsyncLifetime>()
                    .Select(
                        disposable => Aggregator.RunAsync(
                            async () => await disposable.DisposeAsync()
                                .ConfigureAwait(false))))
            .ConfigureAwait(false);

        await base.BeforeTestAssemblyFinishedAsync()
            .ConfigureAwait(false);
    }

    // ReSharper disable once TooManyArguments
    protected override async Task<RunSummary> RunTestCollectionAsync(
        IMessageBus messageBus,
        ITestCollection testCollection,
        IEnumerable<IXunitTestCase> testCases,
        CancellationTokenSource cancellationTokenSource)
    {
        return await new TestCollectionRunner(
                _assemblyFixtureMappings,
                testCollection,
                testCases,
                DiagnosticMessageSink,
                messageBus,
                TestCaseOrderer,
                new ExceptionAggregator(Aggregator),
                cancellationTokenSource).RunAsync()
            .ConfigureAwait(false);
    }
}