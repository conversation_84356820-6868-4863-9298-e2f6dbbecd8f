<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="$([MSBuild]::GetPathOfFileAbove('Containers.Build.props', '$(MSBuildThisFileDirectory)/'))" />

  <PropertyGroup>
    <AssemblyName>Kukui.Infrastructure.Testing.xUnit.AssemblyFixture</AssemblyName>
    <RootNamespace>Kukui.Infrastructure.Testing.xUnit.AssemblyFixture</RootNamespace>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Console" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="xunit.extensibility.core" />
    <PackageReference Include="xunit.extensibility.execution" />
  </ItemGroup>

</Project>