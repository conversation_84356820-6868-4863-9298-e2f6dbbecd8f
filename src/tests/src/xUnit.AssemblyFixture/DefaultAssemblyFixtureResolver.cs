namespace Kukui.Infrastructure.Testing.xUnit.AssemblyFixture;

using System.Reflection;
using Microsoft.Extensions.Logging;
using Xunit.Abstractions;

public class DefaultAssemblyFixtureResolver : IAssemblyFixtureResolver
{
    public Dictionary<Type, object?> AssemblyFixtureMappings { get; } = new();

    public IEnumerable<Type> AssemblyFixtures { get; }

    public DefaultAssemblyFixtureResolver(IReflectionAssemblyInfo testAssembly) : this(testAssembly.Assembly)
    {
    }

    public DefaultAssemblyFixtureResolver(Assembly testAssembly)
    {
        AssemblyFixtures = new HashSet<Type>(
            testAssembly.GetTypes()
                .Where(
                    type => type.GetInterfaces()
                        .Any(x => x.IsAssignableToGenericType(typeof(IAssemblyFixture<>)))));
    }

    public object? CreateInstance(Type fixtureType)
    {
        var parametersConstructors = fixtureType.GetConstructors()
            .Where(
                x => x.IsPublic && x.GetParameters()
                    .Length > 0)
            .ToArray();

        if (AssemblyFixtureMappings.TryGetValue(fixtureType, out var fixtureInstance) && fixtureInstance != null)
            return AssemblyFixtureMappings[fixtureType];

        if (parametersConstructors.Length == 0)
            AssemblyFixtureMappings[fixtureType] = Activator.CreateInstance(fixtureType);

        foreach (var constructor in parametersConstructors.OrderByDescending(
                     x => x.GetParameters()
                         .Length))
        {
            var parameters = constructor.GetParameters();
            var parameterValues = new object?[parameters.Length];
            for (var i = 0; i < parameters.Length; i++)
            {
                var parameter = parameters[i];
                if (parameter.ParameterType == typeof(ILogger))
                {
                    parameterValues[i] = LoggerFactory.Create(
                            builder => builder.AddConsole()
                                .SetMinimumLevel(LogLevel.Debug))
                        .CreateLogger(fixtureType);
                }
                else if (AssemblyFixtureMappings.TryGetValue(parameter.ParameterType, out var instance))
                {
                    parameterValues[i] = instance;
                }
                else if (AssemblyFixtures.Contains(parameter.ParameterType))
                {
                    parameterValues[i] = CreateInstance(parameter.ParameterType);
                    AssemblyFixtureMappings[parameter.ParameterType] = parameterValues[i];
                }
                else
                {
                    throw new InvalidOperationException(
                        $"The constructor of {fixtureType.FullName} contains an unsupported parameter type {parameter.ParameterType.FullName}");
                }
            }

            AssemblyFixtureMappings[fixtureType] = Activator.CreateInstance(fixtureType, parameterValues);
        }

        return AssemblyFixtureMappings[fixtureType];
    }
}