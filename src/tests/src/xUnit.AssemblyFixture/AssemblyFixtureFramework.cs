namespace Kukui.Infrastructure.Testing.xUnit.AssemblyFixture;

using System.Reflection;
using Xunit.Abstractions;
using Xunit.Sdk;

public class AssemblyFixtureFramework : XunitTestFramework
{
    public const string TypeName = "Kukui.Infrastructure.Testing.xUnit.AssemblyFixture.AssemblyFixtureFramework";
    public const string AssemblyName = "Kukui.Infrastructure.Testing.xUnit.AssemblyFixture";

    public AssemblyFixtureFramework(IMessageSink messageSink) : base(messageSink)
    {
    }

    protected override ITestFrameworkExecutor CreateExecutor(AssemblyName assemblyName)
    {
        return new TestFrameworkExecutor(assemblyName, SourceInformationProvider, DiagnosticMessageSink);
    }
}