namespace Kukui.Infrastructure.Testing.xUnit.AssemblyFixture;

using System.Reflection;
using Xunit.Abstractions;
using Xunit.Sdk;

public class TestCollectionRunner : XunitTestCollectionRunner
{
    private readonly Dictionary<Type, object?> _assemblyFixtureMappings;
    private readonly IMessageSink _diagnosticMessageSink;

    public TestCollectionRunner(
        Dictionary<Type, object?> assemblyFixtureMappings,
        ITestCollection testCollection,
        IEnumerable<IXunitTestCase> testCases,
        IMessageSink diagnosticMessageSink,
        IMessageBus messageBus,
        ITestCaseOrderer testCaseOrderer,
        ExceptionAggregator aggregator,
        CancellationTokenSource cancellationTokenSource) : base(
        testCollection,
        testCases,
        diagnosticMessageSink,
        messageBus,
        testCaseOrderer,
        aggregator,
        cancellationTokenSource)
    {
        _assemblyFixtureMappings = assemblyFixtureMappings;
        _diagnosticMessageSink = diagnosticMessageSink;
    }

    protected override Task<RunSummary> RunTestClassAsync(
        ITestClass testClass,
        IReflectionTypeInfo @class,
        IEnumerable<IXunitTestCase> testCases)
    {
        foreach (var fixtureType in @class.Type
                     .GetTypeInfo()
                     .ImplementedInterfaces
                     .Where(
                         i => i.GetTypeInfo()
                             .IsGenericType && i.GetGenericTypeDefinition() == typeof(IAssemblyFixture<>))
                     .Select(
                         i => i.GetTypeInfo()
                             .GenericTypeArguments
                             .Single())
                     // First pass at filtering out before locking
                     .Where(i => !_assemblyFixtureMappings.ContainsKey(i)))
            // ConcurrentDictionary's GetOrAdd does not lock around the value factory call, so we need
            // to do it ourselves.
            lock (_assemblyFixtureMappings)
            {
                if (!_assemblyFixtureMappings.ContainsKey(fixtureType))
                    Aggregator.Run(
                        () => _assemblyFixtureMappings.Add(fixtureType, CreateAssemblyFixtureInstance(fixtureType)));
            }

        // Don't want to use .Concat + .ToDictionary because of the possibility of overriding types,
        // so instead we'll just let collection fixtures override assembly fixtures.
        var combinedFixtures = new Dictionary<Type, object?>(_assemblyFixtureMappings);
        foreach (var kvp in CollectionFixtureMappings) combinedFixtures[kvp.Key] = kvp.Value;

        // We've done everything we need, so let the built-in types do the rest of the heavy lifting
        return new XunitTestClassRunner(
            testClass,
            @class,
            testCases,
            _diagnosticMessageSink,
            MessageBus,
            TestCaseOrderer,
            new ExceptionAggregator(Aggregator),
            CancellationTokenSource,
            combinedFixtures).RunAsync();
    }

    private object? CreateAssemblyFixtureInstance(Type fixtureType)
    {
        var constructors = fixtureType.GetConstructors();

        if (constructors.Length > 1)
            throw new InvalidOperationException($"The type ${fixtureType.FullName} can only contain one constructor.");

        if (constructors[0]
                .GetParameters()
                .Length == 0)
            return Activator.CreateInstance(fixtureType);

        return Activator.CreateInstance(fixtureType, _diagnosticMessageSink);
    }
}