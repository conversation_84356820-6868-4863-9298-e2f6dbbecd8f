<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net9.0' ">
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.4" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageVersion Include="Scalar.AspNetCore" Version="2.2.1" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net8.0' ">
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.4" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="8.0.15" />
    <PackageVersion Include="Scalar.AspNetCore" Version="2.2.1" />
  </ItemGroup>
  <ItemGroup Condition=" '$(TargetFramework)' == 'net6.0' ">
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="6.0.36" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Relational" Version="6.0.36" />
    <PackageVersion Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.29" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.36" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.36" />
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.36" />
  </ItemGroup>
  <ItemGroup>
    <PackageVersion Include="CassandraCSharpDriver" Version="3.22.0" />
    <PackageVersion Include="App.Metrics.Abstractions" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Datadog" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Reporting.Datadog" Version="4.3.0" />
    <PackageVersion Include="CassandraCSharpDriver.AppMetrics" Version="3.22.0" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageVersion Include="Microsoft.Identity.Client" Version="4.70.2" />
    <PackageVersion Include="Microsoft.Bcl.AsyncInterfaces" Version="9.0.4" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="System.Formats.Asn1" Version="9.0.4" />
  </ItemGroup>
  <ItemGroup>
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.4" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" Version="9.0.4" />
    <PackageVersion Include="Grpc.Net.ClientFactory" Version="2.71.0" />
    <PackageVersion Include="Serilog" Version="4.2.0" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="9.0.0" />
    <PackageVersion Include="Destructurama.Attributed" Version="5.1.0" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageVersion Include="Serilog.Expressions" Version="5.0.0" />
    <PackageVersion Include="Serilog.Exceptions" Version="8.4.0" />
    <PackageVersion Include="Serilog.Enrichers.Demystifier" Version="1.0.3" />
    <PackageVersion Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageVersion Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.Seq" Version="9.0.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageVersion Include="Figgle" Version="0.5.1" />
    <PackageVersion Include="Scrutor" Version="6.0.1" />
    <PackageVersion Include="NSwag.AspNetCore" Version="14.3.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.Nats" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.SqlServer" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.System" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.Uris" Version="9.0.0" />
    <PackageVersion Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageVersion Include="FluentValidation" Version="11.11.0" />
    <PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageVersion Include="MediatR" Version="12.5.0" />
    <PackageVersion Include="VaultSharp" Version="1.17.5.1" />
  </ItemGroup>
  <ItemGroup>
    <PackageVersion Include="CommunityToolkit.HighPerformance" Version="8.4.0" />
    <PackageVersion Include="OneOf" Version="3.0.271" />
    <PackageVersion Include="OneOf.SourceGenerator" Version="3.0.271" />
    <PackageVersion Include="OneOf.Serialization.SystemTextJson" Version="1.1.1" />
    <PackageVersion Include="NATS.Client.Core" Version="2.5.4" />
    <PackageVersion Include="NATS.Client.Serializers.Json" Version="2.5.4" />
    <PackageVersion Include="NATS.Client.JetStream" Version="2.5.2" />
    <PackageVersion Include="NATS.Client.Hosting" Version="2.5.4" />
    <PackageVersion Include="NATS.Client.KeyValueStore" Version="2.5.4" />
    <PackageVersion Include="MassTransit.RabbitMQ" Version="[8.4.1]" />
  </ItemGroup>
  <ItemGroup>
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All" />
    <PackageVersion Include="MinVer" Version="6.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
  </ItemGroup>
 <!-- Hosting -->
 <ItemGroup>
    <PackageVersion Include="Kukui.Infrastructure.Hosting.Server" Version="4.5.0" />
    <PackageVersion Include="Kukui.Infrastructure.Hosting.Web" Version="4.5.0" />
  </ItemGroup>
 <!-- Source Generator -->
 <ItemGroup>
    <PackageVersion Include="Microsoft.CodeAnalysis.CSharp" Version="4.3.1" PrivateAssets="all" />
    <PackageVersion Include="Microsoft.CodeAnalysis.Analyzers" Version="3.11.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="Microsoft.Net.Compilers.Toolset" Version="4.13.0" PrivateAssets="all" />
  </ItemGroup>
</Project>