namespace Kukui.Infrastructure.HealthChecks.ScyllaDb;

using Cassandra;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

public class ScyllaHealthCheck : IHealthCheck
{
    private readonly ILogger<ScyllaHealthCheck> _logger;
    private readonly ISession _session;

    public ScyllaHealthCheck(ISession session, ILogger<ScyllaHealthCheck> logger)
    {
        _session = session;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _session.ExecuteAsync(
                new SimpleStatement("SELECT data_center FROM system.local")
                    .SetConsistencyLevel(ConsistencyLevel.Quorum));

            return HealthCheckResult.Healthy();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Scylla health check failed");
            return HealthCheckResult.Unhealthy();
        }
    }
}