namespace HealthChecks.MassTransit.RabbitMq;

using Microsoft.Extensions.DependencyInjection;

public static class ServiceRegistrations
{
    public static IHealthChecksBuilder AddMassTransitRabbitMq(this IHealthChecksBuilder builder)
    {
        builder.Services.AddSingleton<MassTransitRabbitMqHealthCheck>();

        return builder.AddCheck<MassTransitRabbitMqHealthCheck>(
            "rabbit-mq",
            tags: ["massTransitRabbitMq", "message-service"]);
    }
}