namespace HealthChecks.MassTransit.RabbitMq;

using global::MassTransit;
using Microsoft.Extensions.Diagnostics.HealthChecks;

public class MassTransitRabbitMqHealthCheck : IHealthCheck
{
    private readonly IBusControl _bus;

    public MassTransitRabbitMqHealthCheck(IBusControl bus)
    {
        _bus = bus;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken ct = default)
    {
        try
        {
            var result = _bus.CheckHealth();

            return Task.FromResult(
                result.Status switch
                {
                    BusHealthStatus.Healthy => HealthCheckResult.Healthy(),
                    BusHealthStatus.Degraded => HealthCheckResult.Degraded(),
                    _ => HealthCheckResult.Unhealthy()
                });
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy(exception: ex));
        }
    }
}