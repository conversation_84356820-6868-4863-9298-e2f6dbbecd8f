namespace HealthChecks.MassTransit.RabbitMq;

using global::MassTransit;
using global::MassTransit.RabbitMqTransport;
using global::MassTransit.RabbitMqTransport.Topology;
using Microsoft.Extensions.Diagnostics.HealthChecks;

public class MassTransitRabbitMqHealthCheck : IHealthCheck
{
    private readonly IBusControl _bus;

    public MassTransitRabbitMqHealthCheck(IBusControl bus)
    {
        _bus = bus;
    }

    public Task<HealthCheckResult> CheckHealthAsync()
    {
        try
        {
            var topology = (RabbitMqBusTopology)_bus.GetRabbitMqHostTopology();
            var status = topology.
        }
        catch (Exception ex)
        {
            return Task.FromResult(HealthCheckResult.Unhealthy(exception: ex));
        }
    }
}