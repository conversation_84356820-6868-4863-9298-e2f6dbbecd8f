root = true
; EditorConfig to support per-solution formatting.
; Use the EditorConfig VS add-in to make this work.
; http://editorconfig.org/
;
; Here are some resources for what's supported for .NET/C#
; https://kent-boogaart.com/blog/editorconfig-reference-for-c-developers
; https://learn.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference
;
; Be **careful** editing this because some of the rules don't support adding a severity level
; For instance if you change to `dotnet_sort_system_directives_first = true:warning` (adding `:warning`)
; then the rule will be silently ignored.

; This is the default for the codeline.

[*]
indent_style = space
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = false

# ReSharper properties
resharper_blank_lines_after_control_transfer_statements = 1
resharper_csharp_wrap_multiple_type_parameter_constraints_style = chop_always
resharper_keep_existing_embedded_arrangement = false
resharper_local_function_body = expression_body
resharper_max_primary_constructor_parameters_on_line = 3
resharper_method_or_operator_body = expression_body
resharper_wrap_before_primary_constructor_declaration_lpar = false
resharper_csharp_wrap_before_first_type_parameter_constraint = true
resharper_wrap_after_property_in_chained_method_calls = false
resharper_wrap_before_extends_colon = true
resharper_wrap_before_first_method_call = false
resharper_wrap_chained_method_calls = chop_if_long
resharper_blank_lines_around_single_line_auto_property = 1
resharper_blank_lines_around_single_line_local_method = 1
resharper_blank_lines_around_single_line_property = 1
resharper_csharp_blank_lines_around_single_line_invocable = 1
resharper_csharp_keep_blank_lines_in_code = 1
resharper_csharp_wrap_after_declaration_lpar = true
resharper_csharp_wrap_after_invocation_lpar = true
resharper_csharp_wrap_arguments_style = chop_if_long
resharper_csharp_wrap_extends_list_style = chop_if_long
resharper_csharp_wrap_parameters_style = chop_if_long
resharper_keep_existing_attribute_arrangement = true
resharper_keep_existing_declaration_parens_arrangement = false
resharper_keep_existing_expr_member_arrangement = false
resharper_keep_existing_linebreaks = false
resharper_space_within_single_line_array_initializer_braces = true

# ReSharper inspection severities
resharper_arrange_object_creation_when_type_not_evident_highlighting = suggestion

[*.cs]
dotnet_sort_system_directives_first = true

# Microsoft .NET properties
csharp_new_line_before_members_in_object_initializers = false
csharp_preferred_modifier_order = public, private, protected, internal, new, static, abstract, virtual, sealed, readonly, override, extern, unsafe, volatile, async:suggestion
csharp_space_after_cast = true
csharp_style_var_for_built_in_types = true:warning
csharp_style_var_elsewhere = true:warning
csharp_style_var_when_type_is_apparent = true:warning
csharp_using_directive_placement = inside_namespace:silent
dotnet_naming_rule.private_constants_rule.severity = warning
dotnet_naming_rule.private_constants_rule.style = upper_camel_case_style
dotnet_naming_rule.private_constants_rule.symbols = private_constants_symbols
dotnet_naming_rule.private_instance_fields_rule.severity = warning
dotnet_naming_rule.private_instance_fields_rule.style = lower_camel_case_style_1
dotnet_naming_rule.private_instance_fields_rule.symbols = private_instance_fields_symbols
dotnet_naming_rule.private_static_fields_override_rule.severity = warning
dotnet_naming_rule.private_static_fields_override_rule.style = upper_camel_case_style
dotnet_naming_rule.private_static_fields_override_rule.symbols = private_static_fields_override_symbols
dotnet_naming_rule.private_static_fields_rule.severity = warning
dotnet_naming_rule.private_static_fields_rule.style = lower_camel_case_style_1
dotnet_naming_rule.private_static_fields_rule.symbols = private_static_fields_symbols
dotnet_naming_rule.private_static_fields_rule_1.severity = warning
dotnet_naming_rule.private_static_fields_rule_1.style = lower_camel_case_style_1
dotnet_naming_rule.private_static_fields_rule_1.symbols = private_static_fields_symbols_1
dotnet_naming_rule.private_static_readonly_rule.severity = warning
dotnet_naming_rule.private_static_readonly_rule.style = upper_camel_case_style
dotnet_naming_rule.private_static_readonly_rule.symbols = private_static_readonly_symbols
dotnet_naming_rule.unity_serialized_field_rule.severity = warning
dotnet_naming_rule.unity_serialized_field_rule.style = lower_camel_case_style
dotnet_naming_rule.unity_serialized_field_rule.symbols = unity_serialized_field_symbols
dotnet_naming_rule.unity_serialized_field_rule_1.severity = warning
dotnet_naming_rule.unity_serialized_field_rule_1.style = lower_camel_case_style
dotnet_naming_rule.unity_serialized_field_rule_1.symbols = unity_serialized_field_symbols_1
dotnet_naming_rule.unity_serialized_field_rule_2.severity = warning
dotnet_naming_rule.unity_serialized_field_rule_2.style = lower_camel_case_style
dotnet_naming_rule.unity_serialized_field_rule_2.symbols = unity_serialized_field_symbols_2
dotnet_naming_rule.unity_serialized_field_rule_3.severity = warning
dotnet_naming_rule.unity_serialized_field_rule_3.style = lower_camel_case_style
dotnet_naming_rule.unity_serialized_field_rule_3.symbols = unity_serialized_field_symbols_3
dotnet_naming_style.lower_camel_case_style.capitalization = camel_case
dotnet_naming_style.lower_camel_case_style_1.capitalization = camel_case
dotnet_naming_style.lower_camel_case_style_1.required_prefix = _
dotnet_naming_style.upper_camel_case_style.capitalization = pascal_case
dotnet_naming_symbols.private_constants_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_constants_symbols.applicable_kinds = field
dotnet_naming_symbols.private_constants_symbols.required_modifiers = const
dotnet_naming_symbols.private_instance_fields_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_instance_fields_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_override_symbols.applicable_accessibilities = local, private
dotnet_naming_symbols.private_static_fields_override_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_override_symbols.required_modifiers = const, static
dotnet_naming_symbols.private_static_fields_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_static_fields_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_symbols.required_modifiers = static
dotnet_naming_symbols.private_static_fields_symbols_1.applicable_accessibilities = local, private
dotnet_naming_symbols.private_static_fields_symbols_1.applicable_kinds = field
dotnet_naming_symbols.private_static_fields_symbols_1.required_modifiers = static
dotnet_naming_symbols.private_static_readonly_symbols.applicable_accessibilities = private
dotnet_naming_symbols.private_static_readonly_symbols.applicable_kinds = field
dotnet_naming_symbols.private_static_readonly_symbols.required_modifiers = static, readonly
dotnet_naming_symbols.unity_serialized_field_symbols.applicable_accessibilities = *
dotnet_naming_symbols.unity_serialized_field_symbols.applicable_kinds =
dotnet_naming_symbols.unity_serialized_field_symbols_1.applicable_accessibilities = *
dotnet_naming_symbols.unity_serialized_field_symbols_1.applicable_kinds =
dotnet_naming_symbols.unity_serialized_field_symbols_2.applicable_accessibilities = *
dotnet_naming_symbols.unity_serialized_field_symbols_2.applicable_kinds =
dotnet_naming_symbols.unity_serialized_field_symbols_3.applicable_accessibilities = *
dotnet_naming_symbols.unity_serialized_field_symbols_3.applicable_kinds =
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:none
dotnet_style_parentheses_in_other_binary_operators = always_for_clarity:none
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:none
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion
dotnet_style_predefined_type_for_member_access = true:suggestion
dotnet_style_qualification_for_event = false
dotnet_style_qualification_for_field = false
dotnet_style_qualification_for_method = false
dotnet_style_qualification_for_property = false
dotnet_style_require_accessibility_modifiers = for_non_interface_members:suggestion

[*.{appxmanifest,asax,ascx,aspx,axaml,build,c,c++,cc,cginc,compute,cp,cpp,cs,cshtml,cu,cuh,cxx,dtd,fx,fxh,h,hh,hlsl,hlsli,hlslinc,hpp,hxx,inc,inl,ino,ipp,master,mpp,mq4,mq5,mqh,nuspec,paml,razor,resw,resx,shader,skin,tpp,usf,ush,vb}]
indent_size = 4
tab_width = 4

[{*.har,*.inputactions,*.jsb2,*.jsb3,*.json,.babelrc,.eslintrc,.stylelintrc,bowerrc,jest.config, csproj,xml,xaml,xamlx,xoml,xsd}]
indent_size = 2

[{*.yaml,*.yml}]
indent_size = 2
