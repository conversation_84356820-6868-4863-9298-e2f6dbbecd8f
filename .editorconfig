root = true

[*]
charset = utf-8
indent_style = space
indent_size = 2
trim_trailing_whitespace = true
insert_final_newline = false
end_of_line = lf

[src/NATS.Client.Core/NaCl/**.cs]
generated_code = true

[*.cs]
indent_size = 4
max_line_length = 300

# changes from VS2019 defaults
csharp_style_namespace_declarations = file_scoped

# changes from VS2017 defaults
csharp_style_expression_bodied_methods = false:none
csharp_style_expression_bodied_constructors = false:none
csharp_style_expression_bodied_operators = false:none
csharp_prefer_braces = false:none
csharp_indent_switch_labels = false
csharp_space_after_cast = true
csharp_preserve_single_line_statements = false

# use VS2017 defaults, but make them warnings (instead of none)
dotnet_style_qualification_for_field = false:warning
dotnet_style_qualification_for_property = false:warning
dotnet_style_qualification_for_method = false:warning
dotnet_style_qualification_for_event = false:warning
dotnet_style_predefined_type_for_locals_parameters_members = true:warning
dotnet_style_explicit_tuple_names = true:warning
dotnet_style_coalesce_expression = true:warning
csharp_style_var_for_built_in_types = true:warning
csharp_style_var_when_type_is_apparent = true:warning
csharp_style_var_elsewhere = true:warning

# use VS2017 defaults, but make them suggestions (instead of none)
csharp_style_expression_bodied_properties = true:suggestion
csharp_style_expression_bodied_indexers = true:suggestion
csharp_style_expression_bodied_accessors = true:suggestion

# explicitly use VS2017 defaults
dotnet_style_object_initializer = true
dotnet_style_collection_initializer = true
dotnet_style_null_propagation = true
csharp_style_pattern_matching_over_is_with_cast_check = true
csharp_style_pattern_matching_over_as_with_null_check = true
csharp_style_inlined_variable_declaration = true
csharp_prefer_simple_default_expression = true
csharp_style_throw_expression = true
csharp_style_conditional_delegate_call = true
dotnet_sort_system_directives_first = true
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_within_query_expression_clauses = true
csharp_indent_case_contents = true
csharp_indent_labels = one_less_than_current
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_preserve_single_line_blocks = true

# Spacing Rules
dotnet_diagnostic.SA1000.severity = warning # Keywords should be spaced correctly
dotnet_diagnostic.SA1001.severity = warning # Commas should be spaced correctly
dotnet_diagnostic.SA1002.severity = warning # Semicolons should be spaced correctly
dotnet_diagnostic.SA1003.severity = warning # Symbols should be spaced correctly
dotnet_diagnostic.SA1004.severity = warning # Documentation lines should begin with single space
dotnet_diagnostic.SA1005.severity = warning # Single line comments should begin with single space
dotnet_diagnostic.SA1006.severity = warning # Preprocessor keywords should not be preceded by space
dotnet_diagnostic.SA1007.severity = warning # Operator keyword should be followed by space
dotnet_diagnostic.SA1008.severity = warning # Opening parenthesis should be spaced correctly
dotnet_diagnostic.SA1009.severity = warning # Closing parenthesis should be spaced correctly
dotnet_diagnostic.SA1010.severity = warning # Opening square brackets should be spaced correctly
dotnet_diagnostic.SA1011.severity = warning # Closing square brackets should be spaced correctly
dotnet_diagnostic.SA1012.severity = warning # Opening braces should be spaced correctly
dotnet_diagnostic.SA1013.severity = warning # Closing braces should be spaced correctly
dotnet_diagnostic.SA1014.severity = warning # Opening generic brackets should be spaced correctly
dotnet_diagnostic.SA1015.severity = warning # Closing generic brackets should be spaced correctly
dotnet_diagnostic.SA1016.severity = warning # Opening attribute brackets should be spaced correctly
dotnet_diagnostic.SA1017.severity = warning # Closing attribute brackets should be spaced correctly
dotnet_diagnostic.SA1018.severity = warning # Nullable type symbols should be spaced correctly
dotnet_diagnostic.SA1019.severity = warning # Member access symbols should be spaced correctly
dotnet_diagnostic.SA1020.severity = warning # Increment decrement symbols should be spaced correctly
dotnet_diagnostic.SA1021.severity = warning # Negative signs should be spaced correctly
dotnet_diagnostic.SA1022.severity = warning # Positive signs should be spaced correctly
dotnet_diagnostic.SA1023.severity = warning # Dereference and access of symbols should be spaced correctly
dotnet_diagnostic.SA1024.severity = warning # Colons should be spaced correctly
dotnet_diagnostic.SA1025.severity = warning # Code should not contain multiple whitespace in a row
dotnet_diagnostic.SA1026.severity = warning # Code should not contain space after new or stackalloc keyword in implicitly typed array allocation
dotnet_diagnostic.SA1027.severity = warning # Use tabs correctly
dotnet_diagnostic.SA1028.severity = warning # Code should not contain trailing whitespace

# Readability Rules
dotnet_diagnostic.SA1100.severity = warning # Do not prefix calls with base unless local implementation exists
dotnet_diagnostic.SA1101.severity = none # Prefix local calls with this
dotnet_diagnostic.SA1102.severity = warning # Query clause should follow previous clause
dotnet_diagnostic.SA1103.severity = warning # Query clauses should be on separate lines or all on one line
dotnet_diagnostic.SA1104.severity = warning # Query clause should begin on new line when previous clause spans multiple lines
dotnet_diagnostic.SA1105.severity = warning # Query clauses spanning multiple lines should begin on own line
dotnet_diagnostic.SA1106.severity = warning # Code should not contain empty statements
dotnet_diagnostic.SA1107.severity = warning # Code should not contain multiple statements on one line
dotnet_diagnostic.SA1108.severity = warning # Block statements should not contain embedded comments
dotnet_diagnostic.SA1109.severity = none # Block statements should not contain embedded regions
dotnet_diagnostic.SA1110.severity = warning # Opening parenthesis or bracket should be on declaration line
dotnet_diagnostic.SA1111.severity = warning # Closing parenthesis should be on line of last parameter
dotnet_diagnostic.SA1112.severity = warning # Closing parenthesis should be on line of opening parenthesis
dotnet_diagnostic.SA1113.severity = warning # Comma should be on the same line as previous parameter
dotnet_diagnostic.SA1114.severity = warning # Parameter list should follow declaration
dotnet_diagnostic.SA1115.severity = warning # Parameter should follow comma
dotnet_diagnostic.SA1116.severity = warning # Split parameters should start on line after declaration
dotnet_diagnostic.SA1117.severity = warning # Parameters should be on same line or separate lines
dotnet_diagnostic.SA1118.severity = warning # Parameter should not span multiple lines
dotnet_diagnostic.SA1120.severity = warning # Comments should contain text
dotnet_diagnostic.SA1121.severity = warning # Use built-in type alias
dotnet_diagnostic.SA1122.severity = warning # Use string.Empty for empty strings
dotnet_diagnostic.SA1123.severity = warning # Do not place regions within elements
dotnet_diagnostic.SA1124.severity = warning # Do not use regions
dotnet_diagnostic.SA1125.severity = warning # Use shorthand for nullable types
dotnet_diagnostic.SA1126.severity = none # Prefix calls correctly
dotnet_diagnostic.SA1127.severity = warning # Generic type constraints should be on their own line
dotnet_diagnostic.SA1128.severity = warning # Put constructor initializers on their own line
dotnet_diagnostic.SA1129.severity = warning # Do not use default value type constructor
dotnet_diagnostic.SA1130.severity = warning # Use lambda syntax
dotnet_diagnostic.SA1131.severity = warning # Use readable conditions
dotnet_diagnostic.SA1132.severity = warning # Do not combine fields
dotnet_diagnostic.SA1133.severity = warning # Do not combine attributes
dotnet_diagnostic.SA1134.severity = warning # Attributes should not share line
dotnet_diagnostic.SA1135.severity = warning # Using directives should be qualified
dotnet_diagnostic.SA1136.severity = warning # Enum values should be on separate lines
dotnet_diagnostic.SA1137.severity = warning # Elements should have the same indentation
dotnet_diagnostic.SA1139.severity = warning # Use literal suffix notation instead of casting
dotnet_diagnostic.SX1101.severity = warning # Do not prefix local calls with 'this.'

# Ordering Rules
dotnet_diagnostic.SA1200.severity = none # Using directives should be placed correctly
dotnet_diagnostic.SA1201.severity = warning # Elements should appear in the correct order
dotnet_diagnostic.SA1202.severity = warning # Elements should be ordered by access
dotnet_diagnostic.SA1203.severity = warning # Constants should appear before fields
dotnet_diagnostic.SA1204.severity = warning # Static elements should appear before instance elements
dotnet_diagnostic.SA1205.severity = warning # Partial elements should declare access
dotnet_diagnostic.SA1206.severity = warning # Declaration keywords should follow order
dotnet_diagnostic.SA1207.severity = warning # Protected should come before internal
dotnet_diagnostic.SA1208.severity = warning # System using directives should be placed before other using directives
dotnet_diagnostic.SA1209.severity = warning # Using alias directives should be placed after other using directives
dotnet_diagnostic.SA1210.severity = warning # Using directives should be ordered alphabetically by namespace
dotnet_diagnostic.SA1211.severity = warning # Using alias directives should be ordered alphabetically by alias name
dotnet_diagnostic.SA1212.severity = warning # Property accessors should follow order
dotnet_diagnostic.SA1213.severity = warning # Event accessors should follow order
dotnet_diagnostic.SA1214.severity = warning # Readonly fields should appear before non-readonly fields
dotnet_diagnostic.SA1216.severity = warning # Using static directives should be placed at the correct location
dotnet_diagnostic.SA1217.severity = warning # Using static directives should be ordered alphabetically

# Naming Rules
dotnet_diagnostic.SA1300.severity = warning # Element should begin with upper-case letter
dotnet_diagnostic.SA1301.severity = none # Element should begin with lower-case letter
dotnet_diagnostic.SA1302.severity = warning # Interface names should begin with I
dotnet_diagnostic.SA1303.severity = warning # Const field names should begin with upper-case letter
dotnet_diagnostic.SA1304.severity = warning # Non-private readonly fields should begin with upper-case letter
dotnet_diagnostic.SA1305.severity = none # Field names should not use Hungarian notation
dotnet_diagnostic.SA1306.severity = none # Field names should begin with lower-case letter
dotnet_diagnostic.SA1307.severity = warning # Accessible fields should begin with upper-case letter
dotnet_diagnostic.SA1308.severity = warning # Variable names should not be prefixed
dotnet_diagnostic.SA1309.severity = none # Field names should not begin with underscore
dotnet_diagnostic.SA1310.severity = warning # Field names should not contain underscore
dotnet_diagnostic.SA1311.severity = warning # Static readonly fields should begin with upper-case letter
dotnet_diagnostic.SA1312.severity = warning # Variable names should begin with lower-case letter
dotnet_diagnostic.SA1313.severity = warning # Parameter names should begin with lower-case letter
dotnet_diagnostic.SA1314.severity = warning # Type parameter names should begin with T
dotnet_diagnostic.SA1316.severity = none # Tuple element names should use correct casing
dotnet_diagnostic.SX1309.severity = warning # Field names should begin with underscore
dotnet_diagnostic.SX1309S.severity = none # Static field names should begin with underscore

# Maintainability Rules
dotnet_diagnostic.SA1119.severity = warning # Statement should not use unnecessary parenthesis
dotnet_diagnostic.SA1400.severity = warning # Access modifier should be declared
dotnet_diagnostic.SA1401.severity = warning # Fields should be private
dotnet_diagnostic.SA1402.severity = none # File may only contain a single type
dotnet_diagnostic.SA1403.severity = warning # File may only contain a single namespace
dotnet_diagnostic.SA1404.severity = warning # Code analysis suppression should have justification
dotnet_diagnostic.SA1405.severity = warning # Debug.Assert should provide message text
dotnet_diagnostic.SA1406.severity = warning # Debug.Fail should provide message text
dotnet_diagnostic.SA1407.severity = warning # Arithmetic expressions should declare precedence
dotnet_diagnostic.SA1408.severity = warning # Conditional expressions should declare precedence
dotnet_diagnostic.SA1409.severity = none # Remove unnecessary code
dotnet_diagnostic.SA1410.severity = warning # Remove delegate parenthesis when possible
dotnet_diagnostic.SA1411.severity = warning # Attribute constructor should not use unnecessary parenthesis
dotnet_diagnostic.SA1412.severity = none # Store files as UTF-8 with byte order mark
dotnet_diagnostic.SA1413.severity = warning # Use trailing comma in multi-line initializers
dotnet_diagnostic.SA1414.severity = none # Tuple types in signatures should have element names

# Layout Rules
dotnet_diagnostic.SA1500.severity = warning # Braces for multi-line statements should not share line
dotnet_diagnostic.SA1501.severity = warning # Statement should not be on a single line
dotnet_diagnostic.SA1502.severity = warning # Element should not be on a single line
dotnet_diagnostic.SA1503.severity = none # Braces should not be omitted
dotnet_diagnostic.SA1504.severity = warning # All accessors should be single-line or multi-line
dotnet_diagnostic.SA1505.severity = warning # Opening braces should not be followed by blank line
dotnet_diagnostic.SA1506.severity = warning # Element documentation headers should not be followed by blank line
dotnet_diagnostic.SA1507.severity = warning # Code should not contain multiple blank lines in a row
dotnet_diagnostic.SA1508.severity = warning # Closing braces should not be preceded by blank line
dotnet_diagnostic.SA1509.severity = warning # Opening braces should not be preceded by blank line
dotnet_diagnostic.SA1510.severity = warning # Chained statement blocks should not be preceded by blank line
dotnet_diagnostic.SA1511.severity = warning # While-do footer should not be preceded by blank line
dotnet_diagnostic.SA1512.severity = warning # Single-line comments should not be followed by blank line
dotnet_diagnostic.SA1513.severity = warning # Closing brace should be followed by blank line
dotnet_diagnostic.SA1514.severity = warning # Element documentation header should be preceded by blank line
dotnet_diagnostic.SA1515.severity = warning # Single-line comment should be preceded by blank line
dotnet_diagnostic.SA1516.severity = warning # Elements should be separated by blank line
dotnet_diagnostic.SA1517.severity = warning # Code should not contain blank lines at start of file
dotnet_diagnostic.SA1518.severity = warning # Use line endings correctly at end of file
dotnet_diagnostic.SA1519.severity = warning # Braces should not be omitted from multi-line child statement
dotnet_diagnostic.SA1520.severity = warning # Use braces consistently

# Documentation Rules
dotnet_diagnostic.SA1600.severity = none # Elements should be documented
dotnet_diagnostic.SA1601.severity = none # Partial elements should be documented
dotnet_diagnostic.SA1602.severity = none # Enumeration items should be documented
dotnet_diagnostic.SA1603.severity = none # Documentation should contain valid XML
dotnet_diagnostic.SA1604.severity = none # Element documentation should have summary
dotnet_diagnostic.SA1605.severity = none # Partial element documentation should have summary
dotnet_diagnostic.SA1606.severity = none # Element documentation should have summary text
dotnet_diagnostic.SA1607.severity = none # Partial element documentation should have summary text
dotnet_diagnostic.SA1608.severity = none # Element documentation should not have default summary
dotnet_diagnostic.SA1609.severity = none # Property documentation should have value
dotnet_diagnostic.SA1610.severity = none # Property documentation should have value text
dotnet_diagnostic.SA1611.severity = none # Element parameters should be documented
dotnet_diagnostic.SA1612.severity = none # Element parameter documentation should match element parameters
dotnet_diagnostic.SA1613.severity = none # Element parameter documentation should declare parameter name
dotnet_diagnostic.SA1614.severity = none # Element parameter documentation should have text
dotnet_diagnostic.SA1615.severity = none # Element return value should be documented
dotnet_diagnostic.SA1616.severity = none # Element return value documentation should have text
dotnet_diagnostic.SA1617.severity = none # Void return value should not be documented
dotnet_diagnostic.SA1618.severity = none # Generic type parameters should be documented
dotnet_diagnostic.SA1619.severity = none # Generic type parameters should be documented partial class
dotnet_diagnostic.SA1620.severity = none # Generic type parameter documentation should match type parameters
dotnet_diagnostic.SA1621.severity = none # Generic type parameter documentation should declare parameter name
dotnet_diagnostic.SA1622.severity = none # Generic type parameter documentation should have text
dotnet_diagnostic.SA1623.severity = none # Property summary documentation should match accessors
dotnet_diagnostic.SA1624.severity = none # Property summary documentation should omit accessor with restricted access
dotnet_diagnostic.SA1625.severity = none # Element documentation should not be copied and pasted
dotnet_diagnostic.SA1626.severity = none # Single-line comments should not use documentation style slashes
dotnet_diagnostic.SA1627.severity = none # Documentation text should not be empty
dotnet_diagnostic.SA1628.severity = none # Documentation text should begin with a capital letter
dotnet_diagnostic.SA1629.severity = none # Documentation text should end with a period
dotnet_diagnostic.SA1630.severity = none # Documentation text should contain whitespace
dotnet_diagnostic.SA1631.severity = none # Documentation should meet character percentage
dotnet_diagnostic.SA1632.severity = none # Documentation text should meet minimum character length
dotnet_diagnostic.SA1633.severity = none # File should have header
dotnet_diagnostic.SA1634.severity = none # File header should show copyright
dotnet_diagnostic.SA1635.severity = none # File header should have copyright text
dotnet_diagnostic.SA1636.severity = none # File header copyright text should match
dotnet_diagnostic.SA1637.severity = none # File header should contain file name
dotnet_diagnostic.SA1638.severity = none # File header file name documentation should match file name
dotnet_diagnostic.SA1639.severity = none # File header should have summary
dotnet_diagnostic.SA1640.severity = none # File header should have valid company text
dotnet_diagnostic.SA1641.severity = none # File header company name text should match
dotnet_diagnostic.SA1642.severity = none # Constructor summary documentation should begin with standard text
dotnet_diagnostic.SA1643.severity = none # Destructor summary documentation should begin with standard text
dotnet_diagnostic.SA1644.severity = none # Documentation headers should not contain blank lines
dotnet_diagnostic.SA1645.severity = none # Included documentation file does not exist
dotnet_diagnostic.SA1646.severity = none # Included documentation XPath does not exist
dotnet_diagnostic.SA1647.severity = none # Include node does not contain valid file and path
dotnet_diagnostic.SA1648.severity = none # Inheritdoc should be used with inheriting class
dotnet_diagnostic.SA1649.severity = none # File name should match first type name
dotnet_diagnostic.SA1650.severity = none # Element documentation should be spelled correctly
dotnet_diagnostic.SA1651.severity = none # Do not use placeholder elements

# VS Threading Analyzers
dotnet_diagnostic.VSTHRD100.severity = none # Avoid async void
dotnet_diagnostic.VSTHRD101.severity = none # Avoid unsupported async delegates
dotnet_diagnostic.VSTHRD003.severity = none # Avoid awaiting foreign Tasks
dotnet_diagnostic.VSTHRD111.severity = error # Use ConfigureAwait(bool)
