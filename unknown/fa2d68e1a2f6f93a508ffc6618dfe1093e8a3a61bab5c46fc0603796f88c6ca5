namespace Kukui.Infrastructure.Authentication.Keycloak;

using System.Text.Json.Serialization;

public class AccessTokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; }

    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    public DateTime ExpirationDate => DateTime.Now.AddSeconds(ExpiresIn);
}